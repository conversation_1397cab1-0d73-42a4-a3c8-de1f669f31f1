"use client";

import { motion } from "motion/react";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  MousePointer, 
  UserPlus, 
  MessageCircle, 
  ArrowRight,
  Clock,
  Shield,
  Sparkles
} from "lucide-react";

const steps = [
  {
    number: "01",
    icon: Mouse<PERSON>ointer,
    title: "One-Click Creation",
    description: "Click 'Create Room' and instantly generate a secure, anonymous discussion space. No registration, no personal information required.",
    features: ["Instant generation", "No sign-up needed", "Secure by default"],
    color: "primary"
  },
  {
    number: "02",
    icon: UserPlus,
    title: "Anonymous Entry",
    description: "Share the room link with participants. Everyone joins with AI-generated anonymous identities and voice modulation.",
    features: ["Anonymous identities", "Voice modulation", "Secure sharing"],
    color: "cyan-500"
  },
  {
    number: "03",
    icon: MessageCircle,
    title: "AI-Enhanced Discussion",
    description: "Engage in meaningful conversations with real-time AI moderation, smart facilitation, and privacy protection.",
    features: ["AI moderation", "Smart facilitation", "Real-time insights"],
    color: "green-500"
  }
];

export function HowItWorks() {
  return (
    <section className="py-16 sm:py-20 lg:py-24 px-4 sm:px-6 lg:px-8 relative bg-gradient-to-b from-background to-primary/5">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          className="text-center mb-12 sm:mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="mb-4 border-chart-2/30 text-chart-2 text-xs sm:text-sm">
            <Clock className="w-3 h-3 mr-1" />
            Simple Process
          </Badge>
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 px-2">
            How <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">VeilMeet</span> Works
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed px-2">
            Get started in under 30 seconds. Three simple steps to anonymous, AI-enhanced conversations
            that protect your identity while fostering meaningful dialogue.
          </p>
        </motion.div>

        {/* Steps */}
        <div className="space-y-8 sm:space-y-12 lg:space-y-0 lg:grid lg:grid-cols-3 lg:gap-6 xl:gap-8">
          {steps.map((step, index) => (
            <motion.div
              key={step.number}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              viewport={{ once: true }}
              className="relative"
            >
              {/* Connection line for desktop */}
              {index < steps.length - 1 && (
                <div className="hidden lg:block absolute top-1/2 left-full w-6 xl:w-8 h-0.5 bg-gradient-to-r from-primary/50 to-transparent transform -translate-y-1/2 z-10" />
              )}

              <Card className="h-full bg-card/50 backdrop-blur-sm border-border/50 hover:border-primary/30 transition-all duration-300 group hover:shadow-lg">
                <CardContent className="p-4 sm:p-6 lg:p-8">
                  {/* Step number and icon */}
                  <div className="flex items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
                    <div className={`w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-xl sm:rounded-2xl bg-${step.color}/10 flex items-center justify-center group-hover:bg-${step.color}/20 transition-colors flex-shrink-0`}>
                      <step.icon className={`w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-${step.color}`} />
                    </div>
                    <div className={`text-4xl sm:text-5xl lg:text-6xl font-bold text-${step.color}/20 group-hover:text-${step.color}/30 transition-colors`}>
                      {step.number}
                    </div>
                  </div>

                  {/* Content */}
                  <h3 className="text-lg sm:text-xl lg:text-2xl font-semibold mb-3 sm:mb-4 group-hover:text-primary transition-colors leading-tight">
                    {step.title}
                  </h3>

                  <p className="text-sm sm:text-base text-muted-foreground mb-4 sm:mb-6 leading-relaxed">
                    {step.description}
                  </p>

                  {/* Features list */}
                  <ul className="space-y-2">
                    {step.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center gap-2 text-xs sm:text-sm text-muted-foreground">
                        <div className={`w-1.5 h-1.5 rounded-full bg-${step.color} flex-shrink-0`} />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-12 sm:mt-16 px-2"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="bg-gradient-to-br from-primary/20 to-primary/10 rounded-2xl p-4 sm:p-6 lg:p-8 border border-primary/20">
            <div className="flex flex-col sm:flex-row items-center justify-center gap-2 sm:gap-4 mb-4">
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4 sm:w-5 sm:h-5 text-green-500" />
                <span className="text-green-500 font-medium text-sm sm:text-base">100% Anonymous</span>
              </div>
              <div className="flex items-center gap-2">
                <Sparkles className="w-4 h-4 sm:w-5 sm:h-5 text-primary" />
                <span className="text-primary font-medium text-sm sm:text-base">AI-Enhanced</span>
              </div>
            </div>

            <h3 className="text-xl sm:text-2xl font-semibold mb-4 px-2">
              Ready to speak your truth?
            </h3>

            <p className="text-sm sm:text-base text-muted-foreground mb-6 max-w-2xl mx-auto leading-relaxed px-2">
              Join thousands who have discovered the freedom of anonymous, AI-moderated conversations.
              Your voice matters, your identity stays protected.
            </p>

            <Button
              size="lg"
              className="w-full sm:w-auto bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-primary-foreground px-6 sm:px-8 py-4 sm:py-6 text-base sm:text-lg font-semibold shadow-lg transition-all duration-300 group min-h-[48px]"
            >
              <span className="whitespace-nowrap">Start Your First Anonymous Room</span>
              <ArrowRight className="ml-2 h-4 w-4 sm:h-5 sm:w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
