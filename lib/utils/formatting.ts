// Formatting utilities for dates, numbers, text, and other data types
// Production-ready formatting functions with internationalization support

// ============================================================================
// DATE AND TIME FORMATTING
// ============================================================================

/**
 * Format relative time (e.g., "2 minutes ago", "in 3 hours")
 */
export function formatRelativeTime(
  timestamp: number,
  locale = 'en-US',
  options: Intl.RelativeTimeFormatOptions = {}
): string {
  const now = Date.now();
  const diff = timestamp - now;
  const absDiff = Math.abs(diff);

  const rtf = new Intl.RelativeTimeFormat(locale, {
    numeric: 'auto',
    style: 'long',
    ...options,
  });

  // Define time units in milliseconds
  const units: Array<[Intl.RelativeTimeFormatUnit, number]> = [
    ['year', 365 * 24 * 60 * 60 * 1000],
    ['month', 30 * 24 * 60 * 60 * 1000],
    ['week', 7 * 24 * 60 * 60 * 1000],
    ['day', 24 * 60 * 60 * 1000],
    ['hour', 60 * 60 * 1000],
    ['minute', 60 * 1000],
    ['second', 1000],
  ];

  for (const [unit, ms] of units) {
    if (absDiff >= ms) {
      const value = Math.round(diff / ms);
      return rtf.format(value, unit);
    }
  }

  return rtf.format(0, 'second');
}

/**
 * Format absolute time with various formats
 */
export function formatAbsoluteTime(
  timestamp: number,
  format: 'short' | 'medium' | 'long' | 'full' = 'medium',
  locale = 'en-US'
): string {
  const date = new Date(timestamp);
  
  const formatOptions: Record<string, Intl.DateTimeFormatOptions> = {
    short: { 
      month: 'short', 
      day: 'numeric', 
      hour: 'numeric', 
      minute: '2-digit' 
    },
    medium: { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric', 
      hour: 'numeric', 
      minute: '2-digit' 
    },
    long: { 
      weekday: 'long', 
      month: 'long', 
      day: 'numeric', 
      year: 'numeric', 
      hour: 'numeric', 
      minute: '2-digit' 
    },
    full: { 
      weekday: 'long', 
      month: 'long', 
      day: 'numeric', 
      year: 'numeric', 
      hour: 'numeric', 
      minute: '2-digit', 
      second: '2-digit',
      timeZoneName: 'short' 
    },
  };

  return new Intl.DateTimeFormat(locale, formatOptions[format]).format(date);
}

/**
 * Format duration (e.g., "2h 30m", "45s")
 */
export function formatDuration(
  milliseconds: number,
  options: {
    format?: 'short' | 'long';
    precision?: 'seconds' | 'minutes' | 'hours';
    maxUnits?: number;
  } = {}
): string {
  const { format = 'short', precision = 'seconds', maxUnits = 2 } = options;
  
  const units = [
    { name: 'day', short: 'd', ms: 24 * 60 * 60 * 1000 },
    { name: 'hour', short: 'h', ms: 60 * 60 * 1000 },
    { name: 'minute', short: 'm', ms: 60 * 1000 },
    { name: 'second', short: 's', ms: 1000 },
  ];

  // Filter units based on precision
  const precisionIndex = units.findIndex(u => u.name === precision);
  const filteredUnits = units.slice(0, precisionIndex + 1);

  const parts: string[] = [];
  let remaining = Math.abs(milliseconds);

  for (const unit of filteredUnits) {
    if (parts.length >= maxUnits) break;
    
    const value = Math.floor(remaining / unit.ms);
    if (value > 0) {
      const label = format === 'short' 
        ? unit.short 
        : value === 1 ? unit.name : `${unit.name}s`;
      
      parts.push(`${value}${format === 'short' ? '' : ' '}${label}`);
      remaining -= value * unit.ms;
    }
  }

  return parts.length > 0 ? parts.join(' ') : `0${format === 'short' ? 's' : ' seconds'}`;
}

// ============================================================================
// NUMBER FORMATTING
// ============================================================================

/**
 * Format numbers with locale-specific formatting
 */
export function formatNumber(
  value: number,
  options: Intl.NumberFormatOptions & { locale?: string } = {}
): string {
  const { locale = 'en-US', ...formatOptions } = options;
  return new Intl.NumberFormat(locale, formatOptions).format(value);
}

/**
 * Format file sizes (e.g., "1.2 MB", "345 KB")
 */
export function formatFileSize(
  bytes: number,
  options: {
    binary?: boolean;
    precision?: number;
    locale?: string;
  } = {}
): string {
  const { binary = false, precision = 1, locale = 'en-US' } = options;
  
  const base = binary ? 1024 : 1000;
  const units = binary 
    ? ['B', 'KiB', 'MiB', 'GiB', 'TiB', 'PiB']
    : ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

  if (bytes === 0) return '0 B';

  const exponent = Math.floor(Math.log(Math.abs(bytes)) / Math.log(base));
  const value = bytes / Math.pow(base, exponent);
  const unit = units[Math.min(exponent, units.length - 1)];

  return `${formatNumber(value, { 
    minimumFractionDigits: 0,
    maximumFractionDigits: precision,
    locale 
  })} ${unit}`;
}

/**
 * Format percentages
 */
export function formatPercentage(
  value: number,
  options: {
    precision?: number;
    locale?: string;
    showSign?: boolean;
  } = {}
): string {
  const { precision = 1, locale = 'en-US', showSign = false } = options;
  
  const formatted = new Intl.NumberFormat(locale, {
    style: 'percent',
    minimumFractionDigits: 0,
    maximumFractionDigits: precision,
    signDisplay: showSign ? 'always' : 'auto',
  }).format(value);

  return formatted;
}

// ============================================================================
// TEXT FORMATTING
// ============================================================================

/**
 * Truncate text with ellipsis
 */
export function truncateText(
  text: string,
  maxLength: number,
  options: {
    ellipsis?: string;
    wordBoundary?: boolean;
  } = {}
): string {
  const { ellipsis = '...', wordBoundary = true } = options;
  
  if (text.length <= maxLength) return text;

  let truncated = text.slice(0, maxLength - ellipsis.length);
  
  if (wordBoundary) {
    const lastSpace = truncated.lastIndexOf(' ');
    if (lastSpace > 0) {
      truncated = truncated.slice(0, lastSpace);
    }
  }

  return truncated + ellipsis;
}

/**
 * Capitalize text with various strategies
 */
export function capitalize(
  text: string,
  strategy: 'first' | 'words' | 'sentences' = 'first'
): string {
  if (!text) return text;

  switch (strategy) {
    case 'first':
      return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
    
    case 'words':
      return text.replace(/\b\w/g, char => char.toUpperCase());
    
    case 'sentences':
      return text.replace(/(^|\. )\w/g, char => char.toUpperCase());
    
    default:
      return text;
  }
}

/**
 * Convert text to slug format
 */
export function slugify(
  text: string,
  options: {
    separator?: string;
    lowercase?: boolean;
    maxLength?: number;
  } = {}
): string {
  const { separator = '-', lowercase = true, maxLength } = options;
  
  let slug = text
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .trim()
    .replace(/\s+/g, separator) // Replace spaces with separator
    .replace(new RegExp(`${separator}+`, 'g'), separator); // Remove duplicate separators

  if (lowercase) {
    slug = slug.toLowerCase();
  }

  if (maxLength && slug.length > maxLength) {
    slug = slug.slice(0, maxLength).replace(new RegExp(`${separator}[^${separator}]*$`), '');
  }

  return slug;
}

/**
 * Highlight search terms in text
 */
export function highlightText(
  text: string,
  searchTerm: string,
  options: {
    caseSensitive?: boolean;
    className?: string;
    tag?: string;
  } = {}
): string {
  const { caseSensitive = false, className = 'highlight', tag = 'mark' } = options;
  
  if (!searchTerm.trim()) return text;

  const flags = caseSensitive ? 'g' : 'gi';
  const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, flags);
  
  return text.replace(regex, `<${tag} class="${className}">$1</${tag}>`);
}

/**
 * Extract initials from a name
 */
export function getInitials(
  name: string,
  maxInitials = 2
): string {
  return name
    .split(/\s+/)
    .slice(0, maxInitials)
    .map(word => word.charAt(0).toUpperCase())
    .join('');
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Escape special regex characters
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Format list with proper conjunctions
 */
export function formatList(
  items: string[],
  options: {
    conjunction?: 'and' | 'or';
    locale?: string;
  } = {}
): string {
  const { conjunction = 'and', locale = 'en-US' } = options;
  
  if (items.length === 0) return '';
  if (items.length === 1) return items[0];
  if (items.length === 2) return `${items[0]} ${conjunction} ${items[1]}`;
  
  const formatter = new Intl.ListFormat(locale, { 
    style: 'long', 
    type: conjunction === 'and' ? 'conjunction' : 'disjunction' 
  });
  
  return formatter.format(items);
}

/**
 * Format currency values
 */
export function formatCurrency(
  amount: number,
  currency = 'USD',
  locale = 'en-US'
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(amount);
}
