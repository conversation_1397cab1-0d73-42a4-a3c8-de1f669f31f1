'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface TypingParticipant {
  sessionId: string;
  anonymousAlias: string;
}

interface TypingIndicatorProps {
  typingParticipants: TypingParticipant[];
  currentSessionId?: string;
  className?: string;
}

export function TypingIndicator({ 
  typingParticipants, 
  currentSessionId,
  className 
}: TypingIndicatorProps) {
  // Filter out current user from typing indicators
  const otherTypingParticipants = typingParticipants.filter(
    p => p.sessionId !== currentSessionId
  );

  if (otherTypingParticipants.length === 0) {
    return null;
  }

  const getTypingText = () => {
    const count = otherTypingParticipants.length;
    
    if (count === 1) {
      return `${otherTypingParticipants[0].anonymousAlias} is typing...`;
    } else if (count === 2) {
      return `${otherTypingParticipants[0].anonymous<PERSON>lias} and ${otherTypingParticipants[1].anonymous<PERSON>lias} are typing...`;
    } else if (count === 3) {
      return `${otherTypingParticipants[0].anonymousAlias}, ${otherTypingParticipants[1].anonymousAlias}, and ${otherTypingParticipants[2].anonymousAlias} are typing...`;
    } else {
      return `${otherTypingParticipants[0].anonymousAlias}, ${otherTypingParticipants[1].anonymousAlias}, and ${count - 2} others are typing...`;
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={{ duration: 0.2 }}
        className={cn(
          "px-4 py-2 text-sm text-muted-foreground",
          className
        )}
      >
        <div className="flex items-center gap-2">
          <span>{getTypingText()}</span>
          
          {/* Animated dots */}
          <div className="flex gap-1">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-1 h-1 bg-muted-foreground rounded-full"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: i * 0.2,
                }}
              />
            ))}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
