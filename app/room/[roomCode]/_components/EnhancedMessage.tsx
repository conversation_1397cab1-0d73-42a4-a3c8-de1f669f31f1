'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Reply,
  MoreHorizontal,
  Edit3,
  Trash2,
  Copy,
  Flag,
  Heart,
  ThumbsUp,
  Smile,
  AtSign,
  Clock,
  Check,
  CheckCheck
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatRelativeTime, generateAvatarUrl } from '@/lib/identity';
import { useViewport } from '@/hooks/useViewport';

// Import reaction components
import { QuickReactionBar, ReactionDisplay } from '@/components/room/ReactionPicker';

// Types
import type { Message, Participant } from '@/lib/types';

interface EnhancedMessageProps {
  message: Message;
  currentParticipant?: Participant;
  participants: Participant[];
  onReaction: (messageId: string, emoji: string) => void;
  onReply: (messageId: string, content: string, author: string) => void;
  onEdit?: (messageId: string, newContent: string) => void;
  onDelete?: (messageId: string) => void;
  onMentionClick?: (participantSessionId: string) => void;
  showAvatar?: boolean;
  showTimestamp?: boolean;
  isGrouped?: boolean;
  className?: string;
}

export function EnhancedMessage({
  message,
  currentParticipant,
  participants,
  onReaction,
  onReply,
  onEdit,
  onDelete,
  onMentionClick,
  showAvatar = true,
  showTimestamp = true,
  isGrouped = false,
  className
}: EnhancedMessageProps) {
  const { isMobile } = useViewport();
  const [showReactions, setShowReactions] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const isOwnMessage = message.participantSessionId === currentParticipant?.sessionId;
  const canEdit = isOwnMessage && onEdit;
  const canDelete = isOwnMessage && onDelete;

  // Find message author
  const author = participants.find(p => p.sessionId === message.participantSessionId);
  const authorName = author?.anonymousAlias || message.anonymousAlias;
  const avatarUrl = author?.avatarSeed ? generateAvatarUrl(author.avatarSeed) : generateAvatarUrl(message.participantSessionId);

  // Process message content for mentions
  const processedContent = useMemo(() => {
    if (!message.mentions || message.mentions.length === 0) {
      return message.content;
    }

    let content = message.content;
    message.mentions.forEach(mention => {
      const participant = participants.find(p => p.sessionId === mention.participantSessionId);
      if (participant) {
        content = content.replace(
          `@${mention.participantSessionId}`,
          `@${participant.anonymousAlias}`
        );
      }
    });
    return content;
  }, [message.content, message.mentions, participants]);

  // Handle reactions
  const handleReaction = useCallback((emoji: string) => {
    onReaction(message._id, emoji);
    setShowReactions(false);
  }, [onReaction, message._id]);

  const handleReply = useCallback(() => {
    onReply(message._id, message.content, authorName);
  }, [onReply, message._id, message.content, authorName]);

  const handleEdit = useCallback(() => {
    if (canEdit) {
      // This would typically open an edit modal or inline editor
      const newContent = prompt('Edit message:', message.content);
      if (newContent && newContent !== message.content) {
        onEdit(message._id, newContent);
      }
    }
  }, [canEdit, onEdit, message._id, message.content]);

  const handleDelete = useCallback(() => {
    if (canDelete && confirm('Are you sure you want to delete this message?')) {
      onDelete(message._id);
    }
  }, [canDelete, onDelete, message._id]);

  const handleCopyMessage = useCallback(() => {
    navigator.clipboard?.writeText(message.content);
  }, [message.content]);

  // Render reply reference
  const renderReplyReference = () => {
    if (!message.replyToMessageId) return null;

    // In a real implementation, you'd fetch the referenced message
    return (
      <div className="mb-2 pl-3 border-l-2 border-muted text-sm text-muted-foreground">
        <div className="flex items-center gap-1">
          <Reply className="h-3 w-3" />
          <span>Replying to a message</span>
        </div>
      </div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn(
        "group relative",
        isGrouped ? "mt-1" : "mt-4",
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className={cn(
        "flex gap-3",
        isOwnMessage ? "flex-row-reverse" : "flex-row"
      )}>
        {/* Avatar */}
        {showAvatar && !isGrouped && (
          <div className="flex-shrink-0">
            <img
              src={avatarUrl}
              alt={authorName}
              className="w-8 h-8 rounded-full"
            />
          </div>
        )}

        {/* Message Content */}
        <div className={cn(
          "flex-1 min-w-0",
          showAvatar && !isGrouped ? "" : "ml-11",
          isOwnMessage && "mr-11"
        )}>
          {/* Author & Timestamp */}
          {!isGrouped && (
            <div className={cn(
              "flex items-center gap-2 mb-1",
              isOwnMessage ? "justify-end" : "justify-start"
            )}>
              <span className="text-sm font-medium text-foreground">
                {authorName}
              </span>
              {isOwnMessage && (
                <Badge variant="outline" className="text-xs">
                  You
                </Badge>
              )}
              {showTimestamp && (
                <span className="text-xs text-muted-foreground">
                  {formatRelativeTime(message.timestamp)}
                </span>
              )}
              {message.isEdited && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Edit3 className="h-3 w-3 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs">
                        Edited {message.editedAt ? formatRelativeTime(message.editedAt) : ''}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          )}

          {/* Reply Reference */}
          {renderReplyReference()}

          {/* Message Bubble */}
          <div className={cn(
            "relative rounded-lg px-3 py-2 max-w-md break-words",
            isOwnMessage
              ? "bg-primary text-primary-foreground ml-auto"
              : "bg-muted text-foreground",
            message.hasMentions && "ring-2 ring-blue-500/20"
          )}>
            {/* Message Content */}
            <div className="text-sm whitespace-pre-wrap">
              {processedContent}
            </div>

            {/* Message Status (for own messages) */}
            {isOwnMessage && (
              <div className="flex items-center justify-end mt-1 gap-1">
                <span className="text-xs opacity-70">
                  {formatRelativeTime(message.timestamp)}
                </span>
                {/* Delivery status icons could go here */}
                <CheckCheck className="h-3 w-3 opacity-70" />
              </div>
            )}

            {/* Quick Actions (on hover) */}
            <AnimatePresence>
              {(isHovered || isMobile) && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className={cn(
                    "absolute -top-2 flex items-center gap-1 bg-background border border-border rounded-full px-1 py-0.5 shadow-md",
                    isOwnMessage ? "-left-2" : "-right-2"
                  )}
                >
                  {/* Quick Reactions */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 rounded-full"
                    onClick={() => handleReaction('👍')}
                  >
                    👍
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 rounded-full"
                    onClick={() => handleReaction('❤️')}
                  >
                    ❤️
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 rounded-full"
                    onClick={() => setShowReactions(!showReactions)}
                  >
                    <Smile className="h-3 w-3" />
                  </Button>

                  {/* Reply Button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 rounded-full"
                    onClick={handleReply}
                  >
                    <Reply className="h-3 w-3" />
                  </Button>

                  {/* More Actions */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 rounded-full"
                      >
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align={isOwnMessage ? "start" : "end"}>
                      <DropdownMenuItem onClick={handleCopyMessage}>
                        <Copy className="mr-2 h-4 w-4" />
                        Copy Message
                      </DropdownMenuItem>
                      
                      {canEdit && (
                        <DropdownMenuItem onClick={handleEdit}>
                          <Edit3 className="mr-2 h-4 w-4" />
                          Edit Message
                        </DropdownMenuItem>
                      )}
                      
                      {canDelete && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={handleDelete}
                            className="text-destructive focus:text-destructive"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Message
                          </DropdownMenuItem>
                        </>
                      )}
                      
                      {!isOwnMessage && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <Flag className="mr-2 h-4 w-4" />
                            Report Message
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Reactions */}
          {message.reactions && message.reactions.length > 0 && (
            <div className="mt-2">
              <ReactionDisplay
                reactions={message.reactions.map(r => ({
                  emoji: r.emoji,
                  count: r.count,
                  participantSessionIds: r.participantSessionIds,
                  participantDetails: r.participantSessionIds.map(sessionId => {
                    const participant = participants.find(p => p.sessionId === sessionId);
                    return {
                      sessionId,
                      anonymousAlias: participant?.anonymousAlias || 'Anonymous',
                      reactedAt: Date.now() // This would come from the reaction data
                    };
                  })
                }))}
                currentParticipantId={currentParticipant?.sessionId}
                onReactionToggle={handleReaction}
              />
            </div>
          )}

          {/* Extended Reaction Picker */}
          <AnimatePresence>
            {showReactions && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                className="mt-2"
              >
                <QuickReactionBar
                  onReactionSelect={handleReaction}
                  className="inline-flex"
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  );
}
