/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as cleanup from "../cleanup.js";
import type * as identity from "../identity.js";
import type * as livekit from "../livekit.js";
import type * as livekitActions from "../livekitActions.js";
import type * as messages from "../messages.js";
import type * as rooms from "../rooms.js";
import type * as voiceRoomManagement from "../voiceRoomManagement.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  cleanup: typeof cleanup;
  identity: typeof identity;
  livekit: typeof livekit;
  livekitActions: typeof livekitActions;
  messages: typeof messages;
  rooms: typeof rooms;
  voiceRoomManagement: typeof voiceRoomManagement;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
