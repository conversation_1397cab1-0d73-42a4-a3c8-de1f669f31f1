'use client';

import { useState, useEffect, useCallback } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { useAnonymousIdentity } from './useAnonymousIdentity';
import { storeSessionInfo, clearSessionInfo } from '@/lib/identity';

export interface RoomConfig {
  topic?: string;
  isPublic?: boolean;
  maxParticipants?: number;
  allowVoice?: boolean;
  allowVideo?: boolean;
  aiModerationLevel?: 'light' | 'moderate' | 'strict';
}

export function useRoom(roomCode?: string) {
  const { identity } = useAnonymousIdentity();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Get room by code
  const room = useQuery(
    api.rooms.getRoomByCode,
    roomCode ? { roomCode } : 'skip'
  );

  // Get room participants
  const participants = useQuery(
    api.rooms.getRoomParticipants,
    room ? { roomId: room._id } : 'skip'
  );

  // Get room messages
  const messages = useQuery(
    api.messages.subscribeToRoomMessages,
    room ? { roomId: room._id } : 'skip'
  );

  // Get typing indicators
  const typingIndicators = useQuery(
    api.identity.getTypingIndicators,
    room ? { roomId: room._id } : 'skip'
  );

  // Check if room exists and is accessible
  const isRoomValid = room && room.status === 'active' && room.expiresAt > Date.now();
  const isRoomFull = room && room.currentParticipants >= room.maxParticipants;

  // Check if current user is a participant
  const currentParticipant = participants?.find(
    p => p.sessionId === identity?.sessionId
  );
  const isParticipant = !!currentParticipant;

  return {
    room,
    participants: participants ?? [],
    messages: messages ?? [],
    typingIndicators: typingIndicators ?? [],
    currentParticipant,
    isParticipant,
    isRoomValid,
    isRoomFull,
    isLoading: isLoading || room === undefined,
    error,
  };
}

export function useRoomCreation() {
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [createdRoom, setCreatedRoom] = useState<{
    roomId: Id<'rooms'>;
    roomCode: string;
    shareUrl: string;
    voiceRoomId?: Id<'voiceRooms'> | null;
    voiceEnabled: boolean;
  } | null>(null);

  const createRoomMutation = useMutation(api.rooms.createRoom);

  const createRoom = useCallback(async (config: RoomConfig = {}) => {
    try {
      setIsCreating(true);
      setError(null);

      const result = await createRoomMutation(config);
      setCreatedRoom(result);

      // Store session info
      storeSessionInfo(result.roomCode, result.roomId);

      return result;
    } catch (err) {
      console.error('Failed to create room:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to create room';
      setError(errorMessage);
      throw err;
    } finally {
      setIsCreating(false);
    }
  }, [createRoomMutation]);

  const clearCreatedRoom = useCallback(() => {
    setCreatedRoom(null);
    setError(null);
  }, []);

  return {
    createRoom,
    createdRoom,
    isCreating,
    error,
    clearCreatedRoom,
  };
}

export function useRoomActions(roomId?: Id<'rooms'>) {
  const { identity } = useAnonymousIdentity();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const endRoomMutation = useMutation(api.rooms.endRoom);
  const leaveRoomMutation = useMutation(api.rooms.leaveRoom);

  const endRoom = useCallback(async () => {
    if (!roomId || !identity) {
      throw new Error('Room ID or identity not available');
    }

    try {
      setIsLoading(true);
      setError(null);

      await endRoomMutation({
        roomId,
        creatorSessionId: identity.sessionId,
      });

      clearSessionInfo();
    } catch (err) {
      console.error('Failed to end room:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to end room';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [roomId, identity, endRoomMutation]);

  const leaveRoom = useCallback(async () => {
    if (!roomId || !identity) {
      throw new Error('Room ID or identity not available');
    }

    try {
      setIsLoading(true);
      setError(null);

      await leaveRoomMutation({
        roomId,
        sessionId: identity.sessionId,
      });

      clearSessionInfo();
    } catch (err) {
      console.error('Failed to leave room:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to leave room';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [roomId, identity, leaveRoomMutation]);

  return {
    endRoom,
    leaveRoom,
    isLoading,
    error,
  };
}

export function useRoomDiscovery() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const activeRooms = useQuery(api.rooms.getActiveRooms);

  const refreshRooms = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      // The query will automatically refresh due to Convex's real-time nature
    } catch (err) {
      console.error('Failed to refresh rooms:', err);
      setError('Failed to refresh rooms');
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    activeRooms: activeRooms ?? [],
    isLoading: isLoading || activeRooms === undefined,
    error,
    refreshRooms,
  };
}

// Hook for managing room state and lifecycle
export function useRoomLifecycle(roomCode?: string) {
  const { identity } = useAnonymousIdentity();
  const { room, isRoomValid, isParticipant } = useRoom(roomCode);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');

  // Auto-join room when identity and room are available
  useEffect(() => {
    if (!room || !identity || isParticipant) return;

    const autoJoin = async () => {
      try {
        setConnectionStatus('connecting');
        // Auto-join logic would go here
        setConnectionStatus('connected');
      } catch (err) {
        console.error('Auto-join failed:', err);
        setConnectionStatus('disconnected');
      }
    };

    autoJoin();
  }, [room, identity, isParticipant]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (room && identity && isParticipant) {
        // Cleanup logic would go here
        clearSessionInfo();
      }
    };
  }, [room, identity, isParticipant]);

  return {
    room,
    isRoomValid,
    isParticipant,
    connectionStatus,
    canJoin: isRoomValid && !isParticipant && identity,
  };
}
