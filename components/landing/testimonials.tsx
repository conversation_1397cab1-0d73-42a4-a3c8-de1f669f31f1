"use client";

import { motion } from "motion/react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  Quote, 
  Star, 
  Building2, 
  Heart, 
  GraduationCap,
  Users,
  Sparkles
} from "lucide-react";

const testimonials = [
  {
    quote: "VeilMeet transformed our employee feedback process. For the first time, people are sharing honest concerns without fear of retaliation. The AI moderation keeps discussions productive.",
    author: "Anonymous HR Director",
    company: "Fortune 500 Company",
    industry: "Corporate",
    icon: Building2,
    color: "blue-500",
    rating: 5
  },
  {
    quote: "As a therapist, I've seen how anonymity can unlock authentic conversations. VeilMeet provides the perfect balance of safety and meaningful dialogue for support groups.",
    author: "Licensed Therapist",
    company: "Mental Health Clinic",
    industry: "Healthcare",
    icon: Heart,
    color: "green-500",
    rating: 5
  },
  {
    quote: "Our students finally feel comfortable discussing sensitive topics. The AI ensures conversations stay respectful while maintaining complete anonymity. It's revolutionary.",
    author: "University Professor",
    company: "Major University",
    industry: "Education",
    icon: GraduationCap,
    color: "primary",
    rating: 5
  },
  {
    quote: "The voice modulation is incredible - I can speak my truth without anyone recognizing me. The AI suggestions actually help guide better conversations.",
    author: "Community Organizer",
    company: "Local Government",
    industry: "Community",
    icon: Users,
    color: "cyan-500",
    rating: 5
  },
  {
    quote: "We use VeilMeet for whistleblowing and sensitive reporting. The security is bulletproof, and the AI helps people articulate concerns clearly and constructively.",
    author: "Compliance Officer",
    company: "Financial Institution",
    industry: "Finance",
    icon: Building2,
    color: "orange-500",
    rating: 5
  },
  {
    quote: "Finally, a platform where people can discuss mental health openly. The anonymity removes stigma, and the AI creates a supportive environment for healing.",
    author: "Support Group Leader",
    company: "Non-Profit Organization",
    industry: "Mental Health",
    icon: Heart,
    color: "red-500",
    rating: 5
  }
];

const stats = [
  { value: "98%", label: "User Satisfaction", description: "Report feeling safer to speak honestly" },
  { value: "10,000+", label: "Anonymous Sessions", description: "Successful conversations facilitated" },
  { value: "500+", label: "Organizations", description: "Trust VeilMeet for sensitive discussions" },
  { value: "99.9%", label: "Privacy Rating", description: "Zero privacy breaches to date" }
];

export function Testimonials() {
  return (
    <section className="py-16 sm:py-20 lg:py-24 px-4 sm:px-6 lg:px-8 relative bg-gradient-to-b from-background to-primary/5">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          className="text-center mb-12 sm:mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="mb-4 border-primary/30 text-primary text-xs sm:text-sm">
            <Sparkles className="w-3 h-3 mr-1" />
            User Stories
          </Badge>
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 px-2">
            <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">Real Impact</span>, Real Stories
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed px-2">
            Discover how VeilMeet is empowering honest conversations across industries,
            creating safe spaces where truth can finally be spoken without consequences.
          </p>
        </motion.div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mb-16 sm:mb-20">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
              className="group"
            >
              <Card className="h-full bg-card/50 backdrop-blur-sm border-border/50 hover:border-primary/30 transition-all duration-300 group-hover:shadow-lg">
                <CardContent className="p-4 sm:p-6">
                  {/* Quote icon */}
                  <Quote className="w-6 h-6 sm:w-8 sm:h-8 text-primary/30 mb-3 sm:mb-4" />

                  {/* Rating */}
                  <div className="flex items-center gap-1 mb-3 sm:mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-3 h-3 sm:w-4 sm:h-4 fill-orange-500 text-orange-500" />
                    ))}
                  </div>

                  {/* Quote */}
                  <p className="text-muted-foreground mb-4 sm:mb-6 leading-relaxed italic text-sm sm:text-base">
                    "{testimonial.quote}"
                  </p>

                  {/* Author info */}
                  <div className="flex items-center gap-2 sm:gap-3">
                    <Avatar className="w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0">
                      <AvatarFallback className={`bg-${testimonial.color}/10`}>
                        <testimonial.icon className={`w-4 h-4 sm:w-5 sm:h-5 text-${testimonial.color}`} />
                      </AvatarFallback>
                    </Avatar>
                    <div className="min-w-0 flex-1">
                      <div className="font-semibold text-xs sm:text-sm truncate">{testimonial.author}</div>
                      <div className="text-xs text-muted-foreground truncate">{testimonial.company}</div>
                      <Badge variant="secondary" className="text-xs mt-1">
                        {testimonial.industry}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Stats Section */}
        <motion.div
          className="bg-gradient-to-br from-primary/20 to-primary/10 rounded-3xl p-8 lg:p-12 border border-primary/20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold mb-4">
              Trusted by <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">Thousands</span>
            </h3>
            <p className="text-muted-foreground text-lg">
              The numbers speak for themselves - VeilMeet is transforming how people communicate
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent mb-2">
                  {stat.value}
                </div>
                <div className="font-semibold mb-1">
                  {stat.label}
                </div>
                <div className="text-sm text-muted-foreground">
                  {stat.description}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Bottom message */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center gap-2 text-muted-foreground">
            <Quote className="w-4 h-4" />
            <span>All testimonials are from real users who chose to remain anonymous</span>
          </div>
        </motion.div>
      </div>

      {/* Background decoration */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-1/3 left-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute bottom-1/3 right-0 w-72 h-72 bg-cyan-500/5 rounded-full blur-3xl" />
      </div>
    </section>
  );
}
