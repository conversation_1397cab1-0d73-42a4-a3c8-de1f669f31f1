// Core TypeScript interfaces and types for VeilMeet application
// Production-ready type definitions with comprehensive coverage

import { Id } from '@/convex/_generated/dataModel';

// ============================================================================
// CORE DOMAIN TYPES
// ============================================================================

export type RoomStatus = 'active' | 'ended' | 'paused';
export type MessageType = 'text' | 'voice' | 'system' | 'ai_generated';
export type ModerationLevel = 'light' | 'moderate' | 'strict';
export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

// ============================================================================
// ROOM INTERFACES
// ============================================================================

export interface Room {
  readonly _id: Id<'rooms'>;
  readonly roomCode: string;
  readonly status: RoomStatus;
  readonly topic?: string;
  readonly isPublic: boolean;
  readonly maxParticipants: number;
  readonly currentParticipants: number;
  readonly createdAt: number;
  readonly expiresAt: number;
  readonly aiModerationLevel: ModerationLevel;
  readonly aiAgentsEnabled: string[];
  readonly allowVoice: boolean;
  readonly allowVideo: boolean;
  readonly allowScreenShare: boolean;
  readonly voiceModulationEnabled: boolean;
  readonly recordingAllowed: boolean;
  readonly ephemeralMode: boolean;
  readonly totalMessages?: number;
  readonly averageSessionDuration?: number;
  readonly participantSatisfactionScore?: number;
}

export interface RoomConfig {
  readonly topic?: string;
  readonly isPublic?: boolean;
  readonly maxParticipants?: number;
  readonly allowVoice?: boolean;
  readonly allowVideo?: boolean;
  readonly allowScreenShare?: boolean;
  readonly aiModerationLevel?: ModerationLevel;
  readonly voiceModulationEnabled?: boolean;
  readonly recordingAllowed?: boolean;
  readonly ephemeralMode?: boolean;
}

export interface RoomMetrics {
  readonly totalMessages: number;
  readonly averageSessionDuration: number;
  readonly participantSatisfactionScore: number;
  readonly peakParticipants: number;
  readonly messageFrequency: number;
}

// ============================================================================
// PARTICIPANT INTERFACES
// ============================================================================

export interface Participant {
  readonly _id: Id<'participants'>;
  readonly roomId: Id<'rooms'>;
  readonly sessionId: string;
  readonly anonymousAlias: string;
  readonly avatarSeed: string;
  readonly colorTheme: string;
  readonly joinedAt: number;
  readonly lastActiveAt: number;
  readonly leftAt?: number;
  readonly messageCount: number;
  readonly speakingTime: number;
  readonly reactionsGiven: number;
  readonly reactionsReceived: number;
  readonly isActive: boolean;
  readonly isMuted: boolean;
  readonly isBlocked: boolean;
}

export interface ParticipantSummary {
  readonly sessionId: string;
  readonly anonymousAlias: string;
  readonly avatarSeed: string;
  readonly colorTheme: string;
  readonly joinedAt: number;
  readonly messageCount: number;
  readonly isActive: boolean;
  readonly isMuted: boolean;
  readonly isOnline: boolean;
}

// ============================================================================
// MESSAGE INTERFACES
// ============================================================================

export interface MessageMention {
  readonly participantSessionId: string;
  readonly anonymousAlias: string;
  readonly startIndex: number;
  readonly endIndex: number;
  readonly mentionText: string;
}

export interface MessageReaction {
  readonly emoji: string;
  readonly count: number;
  readonly participantSessionIds: string[];
  readonly firstReactedAt: number;
  readonly lastReactedAt: number;
  readonly participantDetails: Array<{
    sessionId: string;
    anonymousAlias: string;
    reactedAt: number;
  }>;
}

export interface MessageEngagementMetrics {
  readonly totalReactions: number;
  readonly uniqueReactors: number;
  readonly replyCount: number;
  readonly mentionCount: number;
  readonly viewCount: number;
  readonly lastEngagementAt?: number;
}

export interface Message {
  readonly _id: Id<'messages'>;
  readonly roomId: Id<'rooms'>;
  readonly participantSessionId: string;
  readonly anonymousAlias: string;
  readonly content: string;
  readonly messageType: MessageType;
  readonly timestamp: number;
  readonly isEdited: boolean;
  readonly editedAt?: number;
  readonly reactions: MessageReaction[];
  readonly replyToMessageId?: Id<'messages'>;
  readonly threadRootId?: Id<'messages'>;
  readonly threadDepth: number;
  readonly threadReplyCount: number;
  readonly mentions: MessageMention[];
  readonly hasMentions: boolean;
  readonly engagementMetrics: MessageEngagementMetrics;
  readonly isDeleted?: boolean;
  readonly deletedAt?: number;
  readonly aiGenerated?: boolean;
  readonly aiConfidence?: number;
}

export interface MessageDraft {
  readonly content: string;
  readonly replyToMessageId?: Id<'messages'>;
  readonly messageType?: MessageType;
}

// ============================================================================
// IDENTITY INTERFACES
// ============================================================================

export interface AnonymousIdentity {
  readonly sessionId: string;
  readonly anonymousAlias: string;
  readonly avatarSeed: string;
  readonly colorTheme: string;
  readonly createdAt: number;
  readonly expiresAt?: number;
}

export interface TypingParticipant {
  readonly sessionId: string;
  readonly anonymousAlias: string;
  readonly timestamp: number;
}

// ============================================================================
// UI STATE INTERFACES
// ============================================================================

export interface MediaState {
  readonly isAudioEnabled: boolean;
  readonly isVideoEnabled: boolean;
  readonly isScreenSharing: boolean;
  readonly isMuted: boolean;
  readonly audioLevel: number;
  readonly videoQuality: 'low' | 'medium' | 'high';
}

export interface UIState {
  readonly isMobileMenuOpen: boolean;
  readonly isSettingsOpen: boolean;
  readonly isParticipantsListOpen: boolean;
  readonly selectedMessageId?: string;
  readonly replyToMessageId?: string;
  readonly isTyping: boolean;
  readonly lastActivity: number;
}

export interface ViewportState {
  readonly width: number;
  readonly height: number;
  readonly isMobile: boolean;
  readonly isTablet: boolean;
  readonly isDesktop: boolean;
  readonly orientation: 'portrait' | 'landscape';
}

// ============================================================================
// COMPONENT PROP INTERFACES
// ============================================================================

export interface RoomHeaderProps {
  readonly room: Room;
  readonly participantCount: number;
  readonly onShare: () => void;
  readonly onLeave: () => void;
  readonly onEnd?: () => void;
  readonly isCreator?: boolean;
  readonly isLoading?: boolean;
  readonly className?: string;
}

export interface RoomSidebarProps {
  readonly room: Room;
  readonly participants: ParticipantSummary[];
  readonly currentParticipant?: ParticipantSummary;
  readonly mediaState: MediaState;
  readonly onMediaToggle: (type: 'audio' | 'video' | 'screen') => void;
  readonly onClose?: () => void;
  readonly className?: string;
}

export interface ChatInterfaceProps {
  readonly messages: Message[];
  readonly currentParticipant?: ParticipantSummary;
  readonly participants: ParticipantSummary[];
  readonly onMessageReact: (messageId: string, emoji: string) => void;
  readonly onMessageReply: (messageId: string, content: string, author: string) => void;
  readonly onMessageEdit: (messageId: string, content: string) => void;
  readonly onMessageDelete: (messageId: string) => void;
  readonly onMentionClick?: (participantSessionId: string) => void;
  readonly replyingTo?: {
    messageId: string;
    content: string;
    author: string;
  } | null;
  readonly className?: string;
}

export interface MessageInputProps {
  readonly roomId: Id<'rooms'>;
  readonly currentParticipant?: ParticipantSummary;
  readonly replyToMessage?: Message;
  readonly onSendMessage: (content: string, replyToId?: string) => Promise<void>;
  readonly onCancelReply?: () => void;
  readonly disabled?: boolean;
  readonly placeholder?: string;
  readonly className?: string;
}

export interface ParticipantsListProps {
  readonly participants: ParticipantSummary[];
  readonly currentSessionId?: string;
  readonly showDetails?: boolean;
  readonly onParticipantAction?: (sessionId: string, action: string) => void;
  readonly className?: string;
}

export interface TypingIndicatorProps {
  readonly typingParticipants: TypingParticipant[];
  readonly currentSessionId?: string;
  readonly maxDisplay?: number;
  readonly className?: string;
}

// ============================================================================
// API RESPONSE INTERFACES
// ============================================================================

export interface ApiResponse<T = unknown> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: string;
  readonly timestamp: number;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  readonly pagination: {
    readonly page: number;
    readonly limit: number;
    readonly total: number;
    readonly hasNext: boolean;
    readonly hasPrev: boolean;
  };
}

export interface RoomJoinResponse {
  readonly participant: Participant;
  readonly room: Room;
  readonly existingMessages: Message[];
}

export interface MessageSendResponse {
  readonly message: Message;
  readonly tempId?: string;
}

// ============================================================================
// ERROR INTERFACES
// ============================================================================

export interface AppError {
  readonly code: string;
  readonly message: string;
  readonly details?: Record<string, unknown>;
  readonly timestamp: number;
  readonly recoverable: boolean;
}

export interface ValidationError extends AppError {
  readonly field: string;
  readonly value: unknown;
  readonly constraint: string;
}

// ============================================================================
// HOOK RETURN TYPES
// ============================================================================

export interface UseRoomReturn {
  readonly room: Room | null;
  readonly participants: ParticipantSummary[];
  readonly messages: Message[];
  readonly typingIndicators: TypingParticipant[];
  readonly currentParticipant: ParticipantSummary | null;
  readonly isParticipant: boolean;
  readonly isRoomValid: boolean;
  readonly isRoomFull: boolean;
  readonly isLoading: boolean;
  readonly error: AppError | null;
}

export interface UseMediaReturn {
  readonly mediaState: MediaState;
  readonly toggleAudio: () => Promise<void>;
  readonly toggleVideo: () => Promise<void>;
  readonly toggleScreenShare: () => Promise<void>;
  readonly setMuted: (muted: boolean) => void;
  readonly isSupported: boolean;
  readonly permissions: MediaPermissions;
}

export interface MediaPermissions {
  readonly audio: PermissionState;
  readonly video: PermissionState;
  readonly screen: PermissionState;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type EventHandler<T = void> = (event: T) => void | Promise<void>;

export type AsyncEventHandler<T = void> = (event: T) => Promise<void>;
