// convex/schema.ts
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Core Room Management (Enhanced for Voice)
  rooms: defineTable({
    roomCode: v.string(), // e.g., "sunset-wolf-7392"
    status: v.union(v.literal("active"), v.literal("ended"), v.literal("paused")),
    topic: v.optional(v.string()),
    isPublic: v.boolean(),
    maxParticipants: v.number(),
    currentParticipants: v.number(),
    createdAt: v.number(),
    expiresAt: v.number(), // Auto-delete timestamp
    
    // AI Configuration
    aiModerationLevel: v.union(v.literal("light"), v.literal("moderate"), v.literal("strict")),
    aiAgentsEnabled: v.array(v.string()), // ["moderator", "facilitator", "content_generator"]
    
    // Room Settings
    allowVoice: v.boolean(),
    allowVideo: v.boolean(),
    allowScreenShare: v.boolean(),
    voiceModulationEnabled: v.boolean(),
    
    // LiveKit Voice Configuration
    voiceRoomEnabled: v.boolean(),
    voiceRoomStatus: v.union(v.literal("active"), v.literal("inactive"), v.literal("paused")),
    maxVoiceParticipants: v.number(),
    currentVoiceParticipants: v.number(),
    voiceRoomSettings: v.object({
      muteOnJoin: v.boolean(),
      pushToTalk: v.boolean(),
      noiseSuppressionEnabled: v.boolean(),
      echoCancellationEnabled: v.boolean(),
      autoGainControlEnabled: v.boolean(),
      spatialAudioEnabled: v.boolean(),
      recordVoiceSession: v.boolean(),
    }),
    
    // Privacy Settings
    recordingAllowed: v.boolean(),
    ephemeralMode: v.boolean(), // No message history kept
    
    // Metadata (aggregated, anonymous)
    totalMessages: v.optional(v.number()),
    averageSessionDuration: v.optional(v.number()),
    participantSatisfactionScore: v.optional(v.number()),
    totalVoiceMinutes: v.optional(v.number()),
    averageVoiceSessionDuration: v.optional(v.number()),
  })
    .index("by_room_code", ["roomCode"])
    .index("by_status", ["status"])
    .index("by_public_active", ["isPublic", "status"])
    .index("by_expires_at", ["expiresAt"])
    .index("by_voice_enabled", ["voiceRoomEnabled", "status"]),

  // LiveKit Voice Rooms
  voiceRooms: defineTable({
    roomId: v.id("rooms"),
    livekitRoomName: v.string(), // LiveKit room identifier
    livekitRoomSid: v.optional(v.string()), // LiveKit room SID when created
    
    // Voice Room Configuration
    maxParticipants: v.number(),
    currentParticipants: v.number(),
    isRecording: v.boolean(),
    recordingUrl: v.optional(v.string()),
    
    // Voice Room Status
    status: v.union(v.literal("active"), v.literal("ended"), v.literal("paused")),
    createdAt: v.number(),
    startedAt: v.optional(v.number()),
    endedAt: v.optional(v.number()),
    
    // Session Metrics
    totalSpeakingTime: v.number(),
    totalParticipants: v.number(),
    peakParticipants: v.number(),
    
    // Moderation
    aiVoiceModerationEnabled: v.boolean(),
    moderationEvents: v.number(),
    
    expiresAt: v.number(),
  })
    .index("by_room", ["roomId"])
    .index("by_livekit_room", ["livekitRoomName"])
    .index("by_status", ["status"])
    .index("by_expires_at", ["expiresAt"]),

  // LiveKit Voice Participants
  voiceParticipants: defineTable({
    roomId: v.id("rooms"),
    voiceRoomId: v.id("voiceRooms"),
    participantSessionId: v.string(), // Links to main participant
    anonymousAlias: v.string(),
    
    // LiveKit Identity
    livekitParticipantSid: v.optional(v.string()),
    livekitIdentity: v.string(), // Unique identifier for LiveKit
    
    // Voice Session Data
    joinedVoiceAt: v.number(),
    leftVoiceAt: v.optional(v.number()),
    isCurrentlyInVoice: v.boolean(),
    
    // Audio Settings & Status
    isMuted: v.boolean(),
    isSpeaking: v.boolean(),
    audioEnabled: v.boolean(),
    videoEnabled: v.boolean(),
    screenShareEnabled: v.boolean(),
    
    // Voice Modulation (if enabled)
    voiceModulationSettings: v.optional(v.object({
      pitchShift: v.number(),
      toneType: v.string(),
      enabled: v.boolean(),
    })),
    
    // Speaking Analytics
    totalSpeakingTime: v.number(), // Seconds
    speakingTurns: v.number(),
    averageSpeakingTurnDuration: v.number(),
    longestSpeakingTurn: v.number(),
    
    // Connection Quality
    connectionQuality: v.optional(v.string()), // "excellent", "good", "poor"
    audioQuality: v.optional(v.number()), // 0-1 score
    lastQualityCheck: v.optional(v.number()),
    
    // Permissions
    canSpeak: v.boolean(),
    canModerate: v.boolean(),
    
    // Temporary mute/restrictions
    temporarilyMuted: v.boolean(),
    muteReason: v.optional(v.string()),
    muteExpiresAt: v.optional(v.number()),
  })
    .index("by_room", ["roomId"])
    .index("by_voice_room", ["voiceRoomId"])
    .index("by_session", ["participantSessionId"])
    .index("by_livekit_identity", ["livekitIdentity"])
    .index("by_currently_in_voice", ["voiceRoomId", "isCurrentlyInVoice"])
    .index("by_room_active", ["roomId", "isCurrentlyInVoice"]),

  // Voice Session Events (for analytics & debugging)
  voiceEvents: defineTable({
    roomId: v.id("rooms"),
    voiceRoomId: v.id("voiceRooms"),
    participantSessionId: v.string(),
    
    eventType: v.union(
      v.literal("joined"),
      v.literal("left"),
      v.literal("muted"),
      v.literal("unmuted"),
      v.literal("started_speaking"),
      v.literal("stopped_speaking"),
      v.literal("audio_enabled"),
      v.literal("audio_disabled"),
      v.literal("video_enabled"),
      v.literal("video_disabled"),
      v.literal("screen_share_started"),
      v.literal("screen_share_stopped"),
      v.literal("connection_quality_changed"),
      v.literal("temporarily_muted"),
      v.literal("kicked"),
      v.literal("reconnected")
    ),
    
    // Event Data
    eventData: v.optional(v.object({
      previousValue: v.optional(v.string()),
      newValue: v.optional(v.string()),
      reason: v.optional(v.string()),
      duration: v.optional(v.number()), // For speaking/mute events
      quality: v.optional(v.string()), // For connection quality
    })),
    
    createdAt: v.number(),
    expiresAt: v.number(), // Auto-cleanup
  })
    .index("by_room", ["roomId"])
    .index("by_voice_room", ["voiceRoomId"])
    .index("by_participant", ["participantSessionId"])
    .index("by_event_type", ["eventType"])
    .index("by_expires_at", ["expiresAt"]),

  // LiveKit Access Tokens (temporary storage)
  voiceTokens: defineTable({
    participantSessionId: v.string(),
    roomId: v.id("rooms"),
    voiceRoomId: v.id("voiceRooms"),
    
    // Token Data
    livekitToken: v.string(),
    livekitIdentity: v.string(),
    
    // Permissions encoded in token
    permissions: v.object({
      canPublish: v.boolean(),
      canSubscribe: v.boolean(),
      canPublishData: v.boolean(),
      canModerate: v.boolean(),
    }),
    
    // Token Lifecycle
    issuedAt: v.number(),
    expiresAt: v.number(),
    isRevoked: v.boolean(),
    revokedAt: v.optional(v.number()),
    revokeReason: v.optional(v.string()),
  })
    .index("by_participant", ["participantSessionId"])
    .index("by_room", ["roomId"])
    .index("by_identity", ["livekitIdentity"])
    .index("by_expires_at", ["expiresAt"]),

  // Voice Room Analytics
  voiceAnalytics: defineTable({
    voiceRoomId: v.id("voiceRooms"),
    roomId: v.id("rooms"),
    
    // Session Overview
    sessionDuration: v.number(), // Total duration in seconds
    totalParticipants: v.number(),
    peakConcurrentParticipants: v.number(),
    averageParticipants: v.number(),
    
    // Speaking Analytics
    totalSpeakingTime: v.number(),
    averageSpeakingTimePerParticipant: v.number(),
    speakingDistribution: v.array(v.object({
      participantSessionId: v.string(),
      speakingTime: v.number(),
      speakingTurns: v.number(),
    })),
    
    // Engagement Metrics
    participantRetentionRate: v.number(), // % who stayed till end
    averageSessionDuration: v.number(),
    interactionRate: v.number(), // Speaking turns per minute
    
    // Quality Metrics
    averageConnectionQuality: v.number(),
    audioIssuesReported: v.number(),
    disconnectionEvents: v.number(),
    
    // AI Moderation (if enabled)
    aiModerationEvents: v.number(),
    contentFlagged: v.number(),
    automaticMutes: v.number(),
    
    calculatedAt: v.number(),
  })
    .index("by_voice_room", ["voiceRoomId"])
    .index("by_room", ["roomId"])
    .index("by_calculated_at", ["calculatedAt"]),

  // Anonymous Participants (Enhanced for Voice)
  participants: defineTable({
    roomId: v.id("rooms"),
    sessionId: v.string(), // Unique per room session, not tied to user
    anonymousAlias: v.string(), // "Purple Phoenix", "Thoughtful Wolf"
    avatarSeed: v.string(), // For consistent avatar generation
    
    // Voice Settings
    voiceModulationSettings: v.object({
      pitchShift: v.number(),
      toneType: v.string(),
      enabled: v.boolean(),
    }),
    voiceEnabled: v.boolean(), // Has access to voice features
    preferredVoiceMode: v.union(v.literal("push_to_talk"), v.literal("voice_activation"), v.literal("always_on")),
    
    // Session Data
    joinedAt: v.number(),
    lastActiveAt: v.number(),
    leftAt: v.optional(v.number()),
    
    // Voice Session Data
    joinedVoiceAt: v.optional(v.number()),
    leftVoiceAt: v.optional(v.number()),
    isCurrentlyInVoice: v.boolean(),
    totalVoiceTime: v.number(),
    
    // Interaction Metrics (Anonymous)
    messageCount: v.number(),
    speakingTime: v.number(), // In seconds
    reactionsGiven: v.number(),
    reactionsReceived: v.number(),
    
    // Status
    isActive: v.boolean(),
    isMuted: v.boolean(),
    isBlocked: v.boolean(), // By AI moderation
    
    // No personal identifiers - completely anonymous
  })
    .index("by_room", ["roomId"])
    .index("by_session", ["sessionId"])
    .index("by_room_active", ["roomId", "isActive"])
    .index("by_room_in_voice", ["roomId", "isCurrentlyInVoice"]),

  // Enhanced Active Connections (WebRTC + LiveKit)
  activeConnections: defineTable({
    roomId: v.id("rooms"),
    participantSessionId: v.string(),
    connectionId: v.string(), // WebSocket connection ID

    // Real-time Status
    isConnected: v.boolean(),
    lastPing: v.number(),

    // Voice/Video Status
    isAudioEnabled: v.boolean(),
    isVideoEnabled: v.boolean(),
    isScreenSharing: v.boolean(),
    isInVoiceRoom: v.boolean(),

    // LiveKit Connection
    livekitConnected: v.boolean(),
    livekitConnectionQuality: v.optional(v.string()),
    livekitLastUpdate: v.optional(v.number()),

    // Current Activity
    currentAction: v.optional(v.string()), // "typing", "speaking", "idle"

    connectedAt: v.number(),
    expiresAt: v.number(), // Auto-cleanup
  })
    .index("by_room", ["roomId"])
    .index("by_connection", ["connectionId"])
    .index("by_expires_at", ["expiresAt"])
    .index("by_room_voice", ["roomId", "isInVoiceRoom"]),

  // Rest of your existing tables remain the same...
  messages: defineTable({
    roomId: v.id("rooms"),
    participantSessionId: v.string(),
    anonymousAlias: v.string(),

    content: v.string(),
    messageType: v.union(
      v.literal("text"),
      v.literal("voice"),
      v.literal("system"),
      v.literal("ai_generated"),
      v.literal("voice_to_text") // New: transcribed voice messages
    ),

    // Voice Message Data (if applicable)
    voiceMessageData: v.optional(v.object({
      duration: v.number(), // seconds
      transcription: v.optional(v.string()),
      audioUrl: v.optional(v.string()),
      waveformData: v.optional(v.array(v.number())),
    })),

    // Timestamps
    createdAt: v.number(),
    editedAt: v.number(),

    // Message State
    isEdited: v.boolean(),
    isDeleted: v.boolean(),
    isImportant: v.boolean(),

    // Enhanced Reactions with detailed tracking
    reactions: v.array(v.object({
      emoji: v.string(),
      count: v.number(),
      participantSessionIds: v.array(v.string()),
      // Enhanced reaction metadata
      firstReactedAt: v.number(),
      lastReactedAt: v.number(),
      participantDetails: v.array(v.object({
        sessionId: v.string(),
        anonymousAlias: v.string(),
        reactedAt: v.number(),
      })),
    })),

    // Enhanced Threading Support
    replyToMessageId: v.optional(v.id("messages")),
    threadRootId: v.optional(v.id("messages")), // Points to the root message of the thread
    threadDepth: v.optional(v.number()), // 0 for root messages, 1+ for replies
    threadReplyCount: v.optional(v.number()), // Number of direct replies to this message
    threadParticipantCount: v.optional(v.number()), // Number of unique participants in thread
    threadLastReplyAt: v.optional(v.number()), // Timestamp of last reply in thread

    // Mentions System
    mentions: v.optional(v.array(v.object({
      participantSessionId: v.string(),
      anonymousAlias: v.string(),
      startIndex: v.number(), // Character position where mention starts
      endIndex: v.number(), // Character position where mention ends
      mentionText: v.string(), // The actual @username text
    }))),
    hasMentions: v.optional(v.boolean()), // Quick lookup for messages with mentions

    // Message Engagement Analytics
    engagementMetrics: v.optional(v.object({
      totalReactions: v.number(),
      uniqueReactors: v.number(),
      replyCount: v.number(),
      mentionCount: v.number(),
      viewCount: v.number(), // How many times message was viewed
      lastEngagementAt: v.optional(v.number()),
    })),

    // Attachments (for future use)
    attachments: v.array(v.object({
      type: v.string(),
      url: v.string(),
      name: v.string(),
      size: v.number(),
    })),

    // AI Enhancement
    sentiment: v.optional(v.string()), // "positive", "negative", "neutral"
    keyTopics: v.optional(v.array(v.string())),

    // Moderation
    moderationScore: v.optional(v.number()), // 0-1, toxicity score
    moderationReason: v.optional(v.string()),

    // Metadata for extensibility
    metadata: v.optional(v.object({
      flagged: v.optional(v.boolean()),
      flagReason: v.optional(v.string()),
      flaggedAt: v.optional(v.number()),
      approved: v.optional(v.boolean()),
      approvedAt: v.optional(v.number()),
    })),
  })
    .index("by_room", ["roomId"])
    .index("by_room_time", ["roomId", "createdAt"])
    .index("by_important", ["roomId", "isImportant"])
    .index("by_reply", ["replyToMessageId"])
    .index("by_thread_root", ["threadRootId"])
    .index("by_mentions", ["roomId", "hasMentions"])
    .index("by_engagement", ["roomId", "engagementMetrics.lastEngagementAt"])
    .index("by_thread_activity", ["roomId", "threadLastReplyAt"])
    .index("by_message_type", ["roomId", "messageType"]),

  // AI Agent Sessions
  aiAgentSessions: defineTable({
    roomId: v.id("rooms"),
    agentType: v.string(), // "moderator", "facilitator", "content_generator", "voice_moderator"
    agentPersonality: v.string(), // "professional", "casual", "supportive"
    
    // Configuration
    config: v.object({
      interventionThreshold: v.number(),
      responseFrequency: v.string(),
      focusAreas: v.array(v.string()),
      voiceModerationEnabled: v.optional(v.boolean()),
    }),
    
    // Performance Metrics
    interventions: v.number(),
    messagesGenerated: v.number(),
    voiceInterventions: v.optional(v.number()),
    participantEngagementImprovement: v.optional(v.number()),
    
    // Status
    isActive: v.boolean(),
    startedAt: v.number(),
    endedAt: v.optional(v.number()),
  })
    .index("by_room", ["roomId"])
    .index("by_agent_type", ["agentType"])
    .index("by_active", ["isActive"]),

  // AI-Generated Content
  aiGeneratedContent: defineTable({
    roomId: v.id("rooms"),
    contentType: v.union(
      v.literal("summary"), 
      v.literal("key_insights"), 
      v.literal("action_items"),
      v.literal("follow_up_questions"),
      v.literal("topic_suggestions"),
      v.literal("voice_summary"), // New: voice session summaries
      v.literal("speaking_insights") // New: speaking pattern insights
    ),
    
    content: v.string(),
    confidence: v.number(), // AI confidence in the content
    
    // Context
    basedOnMessages: v.array(v.string()), // Message IDs used for generation
    basedOnVoiceData: v.optional(v.boolean()), // If generated from voice analytics
    timeframe: v.object({
      startTime: v.number(),
      endTime: v.number(),
    }),
    
    // User Interaction
    userRating: v.optional(v.number()), // 1-5 stars
    isHelpful: v.optional(v.boolean()),
    
    createdAt: v.number(),
    expiresAt: v.number(), // Auto-delete
  })
    .index("by_room", ["roomId"])
    .index("by_room_type", ["roomId", "contentType"])
    .index("by_expires_at", ["expiresAt"]),

  // Room Discovery & Topics
  roomTopics: defineTable({
    name: v.string(), // "Mental Health", "Tech Discussion", "Anonymous Feedback"
    description: v.string(),
    category: v.string(), // "Support", "Professional", "Social", "Education"
    
    // Usage Stats
    totalRooms: v.number(),
    activeRooms: v.number(),
    averageParticipants: v.number(),
    averageDuration: v.number(),
    voiceRoomsEnabled: v.number(), // How many use voice
    
    // AI Recommendations
    recommendedAiAgents: v.array(v.string()),
    moderationLevel: v.string(),
    voiceRecommended: v.boolean(), // Whether voice is recommended for this topic
    
    isActive: v.boolean(),
    createdAt: v.number(),
  })
    .index("by_category", ["category"])
    .index("by_active", ["isActive"])
    .index("by_voice_recommended", ["voiceRecommended"]),

  // Message Reactions (Separate table for detailed tracking)
  messageReactions: defineTable({
    messageId: v.id("messages"),
    roomId: v.id("rooms"),
    participantSessionId: v.string(),
    anonymousAlias: v.string(),

    emoji: v.string(),
    createdAt: v.number(),

    // Reaction context
    reactionContext: v.optional(v.object({
      deviceType: v.optional(v.string()), // "mobile", "desktop", "tablet"
      reactionSpeed: v.optional(v.number()), // Time from message sent to reaction
      fromVoiceRoom: v.optional(v.boolean()), // If reacted while in voice room
    })),
  })
    .index("by_message", ["messageId"])
    .index("by_room", ["roomId"])
    .index("by_participant", ["participantSessionId"])
    .index("by_emoji", ["emoji"])
    .index("by_message_emoji", ["messageId", "emoji"]),

  // Message Mentions (Separate table for efficient querying)
  messageMentions: defineTable({
    messageId: v.id("messages"),
    roomId: v.id("rooms"),
    mentionerSessionId: v.string(), // Who mentioned
    mentionedSessionId: v.string(), // Who was mentioned
    mentionedAlias: v.string(),

    mentionText: v.string(), // The @username text
    startIndex: v.number(),
    endIndex: v.number(),

    createdAt: v.number(),
    isRead: v.boolean(), // Has the mentioned user seen this?
    readAt: v.optional(v.number()),
  })
    .index("by_message", ["messageId"])
    .index("by_room", ["roomId"])
    .index("by_mentioned", ["mentionedSessionId"])
    .index("by_mentioned_unread", ["mentionedSessionId", "isRead"])
    .index("by_room_mentioned", ["roomId", "mentionedSessionId"]),

  // Message Threads (Enhanced thread tracking)
  messageThreads: defineTable({
    rootMessageId: v.id("messages"),
    roomId: v.id("rooms"),

    // Thread metadata
    participantCount: v.number(),
    messageCount: v.number(),
    lastActivityAt: v.number(),

    // Thread participants
    participants: v.array(v.object({
      sessionId: v.string(),
      anonymousAlias: v.string(),
      messageCount: v.number(),
      firstParticipatedAt: v.number(),
      lastParticipatedAt: v.number(),
    })),

    // Thread analytics
    engagementScore: v.number(), // Calculated engagement metric
    isActive: v.boolean(),

    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_root_message", ["rootMessageId"])
    .index("by_room", ["roomId"])
    .index("by_room_activity", ["roomId", "lastActivityAt"])
    .index("by_engagement", ["roomId", "engagementScore"]),

  // Anonymous Feedback & Ratings (Enhanced for Voice)
  sessionFeedback: defineTable({
    roomId: v.id("rooms"),
    participantSessionId: v.string(), // Anonymous participant

    // Ratings (1-5)
    overallSatisfaction: v.number(),
    conversationQuality: v.number(),
    aiHelpfulness: v.number(),
    anonymityComfort: v.number(),
    voiceQuality: v.optional(v.number()), // Voice-specific rating
    voiceExperience: v.optional(v.number()), // Overall voice experience
    wouldRecommend: v.boolean(),

    // Qualitative Feedback (Anonymous)
    feedback: v.optional(v.string()),
    improvementSuggestions: v.optional(v.string()),
    voiceFeedback: v.optional(v.string()), // Voice-specific feedback

    // Session Context
    sessionDuration: v.number(),
    messagesExchanged: v.number(),
    voiceTimeSpent: v.optional(v.number()),
    usedVoiceFeature: v.boolean(),

    createdAt: v.number(),
  })
    .index("by_room", ["roomId"])
    .index("by_satisfaction", ["overallSatisfaction"])
    .index("by_voice_usage", ["usedVoiceFeature"]),

  // Moderation & Safety (Enhanced for Voice)
  moderationEvents: defineTable({
    roomId: v.id("rooms"),
    eventType: v.union(
      v.literal("auto_warning"),
      v.literal("content_hidden"),
      v.literal("participant_muted"),
      v.literal("escalation_triggered"),
      v.literal("ai_intervention"),
      v.literal("voice_muted"), // New voice-specific events
      v.literal("voice_kicked"),
      v.literal("voice_content_flagged"),
      v.literal("voice_quality_warning")
    ),
    
    // Event Details
    severity: v.union(v.literal("low"), v.literal("medium"), v.literal("high")),
    reason: v.string(),
    automaticAction: v.boolean(),
    
    // Context (Anonymous)
    affectedParticipant: v.optional(v.string()), // Session ID only
    triggerContent: v.optional(v.string()),
    voiceRelated: v.boolean(),
    
    // AI Decision Making
    aiConfidence: v.number(),
    humanReviewRequired: v.boolean(),
    humanReviewCompleted: v.optional(v.boolean()),
    
    createdAt: v.number(),
    resolvedAt: v.optional(v.number()),
  })
    .index("by_room", ["roomId"])
    .index("by_severity", ["severity"])
    .index("by_human_review", ["humanReviewRequired"])
    .index("by_voice_related", ["voiceRelated"]),

  // Analytics (Aggregated, Anonymous) - Enhanced for Voice
  roomAnalytics: defineTable({
    roomId: v.id("rooms"),
    
    // Participation Metrics
    peakParticipants: v.number(),
    totalUniqueParticipants: v.number(),
    averageSessionDuration: v.number(),
    totalMessages: v.number(),
    totalSpeakingTime: v.number(),
    
    // Voice Metrics
    voiceParticipationRate: v.optional(v.number()), // % who joined voice
    averageVoiceDuration: v.optional(v.number()),
    voiceEngagementScore: v.optional(v.number()),
    simultaneousSpeakersAvg: v.optional(v.number()),
    
    // Engagement Metrics
    messageRate: v.number(), // Messages per minute
    participantRetention: v.number(), // Percentage who stayed >50% of session
    crossTalk: v.number(), // How much participants responded to each other
    voiceToTextRatio: v.optional(v.number()), // Voice vs text communication ratio
    
    // AI Performance
    aiInterventions: v.number(),
    aiSuccessRate: v.number(), // Based on participant feedback
    contentGenerationRequests: v.number(),
    voiceAiInterventions: v.optional(v.number()),
    
    // Quality Metrics
    averageSentiment: v.number(),
    toxicContentPercentage: v.number(),
    reportedIssues: v.number(),
    voiceQualityScore: v.optional(v.number()),
    
    // Outcomes
    actionItemsGenerated: v.number(),
    followUpInterest: v.number(), // Participants wanting follow-up sessions
    
    calculatedAt: v.number(),
  })
    .index("by_room", ["roomId"])
    .index("by_calculated_at", ["calculatedAt"]),

  // Global Platform Analytics (Enhanced for Voice)
  platformMetrics: defineTable({
    date: v.string(), // YYYY-MM-DD
    
    // Usage
    totalRoomsCreated: v.number(),
    totalParticipants: v.number(),
    totalMessages: v.number(),
    averageRoomDuration: v.number(),
    
    // Voice Usage
    voiceRoomsCreated: v.number(),
    voiceParticipants: v.number(),
    totalVoiceMinutes: v.number(),
    voiceAdoptionRate: v.number(), // % of rooms that enable voice
    
    // Popular Topics
    topTopics: v.array(v.object({
      topic: v.string(),
      roomCount: v.number(),
      voiceUsage: v.number(),
    })),
    
    // AI Performance
    aiModerationsPerformed: v.number(),
    aiAccuracyRate: v.number(),
    userSatisfactionWithAi: v.number(),
    voiceAiAccuracy: v.number(),
    
    // Growth Metrics
    newUsersToday: v.number(),
    returningUsers: v.number(),
    viralityCoefficient: v.number(), // How many people share rooms
    voiceFeatureRetention: v.number(), // Users who return to use voice again
    
    calculatedAt: v.number(),
  })
    .index("by_date", ["date"]),

  // Temporary Anonymous Identities (Enhanced for Voice)
  temporaryIdentities: defineTable({
    sessionId: v.string(),
    anonymousAlias: v.string(), // "Thoughtful Phoenix"
    avatarSeed: v.string(), // For consistent avatar generation
    colorTheme: v.string(), // "Purple", "Emerald", etc.
    roomCode: v.optional(v.string()), // Associated room if any
    
    // Voice Identity
    voiceProfile: v.optional(v.object({
      preferredName: v.string(), // How they want to be addressed in voice
      voiceModulationPreference: v.string(),
      microphoneSetup: v.optional(v.string()), // "headset", "built-in", "external"
    })),

    createdAt: v.number(),
    expiresAt: v.number(), // Auto-cleanup after 24 hours
    isActive: v.boolean(),
  })
    .index("by_session", ["sessionId"])
    .index("by_expires_at", ["expiresAt"]),

  // LiveKit Room Management & Webhooks
  livekitWebhookEvents: defineTable({
    roomId: v.id("rooms"),
    voiceRoomId: v.optional(v.id("voiceRooms")),
    
    // LiveKit Event Data
    eventType: v.string(), // "room_started", "room_finished", "participant_joined", etc.
    livekitRoomSid: v.string(),
    livekitRoomName: v.string(),
    participantSid: v.optional(v.string()),
    participantIdentity: v.optional(v.string()),
    
    // Event Payload
    eventData: v.object({
      timestamp: v.number(),
      room: v.optional(v.object({
        sid: v.string(),
        name: v.string(),
        numParticipants: v.number(),
        duration: v.optional(v.number()),
      })),
      participant: v.optional(v.object({
        sid: v.string(),
        identity: v.string(),
        state: v.optional(v.string()),
        tracks: v.optional(v.array(v.object({
          sid: v.string(),
          type: v.string(),
          source: v.string(),
        }))),
      })),
    }),
    
    // Processing Status
    processed: v.boolean(),
    processedAt: v.optional(v.number()),
    processingError: v.optional(v.string()),
    
    createdAt: v.number(),
    expiresAt: v.number(), // Auto-cleanup after processing
  })
    .index("by_room", ["roomId"])
    .index("by_livekit_room", ["livekitRoomSid"])
    .index("by_event_type", ["eventType"])
    .index("by_processed", ["processed"])
    .index("by_expires_at", ["expiresAt"]),

  // Voice Recording Management (if enabled)
  voiceRecordings: defineTable({
    roomId: v.id("rooms"),
    voiceRoomId: v.id("voiceRooms"),
    
    // Recording Details
    recordingId: v.string(), // LiveKit recording ID
    recordingUrl: v.optional(v.string()),
    recordingStatus: v.union(
      v.literal("recording"),
      v.literal("processing"),
      v.literal("completed"),
      v.literal("failed"),
      v.literal("deleted")
    ),
    
    // Recording Metadata
    duration: v.optional(v.number()), // seconds
    fileSize: v.optional(v.number()), // bytes
    format: v.string(), // "mp4", "webm", etc.
    
    // Participants in Recording
    participantCount: v.number(),
    participantIdentities: v.array(v.string()),
    
    // Privacy & Consent
    consentGiven: v.boolean(),
    consentTimestamp: v.number(),
    participantConsents: v.array(v.object({
      participantSessionId: v.string(),
      consentGiven: v.boolean(),
      consentTimestamp: v.number(),
    })),
    
    // Auto-deletion
    deleteAt: v.number(), // When to auto-delete the recording
    isDeleted: v.boolean(),
    deletedAt: v.optional(v.number()),
    
    // Access Control
    accessLevel: v.union(v.literal("participants_only"), v.literal("room_creator"), v.literal("none")),
    
    startedAt: v.number(),
    endedAt: v.optional(v.number()),
    createdAt: v.number(),
  })
    .index("by_room", ["roomId"])
    .index("by_voice_room", ["voiceRoomId"])
    .index("by_recording_id", ["recordingId"])
    .index("by_status", ["recordingStatus"])
    .index("by_delete_at", ["deleteAt"]),

  // Voice Transcriptions (if enabled)
  voiceTranscriptions: defineTable({
    roomId: v.id("rooms"),
    voiceRoomId: v.id("voiceRooms"),
    participantSessionId: v.string(),
    
    // Transcription Data
    transcriptionText: v.string(),
    confidence: v.number(), // 0-1 transcription confidence
    language: v.string(), // detected language
    
    // Timing
    startTime: v.number(), // When speaking started
    endTime: v.number(), // When speaking ended
    duration: v.number(), // seconds
    
    // Processing
    processingStatus: v.union(
      v.literal("processing"),
      v.literal("completed"),
      v.literal("failed")
    ),
    
    // Privacy Settings
    isEphemeral: v.boolean(), // Delete after session
    
    createdAt: v.number(),
    expiresAt: v.number(), // Auto-delete
  })
    .index("by_room", ["roomId"])
    .index("by_voice_room", ["voiceRoomId"])
    .index("by_participant", ["participantSessionId"])
    .index("by_time_range", ["voiceRoomId", "startTime"])
    .index("by_expires_at", ["expiresAt"]),

  // Voice Quality Metrics
  voiceQualityMetrics: defineTable({
    roomId: v.id("rooms"),
    voiceRoomId: v.id("voiceRooms"),
    participantSessionId: v.string(),
    
    // Audio Quality Metrics
    audioLevel: v.number(), // Average audio level
    audioQuality: v.string(), // "excellent", "good", "fair", "poor"
    jitter: v.number(), // Network jitter
    latency: v.number(), // Round-trip latency
    packetLoss: v.number(), // Packet loss percentage
    
    // Connection Quality
    connectionQuality: v.string(), // "excellent", "good", "poor"
    bandwidth: v.number(), // Available bandwidth
    
    // Technical Details
    codec: v.string(), // Audio codec used
    bitrate: v.number(), // Audio bitrate
    sampleRate: v.number(), // Audio sample rate
    
    // Issues & Diagnostics
    issuesDetected: v.array(v.string()), // ["echo", "noise", "low_volume"]
    automaticGainControl: v.boolean(),
    noiseSuppression: v.boolean(),
    echoCancellation: v.boolean(),
    
    // Measurement Window
    measurementStart: v.number(),
    measurementEnd: v.number(),
    
    createdAt: v.number(),
    expiresAt: v.number(), // Auto-cleanup
  })
    .index("by_room", ["roomId"])
    .index("by_voice_room", ["voiceRoomId"])
    .index("by_participant", ["participantSessionId"])
    .index("by_quality", ["audioQuality"])
    .index("by_expires_at", ["expiresAt"]),

  // Speaking Turn Analytics
  speakingTurns: defineTable({
    roomId: v.id("rooms"),
    voiceRoomId: v.id("voiceRooms"),
    participantSessionId: v.string(),
    
    // Turn Details
    turnNumber: v.number(), // Sequential turn number in the session
    startTime: v.number(),
    endTime: v.number(),
    duration: v.number(), // seconds
    
    // Audio Analysis
    averageVolume: v.number(),
    peakVolume: v.number(),
    speechRate: v.optional(v.number()), // words per minute if transcribed
    
    // Context
    wasInterrupted: v.boolean(),
    interruptedBy: v.optional(v.string()), // participant session ID
    followedSilence: v.number(), // seconds of silence before this turn
    
    // Quality
    audioQuality: v.string(),
    hasIssues: v.boolean(),
    issues: v.array(v.string()),
    
    createdAt: v.number(),
    expiresAt: v.number(),
  })
    .index("by_voice_room", ["voiceRoomId"])
    .index("by_participant", ["participantSessionId"])
    .index("by_turn_sequence", ["voiceRoomId", "turnNumber"])
    .index("by_duration", ["voiceRoomId", "duration"])
    .index("by_expires_at", ["expiresAt"]),
});