// Validation utilities for form inputs and data validation
// Production-ready validation functions with comprehensive error handling

import { ValidationError } from '@/lib/types';

// ============================================================================
// VALIDATION RESULT TYPES
// ============================================================================

export interface ValidationResult {
  readonly isValid: boolean;
  readonly errors: ValidationError[];
  readonly warnings: string[];
}

export type ValidatorFunction<T = any> = (value: T) => ValidationResult;

// ============================================================================
// BASIC VALIDATORS
// ============================================================================

/**
 * Validate required fields
 */
export function required<T>(value: T, fieldName = 'Field'): ValidationResult {
  const isEmpty = value === null || 
                  value === undefined || 
                  (typeof value === 'string' && value.trim() === '') ||
                  (Array.isArray(value) && value.length === 0);

  return {
    isValid: !isEmpty,
    errors: isEmpty ? [{
      code: 'REQUIRED',
      message: `${fieldName} is required`,
      field: fieldName,
      value,
      constraint: 'required',
      timestamp: Date.now(),
      recoverable: true,
    }] : [],
    warnings: [],
  };
}

/**
 * Validate string length
 */
export function stringLength(
  min?: number,
  max?: number,
  fieldName = 'Field'
): ValidatorFunction<string> {
  return (value: string) => {
    const errors: ValidationError[] = [];
    const warnings: string[] = [];
    
    if (typeof value !== 'string') {
      errors.push({
        code: 'INVALID_TYPE',
        message: `${fieldName} must be a string`,
        field: fieldName,
        value,
        constraint: 'string',
        timestamp: Date.now(),
        recoverable: true,
      });
      return { isValid: false, errors, warnings };
    }

    const length = value.length;

    if (min !== undefined && length < min) {
      errors.push({
        code: 'MIN_LENGTH',
        message: `${fieldName} must be at least ${min} characters`,
        field: fieldName,
        value,
        constraint: `minLength:${min}`,
        timestamp: Date.now(),
        recoverable: true,
      });
    }

    if (max !== undefined && length > max) {
      errors.push({
        code: 'MAX_LENGTH',
        message: `${fieldName} must be no more than ${max} characters`,
        field: fieldName,
        value,
        constraint: `maxLength:${max}`,
        timestamp: Date.now(),
        recoverable: true,
      });
    }

    // Warning for approaching max length
    if (max !== undefined && length > max * 0.9) {
      warnings.push(`${fieldName} is approaching maximum length`);
    }

    return { isValid: errors.length === 0, errors, warnings };
  };
}

/**
 * Validate numeric range
 */
export function numericRange(
  min?: number,
  max?: number,
  fieldName = 'Field'
): ValidatorFunction<number> {
  return (value: number) => {
    const errors: ValidationError[] = [];
    const warnings: string[] = [];

    if (typeof value !== 'number' || isNaN(value)) {
      errors.push({
        code: 'INVALID_TYPE',
        message: `${fieldName} must be a valid number`,
        field: fieldName,
        value,
        constraint: 'number',
        timestamp: Date.now(),
        recoverable: true,
      });
      return { isValid: false, errors, warnings };
    }

    if (min !== undefined && value < min) {
      errors.push({
        code: 'MIN_VALUE',
        message: `${fieldName} must be at least ${min}`,
        field: fieldName,
        value,
        constraint: `min:${min}`,
        timestamp: Date.now(),
        recoverable: true,
      });
    }

    if (max !== undefined && value > max) {
      errors.push({
        code: 'MAX_VALUE',
        message: `${fieldName} must be no more than ${max}`,
        field: fieldName,
        value,
        constraint: `max:${max}`,
        timestamp: Date.now(),
        recoverable: true,
      });
    }

    return { isValid: errors.length === 0, errors, warnings };
  };
}

/**
 * Validate email format
 */
export function email(fieldName = 'Email'): ValidatorFunction<string> {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  return (value: string) => {
    const errors: ValidationError[] = [];
    
    if (typeof value !== 'string') {
      errors.push({
        code: 'INVALID_TYPE',
        message: `${fieldName} must be a string`,
        field: fieldName,
        value,
        constraint: 'string',
        timestamp: Date.now(),
        recoverable: true,
      });
      return { isValid: false, errors, warnings: [] };
    }

    if (!emailRegex.test(value)) {
      errors.push({
        code: 'INVALID_EMAIL',
        message: `${fieldName} must be a valid email address`,
        field: fieldName,
        value,
        constraint: 'email',
        timestamp: Date.now(),
        recoverable: true,
      });
    }

    return { isValid: errors.length === 0, errors, warnings: [] };
  };
}

/**
 * Validate URL format
 */
export function url(fieldName = 'URL'): ValidatorFunction<string> {
  return (value: string) => {
    const errors: ValidationError[] = [];
    
    if (typeof value !== 'string') {
      errors.push({
        code: 'INVALID_TYPE',
        message: `${fieldName} must be a string`,
        field: fieldName,
        value,
        constraint: 'string',
        timestamp: Date.now(),
        recoverable: true,
      });
      return { isValid: false, errors, warnings: [] };
    }

    try {
      new URL(value);
    } catch {
      errors.push({
        code: 'INVALID_URL',
        message: `${fieldName} must be a valid URL`,
        field: fieldName,
        value,
        constraint: 'url',
        timestamp: Date.now(),
        recoverable: true,
      });
    }

    return { isValid: errors.length === 0, errors, warnings: [] };
  };
}

// ============================================================================
// DOMAIN-SPECIFIC VALIDATORS
// ============================================================================

/**
 * Validate room code format
 */
export function roomCode(fieldName = 'Room Code'): ValidatorFunction<string> {
  const roomCodeRegex = /^[a-z]+-[a-z]+-\d{4}$/;
  
  return (value: string) => {
    const errors: ValidationError[] = [];
    
    if (typeof value !== 'string') {
      errors.push({
        code: 'INVALID_TYPE',
        message: `${fieldName} must be a string`,
        field: fieldName,
        value,
        constraint: 'string',
        timestamp: Date.now(),
        recoverable: true,
      });
      return { isValid: false, errors, warnings: [] };
    }

    if (!roomCodeRegex.test(value)) {
      errors.push({
        code: 'INVALID_ROOM_CODE',
        message: `${fieldName} must be in format: word-word-1234`,
        field: fieldName,
        value,
        constraint: 'roomCode',
        timestamp: Date.now(),
        recoverable: true,
      });
    }

    return { isValid: errors.length === 0, errors, warnings: [] };
  };
}

/**
 * Validate message content
 */
export function messageContent(fieldName = 'Message'): ValidatorFunction<string> {
  return (value: string) => {
    const errors: ValidationError[] = [];
    const warnings: string[] = [];
    
    // Check basic requirements
    const lengthResult = stringLength(1, 2000, fieldName)(value);
    errors.push(...lengthResult.errors);
    warnings.push(...lengthResult.warnings);

    // Check for potentially harmful content
    const suspiciousPatterns = [
      /javascript:/i,
      /<script/i,
      /data:text\/html/i,
    ];

    if (suspiciousPatterns.some(pattern => pattern.test(value))) {
      errors.push({
        code: 'SUSPICIOUS_CONTENT',
        message: `${fieldName} contains potentially harmful content`,
        field: fieldName,
        value,
        constraint: 'safe',
        timestamp: Date.now(),
        recoverable: false,
      });
    }

    // Warning for excessive caps
    const capsRatio = (value.match(/[A-Z]/g) || []).length / value.length;
    if (capsRatio > 0.7 && value.length > 10) {
      warnings.push('Message contains excessive capital letters');
    }

    return { isValid: errors.length === 0, errors, warnings };
  };
}

// ============================================================================
// COMPOSITE VALIDATORS
// ============================================================================

/**
 * Combine multiple validators
 */
export function combine<T>(...validators: ValidatorFunction<T>[]): ValidatorFunction<T> {
  return (value: T) => {
    const allErrors: ValidationError[] = [];
    const allWarnings: string[] = [];
    
    for (const validator of validators) {
      const result = validator(value);
      allErrors.push(...result.errors);
      allWarnings.push(...result.warnings);
    }

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      warnings: [...new Set(allWarnings)], // Remove duplicates
    };
  };
}

/**
 * Conditional validator
 */
export function when<T>(
  condition: (value: T) => boolean,
  validator: ValidatorFunction<T>
): ValidatorFunction<T> {
  return (value: T) => {
    if (condition(value)) {
      return validator(value);
    }
    return { isValid: true, errors: [], warnings: [] };
  };
}

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Validate an object against a schema
 */
export function validateObject<T extends Record<string, any>>(
  obj: T,
  schema: Record<keyof T, ValidatorFunction>
): ValidationResult {
  const allErrors: ValidationError[] = [];
  const allWarnings: string[] = [];

  for (const [field, validator] of Object.entries(schema)) {
    const value = obj[field];
    const result = validator(value);
    
    allErrors.push(...result.errors);
    allWarnings.push(...result.warnings);
  }

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings,
  };
}

/**
 * Create a debounced validator
 */
export function debounceValidator<T>(
  validator: ValidatorFunction<T>,
  delay = 300
): ValidatorFunction<T> {
  let timeoutId: NodeJS.Timeout;
  
  return (value: T) => {
    return new Promise<ValidationResult>((resolve) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        resolve(validator(value));
      }, delay);
    }) as any; // Type assertion for compatibility
  };
}
