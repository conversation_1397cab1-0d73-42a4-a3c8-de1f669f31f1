import { useState, useEffect, useCallback, useRef } from 'react';
import { Room, RoomEvent, Track, RemoteParticipant, LocalParticipant, ConnectionQuality } from 'livekit-client';
import { useMutation, useAction } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { useAnonymousIdentity } from './useAnonymousIdentity';

export interface LiveKitVoiceState {
  isConnected: boolean;
  isConnecting: boolean;
  isMuted: boolean;
  isSpeaking: boolean;
  audioEnabled: boolean;
  connectionQuality: 'excellent' | 'good' | 'poor' | null;
  participants: LiveKitParticipant[];
  error: string | null;
}

export interface LiveKitParticipant {
  identity: string;
  name: string;
  isMuted: boolean;
  isSpeaking: boolean;
  audioEnabled: boolean;
  connectionQuality: ConnectionQuality;
}

export interface LiveKitConnectionInfo {
  token: string;
  identity: string;
  roomName: string;
  wsUrl: string;
  expiresAt: number;
}

export function useLiveKitVoice(roomId?: Id<'rooms'>) {
  const { identity } = useAnonymousIdentity();
  const [state, setState] = useState<LiveKitVoiceState>({
    isConnected: false,
    isConnecting: false,
    isMuted: true,
    isSpeaking: false,
    audioEnabled: false,
    connectionQuality: null,
    participants: [],
    error: null,
  });

  const roomRef = useRef<Room | null>(null);
  const localParticipantRef = useRef<LocalParticipant | null>(null);

  // Actions and Mutations
  const generateTokenAction = useAction(api.livekitActions.generateVoiceTokenForParticipant);
  const updateSpeakingMutation = useMutation(api.livekit.updateSpeakingStatus);

  // Clean up room connection
  const cleanup = useCallback(() => {
    if (roomRef.current) {
      roomRef.current.disconnect();
      roomRef.current = null;
    }
    localParticipantRef.current = null;

    // Clean up all remote audio elements
    const audioElements = document.querySelectorAll('audio[data-participant]');
    audioElements.forEach(element => {
      element.remove();
    });

    setState(prev => ({
      ...prev,
      isConnected: false,
      isConnecting: false,
      participants: [],
      connectionQuality: null,
    }));
  }, []);

  // Connect to LiveKit room
  const connect = useCallback(async (connectionInfo: LiveKitConnectionInfo) => {
    if (!roomId || !identity) {
      throw new Error('Room ID or identity not available');
    }

    try {
      setState(prev => ({ ...prev, isConnecting: true, error: null }));

      // Create room instance
      const room = new Room({
        adaptiveStream: true,
        dynacast: true,
        videoCaptureDefaults: {
          resolution: { width: 640, height: 480 },
        },
      });

      roomRef.current = room;

      // Set up event listeners
      room.on(RoomEvent.Connected, () => {
        console.log('Connected to LiveKit room');
        localParticipantRef.current = room.localParticipant;
        setState(prev => ({
          ...prev,
          isConnected: true,
          isConnecting: false,
          audioEnabled: true,
        }));
      });

      room.on(RoomEvent.Disconnected, (reason) => {
        console.log('Disconnected from LiveKit room:', reason);
        cleanup();
      });

      room.on(RoomEvent.ParticipantConnected, (participant: RemoteParticipant) => {
        console.log('Participant connected:', participant.identity);
        updateParticipantsList();
      });

      room.on(RoomEvent.ParticipantDisconnected, (participant: RemoteParticipant) => {
        console.log('Participant disconnected:', participant.identity);
        updateParticipantsList();
      });

      room.on(RoomEvent.TrackMuted, (track: Track, participant) => {
        console.log('Track muted:', track.kind, participant?.identity);
        updateParticipantsList();
      });

      room.on(RoomEvent.TrackUnmuted, (track: Track, participant) => {
        console.log('Track unmuted:', track.kind, participant?.identity);
        updateParticipantsList();
      });

      room.on(RoomEvent.ActiveSpeakersChanged, (speakers) => {
        const localIsSpeaking = speakers.some(s => s === room.localParticipant);
        setState(prev => ({ ...prev, isSpeaking: localIsSpeaking }));
        
        // Update speaking status in backend
        if (localIsSpeaking !== state.isSpeaking) {
          updateSpeakingMutation({
            roomId,
            participantSessionId: identity.sessionId,
            isSpeaking: localIsSpeaking,
          }).catch(console.error);
        }
        
        updateParticipantsList();
      });

      room.on(RoomEvent.ConnectionQualityChanged, (quality: ConnectionQuality, participant) => {
        if (participant === room.localParticipant) {
          const qualityLevel = quality === ConnectionQuality.Excellent ? 'excellent' :
                              quality === ConnectionQuality.Good ? 'good' : 'poor';
          setState(prev => ({ ...prev, connectionQuality: qualityLevel }));
        }
        updateParticipantsList();
      });

      // Handle incoming audio tracks from remote participants
      room.on(RoomEvent.TrackSubscribed, (track, publication, participant) => {
        console.log('Track subscribed:', track.kind, participant.identity);

        if (track.kind === Track.Kind.Audio) {
          // Create audio element for remote participant's audio
          const audioElement = track.attach() as HTMLAudioElement;
          audioElement.autoplay = true;
          audioElement.playsInline = true;

          // Set volume and ensure it plays
          audioElement.volume = 1.0;

          // Add to DOM (hidden) to ensure playback works
          audioElement.style.display = 'none';
          document.body.appendChild(audioElement);

          // Store reference for cleanup
          audioElement.setAttribute('data-participant', participant.identity);

          console.log(`Audio track attached for participant: ${participant.identity}`);
        }

        updateParticipantsList();
      });

      // Handle track unsubscription (cleanup audio elements)
      room.on(RoomEvent.TrackUnsubscribed, (track, publication, participant) => {
        console.log('Track unsubscribed:', track.kind, participant.identity);

        if (track.kind === Track.Kind.Audio) {
          // Find and remove the audio element
          const audioElements = document.querySelectorAll(`audio[data-participant="${participant.identity}"]`);
          audioElements.forEach(element => {
            element.remove();
          });

          console.log(`Audio track detached for participant: ${participant.identity}`);
        }

        updateParticipantsList();
      });

      // Connect to room
      await room.connect(connectionInfo.wsUrl, connectionInfo.token);

      // Enable audio by default but start muted
      await room.localParticipant.setMicrophoneEnabled(true);
      await room.localParticipant.setMicrophoneEnabled(false); // Start muted

    } catch (error) {
      console.error('Failed to connect to LiveKit room:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to connect to voice room';
      setState(prev => ({ 
        ...prev, 
        error: errorMessage, 
        isConnecting: false 
      }));
      cleanup();
      throw error;
    }
  }, [roomId, identity, state.isSpeaking, updateSpeakingMutation, cleanup]);

  // Update participants list
  const updateParticipantsList = useCallback(() => {
    if (!roomRef.current) return;

    const room = roomRef.current;
    const participants: LiveKitParticipant[] = [];

    // Add local participant
    if (room.localParticipant) {
      const audioTrack = room.localParticipant.getTrackPublication(Track.Source.Microphone);
      participants.push({
        identity: room.localParticipant.identity,
        name: room.localParticipant.name || room.localParticipant.identity,
        isMuted: audioTrack?.isMuted ?? true,
        isSpeaking: room.activeSpeakers.includes(room.localParticipant),
        audioEnabled: audioTrack?.isEnabled ?? false,
        connectionQuality: room.localParticipant.connectionQuality,
      });
    }

    // Add remote participants
    room.remoteParticipants.forEach((participant) => {
      const audioTrack = participant.getTrackPublication(Track.Source.Microphone);
      participants.push({
        identity: participant.identity,
        name: participant.name || participant.identity,
        isMuted: audioTrack?.isMuted ?? true,
        isSpeaking: room.activeSpeakers.includes(participant),
        audioEnabled: audioTrack?.isEnabled ?? false,
        connectionQuality: participant.connectionQuality,
      });
    });

    setState(prev => ({ ...prev, participants }));
  }, []);

  // Join voice room
  const joinVoiceRoom = useCallback(async () => {
    if (!roomId || !identity) {
      throw new Error('Room ID or identity not available');
    }

    try {
      // Generate access token using the action
      const connectionInfo = await generateTokenAction({
        roomId,
        participantSessionId: identity.sessionId,
      });

      // Connect to LiveKit
      await connect(connectionInfo);

    } catch (error) {
      console.error('Failed to join voice room:', error);
      throw error;
    }
  }, [roomId, identity, generateTokenAction, connect]);

  // Leave voice room
  const leaveVoiceRoom = useCallback(async () => {
    cleanup();
  }, [cleanup]);

  // Toggle mute
  const toggleMute = useCallback(async () => {
    if (!roomRef.current?.localParticipant) {
      throw new Error('Not connected to voice room');
    }

    try {
      const currentlyMuted = state.isMuted;
      const newMutedState = !currentlyMuted;

      // Enable/disable microphone (note: setMicrophoneEnabled(true) means unmuted)
      await roomRef.current.localParticipant.setMicrophoneEnabled(!newMutedState);

      // Update state
      setState(prev => ({ ...prev, isMuted: newMutedState }));

      console.log(`Microphone ${newMutedState ? 'muted' : 'unmuted'}`);
      return newMutedState;
    } catch (error) {
      console.error('Failed to toggle mute:', error);
      throw error;
    }
  }, [state.isMuted]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return {
    state,
    joinVoiceRoom,
    leaveVoiceRoom,
    toggleMute,
    clearError: () => setState(prev => ({ ...prev, error: null })),
  };
}
