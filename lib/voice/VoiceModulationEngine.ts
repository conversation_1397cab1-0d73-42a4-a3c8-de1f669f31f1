/**
 * VeilMind Voice Modulation Engine
 * Real-time voice transformation for complete anonymity
 */

export interface VoiceModulationSettings {
  // Pitch shifting (-12 to +12 semitones)
  pitchShift: number;
  
  // Formant shifting (0.5 to 2.0 for gender/age anonymity)
  formantShift: number;
  
  // Tone modifications
  warmth: number; // -1 to 1 (bass to treble)
  breathiness: number; // 0 to 1
  resonance: number; // -1 to 1 (chest to head voice)
  
  // Special effects
  roboticFilter: number; // 0 to 1
  
  // Noise gate
  noiseGate: number; // dB (-60 to 0)
}

export interface VoicePreset {
  name: string;
  description: string;
  settings: VoiceModulationSettings;
}

export const VOICE_PRESETS: VoicePreset[] = [
  {
    name: 'Natural',
    description: 'Minimal processing, natural voice',
    settings: {
      pitchShift: 0,
      formantShift: 1.0,
      warmth: 0,
      breathiness: 0,
      resonance: 0,
      roboticFilter: 0,
      noiseGate: -40
    }
  },
  {
    name: 'Deeper Voice',
    description: 'Lower pitch, warmer tone',
    settings: {
      pitchShift: -6,
      formantShift: 0.8,
      warmth: 0.3,
      breathiness: 0,
      resonance: -0.2,
      roboticFilter: 0,
      noiseGate: -40
    }
  },
  {
    name: 'Higher Voice',
    description: 'Higher pitch, brighter tone',
    settings: {
      pitchShift: 5,
      formantShift: 1.3,
      warmth: -0.2,
      breathiness: 0.1,
      resonance: 0.3,
      roboticFilter: 0,
      noiseGate: -40
    }
  },
  {
    name: 'Neutral Shift',
    description: 'Subtle anonymization',
    settings: {
      pitchShift: 2,
      formantShift: 1.1,
      warmth: 0.1,
      breathiness: 0.05,
      resonance: 0,
      roboticFilter: 0,
      noiseGate: -35
    }
  },
  {
    name: 'Whisper Mode',
    description: 'Optimized for quiet speech',
    settings: {
      pitchShift: 1,
      formantShift: 1.0,
      warmth: 0,
      breathiness: 0.4,
      resonance: 0,
      roboticFilter: 0,
      noiseGate: -50
    }
  },
  {
    name: 'Robotic',
    description: 'Complete anonymity with sci-fi effect',
    settings: {
      pitchShift: -2,
      formantShift: 0.9,
      warmth: -0.5,
      breathiness: 0,
      resonance: 0,
      roboticFilter: 0.7,
      noiseGate: -30
    }
  }
];

export class VoiceModulationEngine {
  private audioContext: AudioContext | null = null;
  private workletNode: AudioWorkletNode | null = null;
  private inputStream: MediaStream | null = null;
  private outputStream: MediaStream | null = null;
  private sourceNode: MediaStreamAudioSourceNode | null = null;
  private destinationNode: MediaStreamAudioDestinationNode | null = null;
  
  private currentSettings: VoiceModulationSettings;
  private isInitialized = false;
  private isProcessing = false;
  
  constructor() {
    this.currentSettings = VOICE_PRESETS[0].settings; // Start with natural preset
  }
  
  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    try {
      // Create audio context
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      // Load the voice modulator worklet
      await this.audioContext.audioWorklet.addModule('/voice-modulator-worklet.js');
      
      // Create worklet node
      this.workletNode = new AudioWorkletNode(this.audioContext, 'voice-modulator');
      
      // Create destination for output stream
      this.destinationNode = this.audioContext.createMediaStreamDestination();
      this.workletNode.connect(this.destinationNode);
      
      this.isInitialized = true;
      console.log('Voice Modulation Engine initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Voice Modulation Engine:', error);
      throw error;
    }
  }
  
  async startProcessing(inputStream: MediaStream): Promise<MediaStream> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    if (!this.audioContext || !this.workletNode || !this.destinationNode) {
      throw new Error('Voice Modulation Engine not properly initialized');
    }
    
    try {
      // Store input stream
      this.inputStream = inputStream;
      
      // Create source node from input stream
      this.sourceNode = this.audioContext.createMediaStreamSource(inputStream);
      
      // Connect source to worklet
      this.sourceNode.connect(this.workletNode);
      
      // Get output stream
      this.outputStream = this.destinationNode.stream;
      
      // Apply current settings
      this.updateSettings(this.currentSettings);
      
      this.isProcessing = true;
      console.log('Voice processing started');
      
      return this.outputStream;
    } catch (error) {
      console.error('Failed to start voice processing:', error);
      throw error;
    }
  }
  
  stopProcessing(): void {
    if (this.sourceNode) {
      this.sourceNode.disconnect();
      this.sourceNode = null;
    }
    
    if (this.inputStream) {
      this.inputStream.getTracks().forEach(track => track.stop());
      this.inputStream = null;
    }
    
    this.outputStream = null;
    this.isProcessing = false;
    
    console.log('Voice processing stopped');
  }
  
  updateSettings(settings: Partial<VoiceModulationSettings>): void {
    this.currentSettings = { ...this.currentSettings, ...settings };
    
    if (this.workletNode && this.isProcessing) {
      this.workletNode.port.postMessage({
        type: 'updateParameters',
        data: this.currentSettings
      });
    }
  }
  
  applyPreset(presetName: string): void {
    const preset = VOICE_PRESETS.find(p => p.name === presetName);
    if (preset) {
      this.updateSettings(preset.settings);
      console.log(`Applied voice preset: ${preset.name}`);
    } else {
      console.warn(`Voice preset not found: ${presetName}`);
    }
  }
  
  getCurrentSettings(): VoiceModulationSettings {
    return { ...this.currentSettings };
  }
  
  getAvailablePresets(): VoicePreset[] {
    return [...VOICE_PRESETS];
  }
  
  isReady(): boolean {
    return this.isInitialized;
  }
  
  isActive(): boolean {
    return this.isProcessing;
  }
  
  getOutputStream(): MediaStream | null {
    return this.outputStream;
  }

  getAudioContext(): AudioContext | null {
    return this.audioContext;
  }

  // Utility methods for common adjustments
  setPitchShift(semitones: number): void {
    this.updateSettings({ pitchShift: Math.max(-12, Math.min(12, semitones)) });
  }
  
  setFormantShift(multiplier: number): void {
    this.updateSettings({ formantShift: Math.max(0.5, Math.min(2.0, multiplier)) });
  }
  
  setWarmth(level: number): void {
    this.updateSettings({ warmth: Math.max(-1, Math.min(1, level)) });
  }
  
  setBreathiness(level: number): void {
    this.updateSettings({ breathiness: Math.max(0, Math.min(1, level)) });
  }
  
  setResonance(level: number): void {
    this.updateSettings({ resonance: Math.max(-1, Math.min(1, level)) });
  }
  
  setRoboticFilter(level: number): void {
    this.updateSettings({ roboticFilter: Math.max(0, Math.min(1, level)) });
  }
  
  setNoiseGate(dbLevel: number): void {
    this.updateSettings({ noiseGate: Math.max(-60, Math.min(0, dbLevel)) });
  }
  
  // Cleanup
  dispose(): void {
    this.stopProcessing();
    
    if (this.workletNode) {
      this.workletNode.disconnect();
      this.workletNode = null;
    }
    
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
      this.audioContext = null;
    }
    
    this.destinationNode = null;
    this.isInitialized = false;
    
    console.log('Voice Modulation Engine disposed');
  }
}
