'use client';

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Mic,
  MicOff,
  Phone,
  PhoneOff,
  Volume2,
  VolumeX,
  Settings,
  WifiOff,
  Signal,
  Loader2,
  AlertCircle,
  Headphones,
  Users,
  Clock,
  Maximize2,
  Minimize2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useViewport } from '@/hooks/useViewport';
// Voice functionality removed - keeping only the voice button for future implementation

interface MediaState {
  audio: boolean;
  call: boolean;
  speaker: boolean;
}

interface MediaControlsProps {
  mediaState: MediaState;
  onToggleAudio: () => void;
  onToggleCall: () => void;
  onToggleSpeaker: () => void;
  isConnected?: boolean;
  connectionQuality?: 'excellent' | 'good' | 'poor' | 'disconnected';
  participantCount?: number;
  disabled?: boolean;
  className?: string;
  callDuration?: number; // in seconds
  isFullscreen?: boolean;
  onToggleFullscreen?: () => void;
  // Voice functionality removed - keeping interface for future implementation
  onVoiceToggle?: () => void;
  // Keyboard shortcuts
  enableKeyboardShortcuts?: boolean;
}

interface ConnectionIndicatorProps {
  quality: 'excellent' | 'good' | 'poor' | 'disconnected';
  className?: string;
}

interface CallTimerProps {
  duration: number; // in seconds
  className?: string;
}

function CallTimer({ duration, className }: CallTimerProps) {
  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className={cn("flex items-center gap-1 text-xs text-muted-foreground", className)}>
      <Clock className="h-3 w-3" />
      <span className="font-mono">{formatTime(duration)}</span>
    </div>
  );
}

function ConnectionIndicator({ quality, className }: ConnectionIndicatorProps) {
  const getIndicatorProps = () => {
    switch (quality) {
      case 'excellent':
        return {
          color: 'text-green-500',
          bgColor: 'bg-green-500',
          icon: Signal,
          label: 'Excellent connection',
          bars: 4
        };
      case 'good':
        return {
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-500',
          icon: Signal,
          label: 'Good connection',
          bars: 3
        };
      case 'poor':
        return {
          color: 'text-orange-500',
          bgColor: 'bg-orange-500',
          icon: Signal,
          label: 'Poor connection',
          bars: 2
        };
      case 'disconnected':
        return {
          color: 'text-red-500',
          bgColor: 'bg-red-500',
          icon: WifiOff,
          label: 'Disconnected',
          bars: 0
        };
    }
  };

  const { color, bgColor, icon: Icon, label, bars } = getIndicatorProps();

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={cn("flex items-center gap-1", className)}>
            <Icon className={cn("h-3 w-3", color)} />
            <div className="flex gap-0.5">
              {[1, 2, 3, 4].map((bar) => (
                <div
                  key={bar}
                  className={cn(
                    "w-0.5 h-2 rounded-full transition-colors duration-200",
                    bar <= bars ? bgColor : 'bg-muted'
                  )}
                />
              ))}
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p className="text-xs">{label}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Custom hook for keyboard shortcuts
function useKeyboardShortcuts(
  enabled: boolean,
  callbacks: {
    onToggleAudio: () => void;
    onToggleSpeaker: () => void;
    onToggleFullscreen?: () => void;
  }
) {
  useEffect(() => {
    if (!enabled) return;

    const handleKeyPress = (event: KeyboardEvent) => {
      // Don't trigger shortcuts if user is typing in an input
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (event.key.toLowerCase()) {
        case 'm':
          event.preventDefault();
          callbacks.onToggleAudio();
          break;
        case ' ':
          event.preventDefault();
          callbacks.onToggleSpeaker();
          break;
        case 'f':
          if (callbacks.onToggleFullscreen) {
            event.preventDefault();
            callbacks.onToggleFullscreen();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [enabled, callbacks]);
}

export function MediaControls({
  mediaState,
  onToggleAudio,
  onToggleCall,
  onToggleSpeaker,
  isConnected = true,
  connectionQuality = 'good',
  participantCount = 0,
  disabled = false,
  className,
  callDuration,
  isFullscreen = false,
  onToggleFullscreen,
  onVoiceToggle,
  enableKeyboardShortcuts = true
}: MediaControlsProps) {
  const { isMobile } = useViewport();
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const [audioLevel, setAudioLevel] = useState(0);
  const audioLevelRef = useRef<number>(0);

  // Set up keyboard shortcuts
  useKeyboardShortcuts(enableKeyboardShortcuts && !disabled, {
    onToggleAudio,
    onToggleSpeaker,
    onToggleFullscreen
  });

  // Simulate audio level monitoring (in real implementation, this would come from WebRTC)
  useEffect(() => {
    if (!mediaState.audio || !mediaState.call) {
      setAudioLevel(0);
      return;
    }

    const interval = setInterval(() => {
      // Simulate audio level changes
      const newLevel = Math.random() * 100;
      audioLevelRef.current = newLevel;
      setAudioLevel(newLevel);
    }, 100);

    return () => clearInterval(interval);
  }, [mediaState.audio, mediaState.call]);

  // Voice functionality removed - keeping placeholder for future implementation
  const handleVoiceToggle = useCallback(async () => {
    if (onVoiceToggle) {
      onVoiceToggle();
    }
  }, [onVoiceToggle]);

  const handleMediaToggle = useCallback(async (
    action: () => void,
    type: string
  ) => {
    if (disabled) return;

    try {
      setIsLoading(type);
      await new Promise(resolve => setTimeout(resolve, 200));
      action();
    } catch (error) {
      // Handle error silently
    } finally {
      setIsLoading(null);
    }
  }, [disabled]);

  // Use regular audio controls (voice functionality removed)
  const audioActive = mediaState.audio;

  const controlButtons: Array<{
    key: string;
    active: boolean;
    icon: any;
    label: string;
    onClick: () => void;
    variant: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
    shortcut: string;
    showAudioLevel?: boolean;
    hidden?: boolean;
  }> = [
    {
      key: 'audio',
      active: audioActive,
      icon: audioActive ? Mic : MicOff,
      label: audioActive ? 'Mute microphone' : 'Unmute microphone',
      onClick: () => handleMediaToggle(onToggleAudio, 'audio'),
      variant: audioActive ? 'default' : 'outline',
      shortcut: 'M',
      showAudioLevel: audioActive && mediaState.call
    },

    {
      key: 'speaker',
      active: mediaState.speaker,
      icon: mediaState.speaker ? Volume2 : VolumeX,
      label: mediaState.speaker ? 'Mute speaker' : 'Unmute speaker',
      onClick: () => handleMediaToggle(onToggleSpeaker, 'speaker'),
      variant: mediaState.speaker ? 'default' : 'outline',
      shortcut: 'Space'
    }
  ];

  return (
    <Card className={cn("border-border bg-card/50 backdrop-blur-sm py-2.5 shadow-none", className)}>
      <CardContent className="px-2.5 py-0">
        <div className="flex items-center justify-between gap-2.5">
          {/* Media Controls */}
          <div className="flex items-center gap-2">
            {controlButtons
              .filter(button => !button.hidden)
              .map((button) => {
                const Icon = button.icon;
                const isCurrentlyLoading = isLoading === button.key;
                
                return (
                  <TooltipProvider key={button.key}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant={button.variant}
                          size={isMobile ? "sm" : "default"}
                          onClick={button.onClick}
                          disabled={disabled || isCurrentlyLoading}
                          className={cn(
                            "relative transition-all duration-200",
                            isMobile ? "h-9 w-9 p-0" : "h-10 w-10 p-0",
                            button.active && "ring-2 ring-primary/20",
                            !isConnected && "opacity-50"
                          )}
                        >
                          {isCurrentlyLoading ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Icon className="h-4 w-4" />
                          )}
                          
                          {/* Active indicator */}
                          {button.active && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background"
                            />
                          )}

                          {/* Audio level indicator */}
                          {button.showAudioLevel && audioLevel > 10 && (
                            <motion.div
                              className="absolute inset-0 rounded-md bg-green-500/20 border border-green-500/30"
                              initial={{ opacity: 0 }}
                              animate={{ 
                                opacity: Math.min(audioLevel / 100, 0.8),
                                scale: 1 + (audioLevel / 1000)
                              }}
                              transition={{ duration: 0.1 }}
                            />
                          )}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="text-center">
                          <p className="text-xs">{button.label}</p>
                          {!isMobile && enableKeyboardShortcuts && (
                            <p className="text-xs text-muted-foreground mt-1">
                              Press {button.shortcut === 'Space' ? 'Spacebar' : button.shortcut}
                            </p>
                          )}
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                );
              })}

            {/* Fullscreen Control */}
            {onToggleFullscreen && !isMobile && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleMediaToggle(onToggleFullscreen, 'fullscreen')}
                      disabled={disabled || isLoading === 'fullscreen'}
                      className="h-9 w-9 p-0"
                    >
                      {isLoading === 'fullscreen' ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : isFullscreen ? (
                        <Minimize2 className="h-4 w-4" />
                      ) : (
                        <Maximize2 className="h-4 w-4" />
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="text-center">
                      <p className="text-xs">
                        {isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
                      </p>
                      {enableKeyboardShortcuts && (
                        <p className="text-xs text-muted-foreground mt-1">Press F</p>
                      )}
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}

            {/* Call Control */}
            <div className="ml-2 pl-2 border-l border-border">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={mediaState.call ? "destructive" : "default"}
                      size={isMobile ? "sm" : "default"}
                      onClick={() => handleMediaToggle(onToggleCall, 'call')}
                      disabled={disabled || isLoading === 'call'}
                      className={cn(
                        "transition-all duration-200",
                        isMobile ? "h-9 w-9 p-0" : "h-10 w-10 p-0"
                      )}
                    >
                      {isLoading === 'call' ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : mediaState.call ? (
                        <PhoneOff className="h-4 w-4" />
                      ) : (
                        <Headphones className="h-4 w-4" />
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">
                      {mediaState.call ? 'Leave voice' : 'Join voice'}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>

          {/* Status & Settings */}
          <div className="flex items-center gap-3">
            {/* Call Duration */}
            {callDuration !== undefined && callDuration > 0 && (
              <CallTimer duration={callDuration} />
            )}

            {/* Connection Status */}
            <div className="flex items-center gap-2">
              <ConnectionIndicator quality={connectionQuality} />
              
              {participantCount > 0 && (
                <Badge variant="secondary" className="text-xs flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  {participantCount}
                </Badge>
              )}
            </div>

            {/* Settings Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  disabled={disabled}
                >
                  <Settings className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                {/* Voice modulation removed - keeping placeholder for future implementation */}
                <DropdownMenuItem>
                  <Mic className="mr-2 h-4 w-4" />
                  Microphone Settings
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Volume2 className="mr-2 h-4 w-4" />
                  Audio Settings
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Signal className="mr-2 h-4 w-4" />
                  Connection Info
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Connection Warning */}
        <AnimatePresence>
          {!isConnected && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-3 pt-3 border-t border-border"
            >
              <div className="flex items-center gap-2 text-sm text-orange-600 dark:text-orange-400">
                <AlertCircle className="h-4 w-4 animate-pulse" />
                <span>Connection issues detected. Some features may be limited.</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Voice modulation panel removed - keeping placeholder for future implementation */}
      </CardContent>
    </Card>
  );
}

// Compact version for mobile bottom bar
export function CompactMediaControls({
  mediaState,
  onToggleAudio,
  onToggleCall,
  disabled = false,
  className,
  participantCount = 0
}: Pick<MediaControlsProps, 'mediaState' | 'onToggleAudio' | 'onToggleCall' | 'disabled' | 'className' | 'participantCount'>) {
  return (
    <div className={cn("flex items-center justify-between gap-2 px-4 py-2 bg-card/90 backdrop-blur-sm border-t border-border", className)}>
      <div className="flex items-center gap-2">
        <Button
          variant={mediaState.audio ? "default" : "outline"}
          size="sm"
          onClick={onToggleAudio}
          disabled={disabled}
          className="h-9 w-9 p-0"
        >
          {mediaState.audio ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
        </Button>
      </div>

      {participantCount > 0 && (
        <Badge variant="secondary" className="text-xs flex items-center gap-1">
          <Users className="h-3 w-3" />
          {participantCount}
        </Badge>
      )}

      <Button
        variant={mediaState.call ? "destructive" : "default"}
        size="sm"
        onClick={onToggleCall}
        disabled={disabled}
        className="h-9 w-9 p-0"
      >
        {mediaState.call ? <PhoneOff className="h-4 w-4" /> : <Phone className="h-4 w-4" />}
      </Button>
    </div>
  );
}