"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Users, 
  Building2, 
  Heart, 
  GraduationCap,
  ArrowRight,
  Clock,
  Shield
} from "lucide-react";

interface Room {
  id: string;
  title: string;
  description: string;
  participants: number;
  category: string;
  tags: string[];
  icon: React.ElementType;
  isActive: boolean;
  duration: string;
}

export const RoomDiscoverySection: React.FC = () => {
  const featuredRooms: Room[] = [
    {
      id: "tech-feedback",
      title: "Tech Industry Feedback",
      description: "Workplace culture discussions and honest feedback about tech companies",
      participants: 24,
      category: "Corporate",
      tags: ["Tech", "Workplace", "Culture"],
      icon: Building2,
      isActive: true,
      duration: "2h 15m"
    },
    {
      id: "mental-health",
      title: "Mental Health Support",
      description: "Safe space for sharing experiences and peer support",
      participants: 18,
      category: "Healthcare",
      tags: ["Mental Health", "Support", "Wellness"],
      icon: Heart,
      isActive: true,
      duration: "1h 45m"
    },
    {
      id: "student-voice",
      title: "Student Voice",
      description: "Campus issues, improvements, and student experiences",
      participants: 31,
      category: "Education",
      tags: ["Education", "Students", "Campus"],
      icon: GraduationCap,
      isActive: true,
      duration: "3h 20m"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <section id="discover-rooms" className="py-24 bg-muted/30">
      <div className="container mx-auto px-4">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="max-w-6xl mx-auto"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
              Discover Active Conversations
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Join ongoing discussions or explore what others are talking about. 
              All conversations are completely anonymous and secure.
            </p>
          </motion.div>

          {/* Featured Rooms Grid */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12"
          >
            {featuredRooms.map((room, index) => (
              <motion.div key={room.id} variants={itemVariants}>
                <Card className="h-full hover:shadow-lg transition-all duration-300 border-muted/50 bg-card/80 backdrop-blur-sm group">
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="p-3 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                        <room.icon className="h-6 w-6 text-primary" />
                      </div>
                      <div className="flex items-center gap-2">
                        {room.isActive && (
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                            <span className="text-xs text-green-600 dark:text-green-400 font-medium">
                              Live
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                    <CardTitle className="text-xl mb-2">{room.title}</CardTitle>
                    <p className="text-muted-foreground text-sm leading-relaxed">
                      {room.description}
                    </p>
                  </CardHeader>
                  
                  <CardContent className="pt-0">
                    {/* Room Stats */}
                    <div className="flex items-center justify-between mb-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4" />
                        <span>{room.participants} participants</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        <span>{room.duration}</span>
                      </div>
                    </div>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {room.tags.map((tag, tagIndex) => (
                        <Badge key={tagIndex} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>

                    {/* Category Badge */}
                    <div className="flex items-center justify-between">
                      <Badge variant="outline" className="text-xs">
                        {room.category}
                      </Badge>
                      <Button size="sm" variant="ghost" className="group/btn">
                        Join Room
                        <ArrowRight className="ml-1 h-3 w-3 group-hover/btn:translate-x-1 transition-transform" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {/* Privacy Notice */}
          <motion.div
            variants={itemVariants}
            className="text-center bg-card/50 backdrop-blur-sm border border-muted/50 rounded-lg p-6"
          >
            <div className="flex items-center justify-center gap-2 mb-3">
              <Shield className="h-5 w-5 text-primary" />
              <span className="font-semibold">Privacy Guaranteed</span>
            </div>
            <p className="text-muted-foreground text-sm max-w-2xl mx-auto">
              All rooms use end-to-end encryption and AI-generated anonymous identities. 
              No personal information is collected or stored. Conversations are automatically 
              deleted after sessions end.
            </p>
          </motion.div>

          {/* Browse All Rooms CTA */}
          <motion.div variants={itemVariants} className="text-center mt-12">
            <Button size="lg" variant="outline" className="group">
              Browse All Public Rooms
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};
