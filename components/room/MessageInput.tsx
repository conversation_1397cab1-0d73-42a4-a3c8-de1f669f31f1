'use client';

import { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useViewport } from '@/hooks/useViewport';
import { cn } from '@/lib/utils';
import {
  Send,
  Smile,
  Mic,
  MicOff,
  Loader2,
  X,
  Reply,
  AtSign
} from 'lucide-react';
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { useTypingIndicator } from '@/hooks/useAnonymousIdentity';
import { debounce } from '@/lib/identity';
import { toast } from 'sonner';
import { Id } from '@/convex/_generated/dataModel';

interface MessageInputProps {
  roomId: Id<'rooms'>;
  currentParticipant?: {
    sessionId: string;
    anonymousAlias: string;
    avatarSeed: string;
    joinedAt: number;
    messageCount: number;
    isActive: boolean;
    isMuted: boolean;
  };
  participants?: Array<{
    sessionId: string;
    anonymousAlias: string;
    avatarSeed: string;
    isActive: boolean;
  }>;
  disabled?: boolean;
  placeholder?: string;
  onMessageSent?: () => void;
  replyingTo?: {
    messageId: Id<'messages'>;
    content: string;
    author: string;
  } | null;
  onCancelReply?: () => void;
}

const EMOJI_CATEGORIES = {
  'Smileys': ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳'],
  'Gestures': ['👍', '👎', '👌', '🤌', '🤏', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', '👏', '🙌', '👐', '🤲', '🤝', '🙏'],
  'Hearts': ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝'],
  'Objects': ['💯', '💢', '💥', '💫', '💦', '💨', '🕳️', '💣', '💬', '👁️‍🗨️', '🗨️', '🗯️', '💭', '💤']
};

export function MessageInput({
  roomId,
  currentParticipant,
  participants = [],
  disabled = false,
  placeholder = "Type your message...",
  onMessageSent,
  replyingTo,
  onCancelReply
}: MessageInputProps) {
  const [message, setMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [showMentionSuggestions, setShowMentionSuggestions] = useState(false);
  const [mentionQuery, setMentionQuery] = useState('');
  const [mentionStartPos, setMentionStartPos] = useState(0);
  const [isExpanded, setIsExpanded] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const sendMessageMutation = useMutation(api.messages.sendMessage);
  const [isSending, setIsSending] = useState(false);

  const { startTyping, stopTyping } = useTypingIndicator(roomId);
  const { isMobile, isTablet } = useViewport();
  const isCompact = isMobile || isTablet;

  // Debounced typing indicator
  const debouncedStopTyping = useCallback(
    debounce(() => stopTyping(), 1000),
    [stopTyping]
  );

  // Handle mention detection
  const detectMentions = (text: string, cursorPos: number) => {
    const beforeCursor = text.slice(0, cursorPos);
    const mentionMatch = beforeCursor.match(/@(\w*)$/);

    if (mentionMatch) {
      const query = mentionMatch[1].toLowerCase();
      const startPos = cursorPos - mentionMatch[0].length;

      setMentionQuery(query);
      setMentionStartPos(startPos);
      setShowMentionSuggestions(true);
    } else {
      setShowMentionSuggestions(false);
      setMentionQuery('');
    }
  };

  const handleInputChange = (value: string) => {
    setMessage(value);

    // Handle mention detection
    if (textareaRef.current) {
      detectMentions(value, textareaRef.current.selectionStart || 0);
    }

    // Handle typing indicators
    if (value.trim() && !disabled) {
      startTyping();
      debouncedStopTyping();
    } else {
      stopTyping();
    }

    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      const maxHeight = isCompact ? (isExpanded ? 150 : 100) : 120;
      const newHeight = Math.min(textareaRef.current.scrollHeight, maxHeight);
      textareaRef.current.style.height = `${newHeight}px`;
      
      // Auto-expand on mobile if content is getting long
      if (isCompact && textareaRef.current.scrollHeight > 100 && !isExpanded) {
        setIsExpanded(true);
      }
    }
  };

  // Filter participants for mention suggestions
  const mentionSuggestions = participants
    .filter(p =>
      p.isActive &&
      p.sessionId !== currentParticipant?.sessionId &&
      p.anonymousAlias.toLowerCase().includes(mentionQuery)
    )
    .slice(0, 5);

  // Insert mention
  const insertMention = (participant: typeof participants[0]) => {
    const beforeMention = message.slice(0, mentionStartPos);
    const afterMention = message.slice(textareaRef.current?.selectionStart || 0);
    const newMessage = beforeMention + `@${participant.anonymousAlias} ` + afterMention;

    setMessage(newMessage);
    setShowMentionSuggestions(false);
    setMentionQuery('');

    // Focus back to textarea
    setTimeout(() => {
      textareaRef.current?.focus();
      const newCursorPos = mentionStartPos + participant.anonymousAlias.length + 2;
      textareaRef.current?.setSelectionRange(newCursorPos, newCursorPos);
    }, 0);
  };

  const handleSendMessage = async () => {
    if (!message.trim() || !currentParticipant || disabled || isSending) return;

    const messageContent = message.trim();
    setMessage('');
    setIsSending(true);
    setIsExpanded(false);

    // Stop typing indicator
    stopTyping();

    // Hide mention suggestions
    setShowMentionSuggestions(false);

    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }

    try {
      await sendMessageMutation({
        roomId,
        participantSessionId: currentParticipant.sessionId,
        content: messageContent,
        messageType: 'text',
        replyToMessageId: replyingTo?.messageId,
      });

      // Clear reply state after successful send
      onCancelReply?.();
      onMessageSent?.();
    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message');
      // Restore message on error
      setMessage(messageContent);
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Handle mention suggestions navigation
    if (showMentionSuggestions && mentionSuggestions.length > 0) {
      if (e.key === 'Escape') {
        e.preventDefault();
        setShowMentionSuggestions(false);
        return;
      }
      if (e.key === 'Tab' || e.key === 'Enter') {
        e.preventDefault();
        insertMention(mentionSuggestions[0]);
        return;
      }
    }

    // Handle reply cancellation
    if (e.key === 'Escape' && replyingTo) {
      e.preventDefault();
      onCancelReply?.();
      return;
    }

    // Handle message sending
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const insertEmoji = (emoji: string) => {
    const newMessage = message + emoji;
    setMessage(newMessage);
    textareaRef.current?.focus();
  };

  const handleVoiceRecord = () => {
    // TODO: Implement voice recording
    setIsRecording(!isRecording);
    toast.info('Voice recording coming soon!');
  };



  if (disabled) {
    return (
      <div className="flex items-center justify-center p-4 text-sm text-muted-foreground bg-muted/30 rounded-lg mx-2">
        You need to join the room to send messages
      </div>
    );
  }

  return (
    <div className="p-2.5 pb-safe space-y-2.5">
      {/* Reply Context */}
      {replyingTo && (
        <div className="bg-muted/50 rounded-lg border-l-4 border-primary p-2.5">
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <Reply className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs font-medium text-muted-foreground">
                  Replying to {replyingTo.author}
                </span>
              </div>
              <div className={cn(
                "text-muted-foreground line-clamp-2",
                isCompact ? "text-xs" : "text-sm"
              )}>
                {replyingTo.content.length > (isCompact ? 50 : 100)
                  ? replyingTo.content.substring(0, isCompact ? 50 : 100) + '...'
                  : replyingTo.content
                }
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="shrink-0 p-1 h-6 w-6"
              onClick={onCancelReply}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )}

      {/* Message Input */}
      <div className="flex items-end gap-2.5 relative">
        {/* Desktop Emoji Picker */}
        {!isCompact && (
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="sm" className="shrink-0 min-h-[44px]">
                <Smile className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-2.5" align="start">
              <div className="max-h-64 overflow-y-auto">
                <div className="space-y-2.5">
                  {Object.entries(EMOJI_CATEGORIES).map(([category, emojis]) => (
                    <div key={category}>
                      <div className="text-xs font-medium text-muted-foreground mb-1">
                        {category}
                      </div>
                      <div className="grid grid-cols-8 gap-1">
                        {emojis.map((emoji) => (
                          <Button
                            key={emoji}
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-muted"
                            onClick={() => insertEmoji(emoji)}
                          >
                            {emoji}
                          </Button>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </PopoverContent>
          </Popover>
        )}

        {/* Mobile Emoji Button */}
        {isCompact && (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="shrink-0 h-10 w-10 p-0 rounded-lg"
              >
                <Smile className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-72 p-2.5" align="start" side="top">
              <div className="max-h-64 overflow-y-auto">
                <div className="space-y-2.5">
                  {Object.entries(EMOJI_CATEGORIES).map(([category, emojis]) => (
                    <div key={category}>
                      <div className="text-xs font-medium text-muted-foreground mb-1">
                        {category}
                      </div>
                      <div className="grid grid-cols-6 gap-1">
                        {emojis.map((emoji) => (
                          <Button
                            key={emoji}
                            variant="ghost"
                            size="sm"
                            className="h-10 w-10 p-0 hover:bg-muted text-lg"
                            onClick={() => insertEmoji(emoji)}
                          >
                            {emoji}
                          </Button>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </PopoverContent>
          </Popover>
        )}

        {/* Text Input */}
        <div className="flex-1 relative">
          <Textarea
            ref={textareaRef}
            value={message}
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={replyingTo ? `Reply to ${replyingTo.author}...` : placeholder}
            className={cn(
              "resize-none px-3 py-2.5",
              isCompact
                ? "min-h-[40px] text-base leading-5"
                : "min-h-[44px] text-sm",
              isExpanded && "max-h-[150px]",
              !isExpanded && isCompact ? "max-h-[80px]" : "max-h-[100px]"
            )}
            disabled={isSending}
            rows={1}
          />

          {/* Mention Suggestions */}
          {showMentionSuggestions && mentionSuggestions.length > 0 && (
            <div className="absolute bottom-full left-0 right-0 mb-2.5 bg-background border border-border rounded-lg shadow-lg z-50 max-h-40 overflow-y-auto">
              {mentionSuggestions.map((participant) => (
                <button
                  key={participant.sessionId}
                  className="w-full px-3 py-2.5 text-left hover:bg-muted transition-colors flex items-center gap-2"
                  onClick={() => insertMention(participant)}
                >
                  <AtSign className="h-3 w-3 text-muted-foreground" />
                  <span className="text-sm">{participant.anonymousAlias}</span>
                </button>
              ))}
            </div>
          )}

          {/* Character count - only show when approaching limit */}
          {message.length > 800 && (
            <div className={cn(
              "absolute text-xs text-muted-foreground bottom-2.5 right-2.5",
              message.length > 900 && "text-orange-500",
              message.length > 950 && "text-red-500"
            )}>
              {message.length}/1000
            </div>
          )}
        </div>

        {/* Voice Button */}
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "shrink-0",
            isCompact ? "h-10 w-10 p-0 rounded-lg" : "min-h-[44px]"
          )}
          onClick={handleVoiceRecord}
        >
          {isRecording ? (
            <MicOff className={cn(
              "text-red-500",
              isCompact ? "h-4 w-4" : "h-4 w-4"
            )} />
          ) : (
            <Mic className={cn(
              isCompact ? "h-4 w-4" : "h-4 w-4"
            )} />
          )}
        </Button>

        {/* Send Button */}
        <Button
          onClick={handleSendMessage}
          disabled={!message.trim() || isSending}
          size="sm"
          className={cn(
            "shrink-0",
            isCompact ? "h-10 w-10 p-0 rounded-lg" : "min-h-[44px] min-w-[44px] p-2",
            message.trim() && !isSending && "bg-primary text-primary-foreground hover:bg-primary/90"
          )}
        >
          {isSending ? (
            <Loader2 className={cn(
              "animate-spin",
              isCompact ? "h-4 w-4" : "h-5 w-5"
            )} />
          ) : (
            <Send className={cn(
              isCompact ? "h-4 w-4" : "h-5 w-5"
            )} />
          )}
        </Button>
      </div>
    </div>
  );
}