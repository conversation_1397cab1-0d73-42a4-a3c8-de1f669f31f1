/**
 * TypeScript type definitions for Room Interface Components
 * 
 * These types extend the existing types from @/lib/types to provide
 * specific interfaces for the room interface components.
 */

import type { Id } from '@/convex/_generated/dataModel';
import type {
  Room,
  Participant,
  ParticipantSummary,
  Message,
  TypingParticipant,
  MessageReaction
} from '@/lib/types';

// ============================================================================
// COMPONENT PROP TYPES
// ============================================================================

/**
 * Media control state for voice/video functionality
 */
export interface MediaControlsState {
  audio: boolean;
  video: boolean;
  screenShare: boolean;
  call: boolean;
  speaker?: boolean;
}

/**
 * Connection quality indicator
 */
export type ConnectionQuality = 'excellent' | 'good' | 'poor' | 'disconnected';

/**
 * Reply context for message threading
 */
export interface ReplyContext {
  messageId: string;
  content: string;
  author: string;
}

/**
 * Enhanced message reaction with participant details
 */
export interface EnhancedMessageReaction {
  emoji: string;
  count: number;
  participantSessionIds: string[];
  firstReactedAt: number;
  lastReactedAt: number;
  participantDetails?: Array<{
    sessionId: string;
    anonymousAlias: string;
    reactedAt: number;
  }>;
}

// ============================================================================
// MAIN COMPONENT INTERFACES
// ============================================================================

/**
 * Props for the main RoomInterface component
 */
export interface RoomInterfaceProps {
  // Core data
  room: Room;
  participants: ParticipantSummary[];
  messages: Message[];
  typingIndicators: TypingParticipant[];
  currentParticipant?: ParticipantSummary;
  isCreator?: boolean;

  // Message handlers
  onMessageSend: (content: string, replyToId?: Id<'messages'>) => Promise<void>;
  onReaction: (messageId: string, emoji: string) => Promise<void>;
  onReply: (messageId: string, content: string, author: string) => void;
  onEdit?: (messageId: string, newContent: string) => Promise<void>;
  onDelete?: (messageId: string) => Promise<void>;
  onMentionClick?: (participantSessionId: string) => void;

  // Room actions
  onLeave: () => void;
  onShare: () => void;
  onEndRoom?: () => void;

  // Reply state
  replyingTo?: ReplyContext | null;
  onCancelReply?: () => void;

  // Optional customization
  className?: string;
}

/**
 * Props for the MobileRoomLayout component
 */
export interface MobileRoomLayoutProps extends RoomInterfaceProps {
  // Media controls removed - now using voice controls directly
}

/**
 * Props for MediaControls component
 */
export interface MediaControlsProps {
  mediaState: MediaControlsState;
  onToggleAudio: () => void;
  onToggleVideo: () => void;
  onToggleScreenShare: () => void;
  onToggleCall: () => void;
  onToggleSpeaker: () => void;
  isConnected?: boolean;
  connectionQuality?: ConnectionQuality;
  participantCount?: number;
  disabled?: boolean;
  className?: string;
}

/**
 * Props for EnhancedMessage component
 */
export interface EnhancedMessageProps {
  message: Message;
  currentParticipant?: Participant;
  participants: Participant[];
  onReaction: (messageId: string, emoji: string) => void;
  onReply: (messageId: string, content: string, author: string) => void;
  onEdit?: (messageId: string, newContent: string) => void;
  onDelete?: (messageId: string) => void;
  onMentionClick?: (participantSessionId: string) => void;
  showAvatar?: boolean;
  showTimestamp?: boolean;
  isGrouped?: boolean;
  className?: string;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

// TypingParticipant is imported from @/lib/types

/**
 * Connection status for real-time features
 */
export interface ConnectionStatus {
  status: 'connecting' | 'connected' | 'disconnected' | 'error';
  quality?: ConnectionQuality;
  lastConnected?: number;
  retryCount?: number;
}

/**
 * UI state for component interactions
 */
export interface ComponentUIState {
  showParticipants: boolean;
  showReactions: boolean;
  isHovered: boolean;
  selectedMessageId?: string;
  expandedThreadId?: string;
}

/**
 * Message action types for context menus
 */
export type MessageAction = 
  | 'reply'
  | 'edit' 
  | 'delete'
  | 'copy'
  | 'react'
  | 'report'
  | 'mention';

/**
 * Media device information
 */
export interface MediaDevice {
  deviceId: string;
  label: string;
  kind: 'audioinput' | 'videoinput' | 'audiooutput';
}

/**
 * Room statistics for display
 */
export interface RoomStats {
  totalMessages: number;
  activeParticipants: number;
  averageResponseTime: number;
  mostActiveParticipant?: string;
  popularReactions: Array<{
    emoji: string;
    count: number;
  }>;
}

// ============================================================================
// EVENT HANDLER TYPES
// ============================================================================

/**
 * Message event handlers
 */
export interface MessageEventHandlers {
  onSend: (content: string, replyToId?: string) => Promise<void>;
  onEdit: (messageId: string, newContent: string) => Promise<void>;
  onDelete: (messageId: string) => Promise<void>;
  onReact: (messageId: string, emoji: string) => Promise<void>;
  onReply: (messageId: string, content: string, author: string) => void;
  onMention: (participantSessionId: string) => void;
}

/**
 * Media event handlers
 */
export interface MediaEventHandlers {
  onToggleAudio: () => Promise<void>;
  onToggleVideo: () => Promise<void>;
  onToggleScreenShare: () => Promise<void>;
  onToggleCall: () => Promise<void>;
  onToggleSpeaker: () => Promise<void>;
  onDeviceChange: (deviceId: string, kind: MediaDevice['kind']) => Promise<void>;
}

/**
 * Room event handlers
 */
export interface RoomEventHandlers {
  onJoin: () => Promise<void>;
  onLeave: () => Promise<void>;
  onEnd: () => Promise<void>;
  onShare: () => Promise<void>;
  onInvite: (email?: string) => Promise<void>;
  onSettingsChange: (settings: Partial<Room>) => Promise<void>;
}

// ============================================================================
// CONFIGURATION TYPES
// ============================================================================

/**
 * Component feature flags
 */
export interface ComponentFeatures {
  enableReactions: boolean;
  enableReplies: boolean;
  enableEditing: boolean;
  enableDeletion: boolean;
  enableMentions: boolean;
  enableTypingIndicators: boolean;
  enableMediaControls: boolean;
  enableScreenShare: boolean;
  enableVoiceModulation: boolean;
}

/**
 * Accessibility configuration
 */
export interface AccessibilityConfig {
  enableScreenReader: boolean;
  enableKeyboardShortcuts: boolean;
  enableHighContrast: boolean;
  enableReducedMotion: boolean;
  announceActions: boolean;
}

/**
 * Performance configuration
 */
export interface PerformanceConfig {
  enableVirtualScrolling: boolean;
  messageBufferSize: number;
  typingDebounceMs: number;
  reactionDebounceMs: number;
  connectionRetryAttempts: number;
  connectionRetryDelayMs: number;
}

// ============================================================================
// EXPORT ALL TYPES
// ============================================================================

export type {
  // Re-export base types for convenience
  Room,
  Participant,
  ParticipantSummary,
  Message,
  TypingParticipant,
  MessageReaction,
  Id
};
