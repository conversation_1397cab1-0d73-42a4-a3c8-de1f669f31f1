"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { 
  ArrowRight,
  Clock,
  Shield,
  Brain,
  Zap,
  Lock,
  Trash2,
  CheckCircle,
  Sparkles
} from "lucide-react";

export const FinalCTASection: React.FC = () => {
  const [roomCode, setRoomCode] = useState("");

  const benefits = [
    {
      icon: Clock,
      title: "Create room in under 10 seconds",
      description: "Instant setup, no complexity"
    },
    {
      icon: Shield,
      title: "Complete anonymity guaranteed",
      description: "Zero personal data collection"
    },
    {
      icon: Brain,
      title: "AI-powered conversation enhancement",
      description: "Smart moderation and facilitation"
    },
    {
      icon: Zap,
      title: "No registration required",
      description: "Start conversations immediately"
    },
    {
      icon: Lock,
      title: "Military-grade encryption",
      description: "Bank-level security standards"
    },
    {
      icon: Trash2,
      title: "Auto-delete after sessions",
      description: "No permanent data storage"
    }
  ];

  const quickStats = [
    { value: "10,000+", label: "Sessions Completed" },
    { value: "99.8%", label: "Uptime Guarantee" },
    { value: "< 10s", label: "Setup Time" },
    { value: "100%", label: "Anonymous" }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <section className="py-24 bg-gradient-to-br from-primary/5 via-background to-secondary/5 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-grid-black/[0.02] dark:bg-grid-white/[0.02]" />
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-[60rem] h-[60rem] bg-gradient-to-r from-primary/10 to-secondary/10 rounded-full blur-3xl opacity-30" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="max-w-6xl mx-auto"
        >
          {/* Main CTA Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <div className="flex items-center justify-center gap-2 mb-4">
              <Sparkles className="h-6 w-6 text-primary" />
              <Badge variant="secondary" className="px-4 py-2">
                Ready to Start?
              </Badge>
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-6xl font-bold mb-6">
              Start Your First
              <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                {" "}Anonymous Room
              </span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
              Join thousands of organizations using VeilMeet for honest, secure, 
              and AI-enhanced anonymous conversations.
            </p>
          </motion.div>

          {/* Quick Stats */}
          <motion.div variants={itemVariants} className="mb-12">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {quickStats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-primary mb-1">
                    {stat.value}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Primary Actions */}
          <motion.div variants={itemVariants} className="mb-16">
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-2xl mx-auto">
              <Button size="lg" className="text-lg px-8 py-6 group min-w-[200px]">
                Create Anonymous Room
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
              
              <div className="flex items-center gap-2">
                <Input
                  placeholder="Enter room code"
                  value={roomCode}
                  onChange={(e) => setRoomCode(e.target.value)}
                  className="w-48"
                />
                <Button variant="outline" size="lg" className="px-6 py-6">
                  Join Room
                </Button>
              </div>
            </div>
            
            <p className="text-center text-sm text-muted-foreground mt-4">
              No registration • No personal data • Instant setup
            </p>
          </motion.div>

          {/* Benefits Grid - Cleaner design */}
          <motion.div variants={itemVariants} className="mb-16">
            <h3 className="text-2xl font-bold text-center mb-8">
              Everything You Need for Anonymous Conversations
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <motion.div
                  key={index}
                  className="text-center group hover:scale-105 transition-transform duration-300"
                  whileHover={{ y: -4 }}
                >
                  <div className="p-6 rounded-2xl bg-gradient-to-br from-background/80 to-muted/20 backdrop-blur-sm border border-muted/30 hover:border-primary/30 transition-all duration-300 hover:shadow-lg">
                    <div className="p-4 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10 w-fit mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                      <benefit.icon className="h-6 w-6 text-primary" />
                    </div>
                    <h4 className="font-bold mb-2">{benefit.title}</h4>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {benefit.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Security Assurance - More organic design */}
          <motion.div
            variants={itemVariants}
            className="text-center relative overflow-hidden mb-12"
          >
            {/* Background gradient */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-background/50 to-secondary/5 rounded-3xl" />

            <div className="relative p-12">
              <div className="flex items-center justify-center gap-3 mb-6">
                <div className="p-3 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10">
                  <Shield className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                  Your Privacy is Guaranteed
                </h3>
              </div>
              <p className="text-muted-foreground mb-8 max-w-3xl mx-auto text-lg leading-relaxed">
                We use zero-knowledge architecture, which means we literally cannot access
                your conversations even if we wanted to. Your anonymity isn't just protected
                by policy—it's guaranteed by design.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 text-primary font-medium border border-primary/20">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  SOC 2 Certified
                </div>
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-secondary/10 to-secondary/5 text-secondary font-medium border border-secondary/20">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  GDPR Compliant
                </div>
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 text-primary font-medium border border-primary/20">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  HIPAA Ready
                </div>
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-secondary/10 to-secondary/5 text-secondary font-medium border border-secondary/20">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Zero Data Storage
                </div>
              </div>
            </div>
          </motion.div>

          {/* Final Push */}
          <motion.div variants={itemVariants} className="text-center">
            <h3 className="text-2xl font-bold mb-4">
              Ready to Experience True Anonymous Conversations?
            </h3>
            <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
              Join the revolution in secure, anonymous communication. 
              Start your first room in less than 10 seconds.
            </p>
            <Button size="lg" className="text-lg px-12 py-6 group">
              Get Started Now
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};
