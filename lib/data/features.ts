import { 
  Shield, 
  Brain, 
  Zap, 
  Eye,
  UserX,
  <PERSON>,
  <PERSON>rk<PERSON>,
  <PERSON>,
  LucideIcon
} from "lucide-react";

export interface Feature {
  icon: LucideIcon;
  title: string;
  description: string;
  details: string[];
  badge: string;
  color: string;
}

export interface SimpleFeature {
  icon: LucideIcon;
  title: string;
  description: string;
}

export const mainFeatures: Feature[] = [
  {
    icon: Shield,
    title: "Complete Anonymity",
    description: "No registration or tracking. No digital footprints left behind.",
    details: [
      "No registration or tracking",
      "No digital footprints",
      "AI-generated identities",
      "Zero personal data collection"
    ],
    badge: "Privacy First",
    color: "text-blue-600 dark:text-blue-400"
  },
  {
    icon: Brain,
    title: "AI-Powered Moderation",
    description: "Real-time conversation monitoring keeps discussions productive and respectful.",
    details: [
      "Real-time toxicity detection",
      "Bias prevention algorithms",
      "Context-aware responses",
      "Automatic escalation management"
    ],
    badge: "AI Enhanced",
    color: "text-purple-600 dark:text-purple-400"
  },
  {
    icon: Zap,
    title: "Instant Room Creation",
    description: "Create secure meeting rooms in seconds with no setup complexity.",
    details: [
      "One-click room generation",
      "No configuration needed",
      "Instant secure links",
      "Ready in under 10 seconds"
    ],
    badge: "One-Click",
    color: "text-yellow-600 dark:text-yellow-400"
  }
];

export const additionalFeatures: SimpleFeature[] = [
  {
    icon: Eye,
    title: "Voice Modulation",
    description: "Advanced voice processing for complete audio anonymity"
  },
  {
    icon: UserX,
    title: "Identity Protection",
    description: "AI-generated personas protect your real identity"
  },
  {
    icon: Lock,
    title: "End-to-End Encryption",
    description: "Military-grade security for all communications"
  },
  {
    icon: Sparkles,
    title: "Smart Facilitation",
    description: "AI suggests topics and manages conversation flow"
  },
  {
    icon: Clock,
    title: "Auto-Cleanup",
    description: "All data automatically deleted after sessions"
  }
];

export const trustBadges = [
  "Zero Data Collection",
  "Enterprise Security", 
  "AI-Enhanced Experience",
  "Instant Setup"
];

export const securityCertifications = [
  "SOC 2 Certified",
  "GDPR Compliant", 
  "HIPAA Ready",
  "Zero Data Storage"
];
