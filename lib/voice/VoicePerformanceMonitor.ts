/**
 * VeilMind Voice Performance Monitor
 * Monitors and optimizes voice system performance
 */

export interface PerformanceMetrics {
  // Latency Metrics
  audioLatency: number; // ms
  processingLatency: number; // ms
  networkLatency: number; // ms
  totalLatency: number; // ms
  
  // CPU Usage
  cpuUsage: number; // percentage
  audioWorkletCpu: number; // percentage
  mainThreadCpu: number; // percentage
  
  // Memory Usage
  memoryUsage: number; // MB
  audioBufferMemory: number; // MB
  
  // Audio Quality
  audioQuality: number; // 0-1 score
  dropoutRate: number; // percentage
  jitterBuffer: number; // ms
  
  // Battery Impact (mobile)
  batteryDrain: number; // mAh/hour estimate
  
  // Network
  bandwidth: number; // kbps
  packetLoss: number; // percentage
  
  // Browser Compatibility
  browserScore: number; // 0-1 compatibility score
  webAudioSupport: boolean;
  webRtcSupport: boolean;
  audioWorkletSupport: boolean;
}

export interface PerformanceTarget {
  maxLatency: number; // 100ms
  maxCpuUsage: number; // 10%
  maxMemoryUsage: number; // 50MB
  minAudioQuality: number; // 0.8
  maxDropoutRate: number; // 1%
  maxBatteryDrain: number; // 100mAh/hour
}

export class VoicePerformanceMonitor {
  private static instance: VoicePerformanceMonitor;
  private metrics: PerformanceMetrics;
  private targets: PerformanceTarget;
  private isMonitoring = false;
  
  // Performance tracking
  private performanceObserver: PerformanceObserver | null = null;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private latencyMeasurements: number[] = [];
  private cpuMeasurements: number[] = [];
  
  // Audio context monitoring
  private audioContext: AudioContext | null = null;
  private analyserNode: AnalyserNode | null = null;
  
  // Battery monitoring (mobile)
  private batteryManager: any = null;
  private initialBatteryLevel = 0;
  private batteryMonitorStart = 0;
  
  private constructor() {
    this.metrics = {
      audioLatency: 0,
      processingLatency: 0,
      networkLatency: 0,
      totalLatency: 0,
      cpuUsage: 0,
      audioWorkletCpu: 0,
      mainThreadCpu: 0,
      memoryUsage: 0,
      audioBufferMemory: 0,
      audioQuality: 1,
      dropoutRate: 0,
      jitterBuffer: 0,
      batteryDrain: 0,
      bandwidth: 0,
      packetLoss: 0,
      browserScore: 0,
      webAudioSupport: false,
      webRtcSupport: false,
      audioWorkletSupport: false,
    };
    
    this.targets = {
      maxLatency: 100, // ms
      maxCpuUsage: 10, // %
      maxMemoryUsage: 50, // MB
      minAudioQuality: 0.8,
      maxDropoutRate: 1, // %
      maxBatteryDrain: 100, // mAh/hour
    };
    
    this.initializeBrowserCompatibility();
    this.initializeBatteryMonitoring();
  }
  
  static getInstance(): VoicePerformanceMonitor {
    if (!VoicePerformanceMonitor.instance) {
      VoicePerformanceMonitor.instance = new VoicePerformanceMonitor();
    }
    return VoicePerformanceMonitor.instance;
  }
  
  /**
   * Start performance monitoring
   */
  startMonitoring(audioContext?: AudioContext): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.audioContext = audioContext || null;
    
    // Set up performance observer
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        this.processPerformanceEntries(list.getEntries());
      });
      
      this.performanceObserver.observe({ 
        entryTypes: ['measure', 'navigation', 'resource'] 
      });
    }
    
    // Start periodic monitoring
    this.monitoringInterval = setInterval(() => {
      this.updateMetrics();
    }, 1000);
    
    console.log('Voice performance monitoring started');
  }
  
  /**
   * Stop performance monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) return;
    
    this.isMonitoring = false;
    
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
      this.performanceObserver = null;
    }
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    console.log('Voice performance monitoring stopped');
  }
  
  /**
   * Initialize browser compatibility check
   */
  private initializeBrowserCompatibility(): void {
    let score = 0;
    let checks = 0;
    
    // Web Audio API support
    if ('AudioContext' in window || 'webkitAudioContext' in window) {
      this.metrics.webAudioSupport = true;
      score += 1;
    }
    checks += 1;
    
    // WebRTC support
    if ('RTCPeerConnection' in window) {
      this.metrics.webRtcSupport = true;
      score += 1;
    }
    checks += 1;
    
    // AudioWorklet support
    if ('AudioWorklet' in window) {
      this.metrics.audioWorkletSupport = true;
      score += 1;
    }
    checks += 1;
    
    // Additional checks
    const additionalFeatures = [
      'MediaDevices' in window,
      'navigator' in window && 'mediaDevices' in navigator,
      'getUserMedia' in (navigator.mediaDevices || {}),
      'MediaStreamAudioSourceNode' in window,
      'AnalyserNode' in window,
    ];
    
    additionalFeatures.forEach(supported => {
      if (supported) score += 1;
      checks += 1;
    });
    
    this.metrics.browserScore = score / checks;
  }
  
  /**
   * Initialize battery monitoring for mobile devices
   */
  private async initializeBatteryMonitoring(): Promise<void> {
    if ('getBattery' in navigator) {
      try {
        this.batteryManager = await (navigator as any).getBattery();
        this.initialBatteryLevel = this.batteryManager.level;
        this.batteryMonitorStart = Date.now();
      } catch (error) {
        console.warn('Battery monitoring not available:', error);
      }
    }
  }
  
  /**
   * Process performance entries
   */
  private processPerformanceEntries(entries: PerformanceEntry[]): void {
    entries.forEach(entry => {
      if (entry.name.includes('audio') || entry.name.includes('voice')) {
        this.latencyMeasurements.push(entry.duration);
        
        // Keep only last 100 measurements
        if (this.latencyMeasurements.length > 100) {
          this.latencyMeasurements.shift();
        }
      }
    });
  }
  
  /**
   * Update performance metrics
   */
  private updateMetrics(): void {
    this.updateLatencyMetrics();
    this.updateCpuMetrics();
    this.updateMemoryMetrics();
    this.updateAudioQualityMetrics();
    this.updateBatteryMetrics();
    this.updateNetworkMetrics();
  }
  
  /**
   * Update latency metrics
   */
  private updateLatencyMetrics(): void {
    if (this.audioContext) {
      // Audio context latency
      this.metrics.audioLatency = (this.audioContext.baseLatency || 0) * 1000;
      
      // Processing latency from measurements
      if (this.latencyMeasurements.length > 0) {
        const avgLatency = this.latencyMeasurements.reduce((a, b) => a + b, 0) / this.latencyMeasurements.length;
        this.metrics.processingLatency = avgLatency;
      }
      
      // Total latency
      this.metrics.totalLatency = this.metrics.audioLatency + this.metrics.processingLatency + this.metrics.networkLatency;
    }
  }
  
  /**
   * Update CPU metrics
   */
  private updateCpuMetrics(): void {
    if ('performance' in window && 'memory' in (performance as any)) {
      const memory = (performance as any).memory;
      
      // Estimate CPU usage based on memory allocation patterns
      const memoryPressure = memory.usedJSHeapSize / memory.totalJSHeapSize;
      this.metrics.cpuUsage = Math.min(memoryPressure * 20, 100); // Rough estimate
      
      this.cpuMeasurements.push(this.metrics.cpuUsage);
      if (this.cpuMeasurements.length > 60) { // Keep 1 minute of data
        this.cpuMeasurements.shift();
      }
    }
  }
  
  /**
   * Update memory metrics
   */
  private updateMemoryMetrics(): void {
    if ('performance' in window && 'memory' in (performance as any)) {
      const memory = (performance as any).memory;
      this.metrics.memoryUsage = memory.usedJSHeapSize / (1024 * 1024); // Convert to MB
    }
  }
  
  /**
   * Update audio quality metrics
   */
  private updateAudioQualityMetrics(): void {
    if (this.analyserNode) {
      const bufferLength = this.analyserNode.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      this.analyserNode.getByteFrequencyData(dataArray);
      
      // Calculate audio quality based on frequency distribution
      let totalEnergy = 0;
      let voiceEnergy = 0;
      
      for (let i = 0; i < bufferLength; i++) {
        const frequency = i * (this.audioContext!.sampleRate / 2) / bufferLength;
        const energy = dataArray[i];
        
        totalEnergy += energy;
        
        // Voice frequency range (80Hz - 8kHz)
        if (frequency >= 80 && frequency <= 8000) {
          voiceEnergy += energy;
        }
      }
      
      this.metrics.audioQuality = totalEnergy > 0 ? voiceEnergy / totalEnergy : 1;
    }
  }
  
  /**
   * Update battery metrics
   */
  private updateBatteryMetrics(): void {
    if (this.batteryManager) {
      const currentLevel = this.batteryManager.level;
      const timeElapsed = (Date.now() - this.batteryMonitorStart) / (1000 * 60 * 60); // hours
      
      if (timeElapsed > 0) {
        const batteryUsed = this.initialBatteryLevel - currentLevel;
        // Rough estimate: assume 3000mAh battery capacity
        this.metrics.batteryDrain = (batteryUsed * 3000) / timeElapsed;
      }
    }
  }
  
  /**
   * Update network metrics
   */
  private updateNetworkMetrics(): void {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      this.metrics.bandwidth = connection.downlink * 1000; // Convert to kbps
    }
  }
  
  /**
   * Measure audio processing latency
   */
  measureAudioLatency(startTime: number): void {
    const latency = performance.now() - startTime;
    this.latencyMeasurements.push(latency);
    
    if (this.latencyMeasurements.length > 100) {
      this.latencyMeasurements.shift();
    }
  }
  
  /**
   * Set analyser node for audio quality monitoring
   */
  setAnalyserNode(analyser: AnalyserNode): void {
    this.analyserNode = analyser;
  }
  
  /**
   * Check if performance targets are met
   */
  checkPerformanceTargets(): { passed: boolean; failures: string[] } {
    const failures: string[] = [];
    
    if (this.metrics.totalLatency > this.targets.maxLatency) {
      failures.push(`Latency too high: ${this.metrics.totalLatency.toFixed(1)}ms > ${this.targets.maxLatency}ms`);
    }
    
    if (this.metrics.cpuUsage > this.targets.maxCpuUsage) {
      failures.push(`CPU usage too high: ${this.metrics.cpuUsage.toFixed(1)}% > ${this.targets.maxCpuUsage}%`);
    }
    
    if (this.metrics.memoryUsage > this.targets.maxMemoryUsage) {
      failures.push(`Memory usage too high: ${this.metrics.memoryUsage.toFixed(1)}MB > ${this.targets.maxMemoryUsage}MB`);
    }
    
    if (this.metrics.audioQuality < this.targets.minAudioQuality) {
      failures.push(`Audio quality too low: ${this.metrics.audioQuality.toFixed(2)} < ${this.targets.minAudioQuality}`);
    }
    
    if (this.metrics.dropoutRate > this.targets.maxDropoutRate) {
      failures.push(`Dropout rate too high: ${this.metrics.dropoutRate.toFixed(1)}% > ${this.targets.maxDropoutRate}%`);
    }
    
    if (this.metrics.batteryDrain > this.targets.maxBatteryDrain) {
      failures.push(`Battery drain too high: ${this.metrics.batteryDrain.toFixed(1)}mAh/h > ${this.targets.maxBatteryDrain}mAh/h`);
    }
    
    return {
      passed: failures.length === 0,
      failures
    };
  }
  
  /**
   * Get optimization recommendations
   */
  getOptimizationRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (this.metrics.totalLatency > this.targets.maxLatency) {
      recommendations.push('Reduce audio buffer size');
      recommendations.push('Use AudioWorklet instead of ScriptProcessor');
      recommendations.push('Optimize voice processing algorithms');
    }
    
    if (this.metrics.cpuUsage > this.targets.maxCpuUsage) {
      recommendations.push('Reduce voice modulation complexity');
      recommendations.push('Use Web Workers for heavy processing');
      recommendations.push('Implement adaptive quality based on device performance');
    }
    
    if (this.metrics.memoryUsage > this.targets.maxMemoryUsage) {
      recommendations.push('Implement more aggressive buffer cleanup');
      recommendations.push('Reduce audio buffer sizes');
      recommendations.push('Use streaming processing instead of batch processing');
    }
    
    if (this.metrics.audioQuality < this.targets.minAudioQuality) {
      recommendations.push('Improve noise suppression algorithms');
      recommendations.push('Increase audio sample rate');
      recommendations.push('Optimize voice modulation parameters');
    }
    
    if (this.metrics.batteryDrain > this.targets.maxBatteryDrain) {
      recommendations.push('Reduce processing frequency on mobile');
      recommendations.push('Use device-specific optimizations');
      recommendations.push('Implement power-saving mode');
    }
    
    return recommendations;
  }
  
  /**
   * Generate performance report
   */
  generatePerformanceReport(): {
    metrics: PerformanceMetrics;
    targets: PerformanceTarget;
    targetCheck: { passed: boolean; failures: string[] };
    recommendations: string[];
    browserCompatibility: string;
    timestamp: number;
  } {
    const targetCheck = this.checkPerformanceTargets();
    const recommendations = this.getOptimizationRecommendations();
    
    let browserCompatibility = 'Excellent';
    if (this.metrics.browserScore < 0.8) browserCompatibility = 'Good';
    if (this.metrics.browserScore < 0.6) browserCompatibility = 'Limited';
    if (this.metrics.browserScore < 0.4) browserCompatibility = 'Poor';
    
    return {
      metrics: { ...this.metrics },
      targets: { ...this.targets },
      targetCheck,
      recommendations,
      browserCompatibility,
      timestamp: Date.now()
    };
  }
  
  /**
   * Get current metrics
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }
  
  /**
   * Update performance targets
   */
  updateTargets(newTargets: Partial<PerformanceTarget>): void {
    this.targets = { ...this.targets, ...newTargets };
  }
  
  /**
   * Cleanup and dispose
   */
  dispose(): void {
    this.stopMonitoring();
    console.log('Voice Performance Monitor disposed');
  }
}
