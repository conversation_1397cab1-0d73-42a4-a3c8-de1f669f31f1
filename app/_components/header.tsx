"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Shield, Plus, Users, Menu, X } from "lucide-react";
import { useRouter } from "next/navigation";

interface NavItem {
  name: string;
  link: string;
}

interface HeaderProps {
  navItems: NavItem[];
}

export const Header: React.FC<HeaderProps> = ({ navItems }) => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const router = useRouter();

  const scrollToSection = (sectionId: string) => {
    const element = document.querySelector(sectionId);
    if (element) {
      element.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
    setMobileMenuOpen(false);
  };

  const handleCreateRoom = () => {
    router.push('/create-room');
  };

  const handleJoinRoom = () => {
    router.push('/join');
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md border-b border-muted/20">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Brand */}
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-xl bg-gradient-to-br from-primary/20 to-primary/10">
              <Shield className="h-6 w-6 text-primary" />
            </div>
            <span className="font-bold text-xl bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              VeilMeet
            </span>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navItems.map((navItem, idx) => (
              <button
                key={`nav-${idx}`}
                onClick={() => scrollToSection(navItem.link)}
                className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors duration-200"
              >
                {navItem.name}
              </button>
            ))}
          </nav>

          {/* Desktop Action Buttons */}
          <div className="hidden md:flex items-center gap-3">
            <Button variant="outline" size="sm" className="gap-2" onClick={handleJoinRoom}>
              <Users className="h-4 w-4" />
              Join Room
            </Button>
            <Button size="sm" className="gap-2" onClick={handleCreateRoom}>
              <Plus className="h-4 w-4" />
              Create Room
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="p-2"
            >
              {mobileMenuOpen ? (
                <X className="h-4 w-4" />
              ) : (
                <Menu className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {mobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
              className="md:hidden border-t border-muted/20 py-4"
            >
              <div className="flex flex-col space-y-4">
                {/* Navigation Links */}
                {navItems.map((navItem, idx) => (
                  <button
                    key={`mobile-nav-${idx}`}
                    onClick={() => scrollToSection(navItem.link)}
                    className="text-left px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-muted/50 rounded-lg transition-colors duration-200"
                  >
                    {navItem.name}
                  </button>
                ))}
                
                {/* Mobile Action Buttons */}
                <div className="flex flex-col gap-3 pt-4 border-t border-muted/20">
                  <Button variant="outline" size="sm" className="gap-2 justify-start" onClick={handleJoinRoom}>
                    <Users className="h-4 w-4" />
                    Join Room
                  </Button>
                  <Button size="sm" className="gap-2 justify-start" onClick={handleCreateRoom}>
                    <Plus className="h-4 w-4" />
                    Create Room
                  </Button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </header>
  );
};
