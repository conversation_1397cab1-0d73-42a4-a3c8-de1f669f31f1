"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON>le } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Shield, 
  UserX, 
  Trash2, 
  Server,
  Eye,
  Mic,
  Lock,
  CheckCircle,
  Award,
  FileText,
  Globe,
  Zap
} from "lucide-react";

interface SecurityFeature {
  icon: React.ElementType;
  title: string;
  description: string;
  details: string[];
  color: string;
}

interface Certification {
  name: string;
  description: string;
  icon: React.ElementType;
}

export const PrivacySecuritySection: React.FC = () => {
  const securityFeatures: SecurityFeature[] = [
    {
      icon: Shield,
      title: "Military-Grade Encryption",
      description: "End-to-end AES-256 encryption protects all communications",
      details: [
        "AES-256 encryption standard",
        "Perfect forward secrecy",
        "Encrypted data transmission",
        "Secure key management"
      ],
      color: "text-blue-600 dark:text-blue-400"
    },
    {
      icon: UserX,
      title: "Zero Identity Storage",
      description: "No personal information collected or stored anywhere",
      details: [
        "No account creation required",
        "No personal data collection",
        "Anonymous user sessions",
        "Temporary identity generation"
      ],
      color: "text-green-600 dark:text-green-400"
    },
    {
      icon: Trash2,
      title: "Auto-Delete Sessions",
      description: "All conversation data automatically deleted after sessions end",
      details: [
        "Immediate post-session cleanup",
        "No conversation logs stored",
        "Temporary file deletion",
        "Memory purging protocols"
      ],
      color: "text-red-600 dark:text-red-400"
    },
    {
      icon: Server,
      title: "Distributed Infrastructure",
      description: "Multiple secure servers across different jurisdictions",
      details: [
        "Geographically distributed servers",
        "No single point of failure",
        "Jurisdiction shopping protection",
        "Redundant security measures"
      ],
      color: "text-purple-600 dark:text-purple-400"
    },
    {
      icon: Eye,
      title: "No Tracking Technology",
      description: "Zero cookies, fingerprinting, or behavioral tracking",
      details: [
        "No cookies or tracking pixels",
        "No browser fingerprinting",
        "No behavioral analytics",
        "No cross-site tracking"
      ],
      color: "text-orange-600 dark:text-orange-400"
    },
    {
      icon: Mic,
      title: "Secure Voice Modulation",
      description: "Local device processing ensures voice privacy",
      details: [
        "Client-side voice processing",
        "Real-time voice alteration",
        "No voice data transmission",
        "Hardware-accelerated encryption"
      ],
      color: "text-cyan-600 dark:text-cyan-400"
    }
  ];

  const certifications: Certification[] = [
    {
      name: "SOC 2 Type II",
      description: "Certified for security, availability, and confidentiality",
      icon: Award
    },
    {
      name: "ISO 27001",
      description: "International standard for information security management",
      icon: Shield
    },
    {
      name: "GDPR Compliant",
      description: "Full compliance with European data protection regulations",
      icon: Globe
    },
    {
      name: "HIPAA Ready",
      description: "Healthcare-grade privacy and security standards",
      icon: FileText
    }
  ];

  const additionalGuarantees = [
    "Open source security protocols for transparency",
    "Legal protection - no data to request or subpoena",
    "Regular third-party security audits",
    "Bug bounty program for continuous improvement",
    "Incident response team available 24/7",
    "Zero-knowledge architecture design"
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <section id="privacy-security" className="py-24 bg-background">
      <div className="container mx-auto px-4">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="max-w-6xl mx-auto"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
              Enterprise-Grade
              <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                {" "}Privacy & Security
              </span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Bank-level security meets zero-knowledge privacy. Your conversations 
              are protected by the same standards used by financial institutions and healthcare providers.
            </p>
          </motion.div>

          {/* Security Features Grid */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16"
          >
            {securityFeatures.map((feature, index) => (
              <motion.div key={index} variants={itemVariants}>
                <Card className="h-full border-muted/50 bg-card/50 backdrop-blur-sm hover:shadow-lg transition-all duration-300 group">
                  <CardHeader className="pb-4">
                    <div className="p-3 rounded-xl bg-primary/10 w-fit mb-4 group-hover:bg-primary/20 transition-colors">
                      <feature.icon className={`h-8 w-8 ${feature.color}`} />
                    </div>
                    <CardTitle className="text-xl mb-2">{feature.title}</CardTitle>
                    <p className="text-muted-foreground text-sm leading-relaxed">
                      {feature.description}
                    </p>
                  </CardHeader>
                  
                  <CardContent className="pt-0">
                    <ul className="space-y-2">
                      {feature.details.map((detail, detailIndex) => (
                        <li key={detailIndex} className="flex items-center gap-2 text-sm">
                          <CheckCircle className="h-3 w-3 text-green-600 dark:text-green-400 flex-shrink-0" />
                          <span>{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {/* Certifications */}
          <motion.div variants={itemVariants} className="mb-16">
            <h3 className="text-2xl font-bold text-center mb-8">
              Industry Certifications & Compliance
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {certifications.map((cert, index) => (
                <Card key={index} className="border-muted/50 bg-card/30 backdrop-blur-sm hover:bg-card/50 transition-all duration-300 text-center">
                  <CardContent className="p-6">
                    <div className="p-3 rounded-lg bg-primary/10 w-fit mx-auto mb-4">
                      <cert.icon className="h-8 w-8 text-primary" />
                    </div>
                    <h4 className="font-bold mb-2">{cert.name}</h4>
                    <p className="text-sm text-muted-foreground">
                      {cert.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>

          {/* Additional Guarantees */}
          <motion.div variants={itemVariants} className="mb-12">
            <Card className="border-muted/50 bg-card/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-2xl text-center flex items-center justify-center gap-2">
                  <Lock className="h-6 w-6 text-primary" />
                  Additional Security Guarantees
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {additionalGuarantees.map((guarantee, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{guarantee}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Security Promise */}
          <motion.div
            variants={itemVariants}
            className="text-center bg-gradient-to-r from-primary/10 to-secondary/10 rounded-2xl p-8 border border-primary/20"
          >
            <div className="max-w-4xl mx-auto">
              <div className="flex items-center justify-center gap-2 mb-4">
                <Shield className="h-8 w-8 text-primary" />
                <h3 className="text-2xl font-bold">
                  Our Security Promise
                </h3>
              </div>
              <p className="text-muted-foreground mb-6 text-lg">
                We believe privacy is a fundamental right. That's why we've built VeilMeet 
                with a zero-knowledge architecture—we literally cannot access your conversations 
                even if we wanted to. Your anonymity isn't just protected by policy, 
                it's guaranteed by design.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Badge variant="outline" className="px-4 py-2 text-sm">
                  <Zap className="h-3 w-3 mr-2" />
                  Zero-Knowledge Architecture
                </Badge>
                <Badge variant="outline" className="px-4 py-2 text-sm">
                  <Shield className="h-3 w-3 mr-2" />
                  End-to-End Encryption
                </Badge>
                <Badge variant="outline" className="px-4 py-2 text-sm">
                  <UserX className="h-3 w-3 mr-2" />
                  Complete Anonymity
                </Badge>
                <Badge variant="outline" className="px-4 py-2 text-sm">
                  <Trash2 className="h-3 w-3 mr-2" />
                  Auto-Delete Everything
                </Badge>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};
