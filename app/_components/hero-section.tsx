"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
  Shield,
  Brain,
  Users,
  ArrowRight
} from "lucide-react";

export const HeroSection: React.FC = () => {
  const [roomCode, setRoomCode] = useState("");
  const router = useRouter();

  const handleCreateRoom = () => {
    router.push('/create-room');
  };

  const handleJoinRoom = () => {
    if (roomCode.trim()) {
      router.push(`/join?code=${encodeURIComponent(roomCode.trim())}`);
    } else {
      router.push('/join');
    }
  };

  // Animation variants
  const fadeInUp = {
    initial: { opacity: 0, y: 30 },
    animate: { opacity: 1, y: 0 }
  };

  const fadeIn = {
    initial: { opacity: 0 },
    animate: { opacity: 1 }
  };

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.2,
      }
    }
  };

  return (
    <section className="relative h-screen w-full flex items-center justify-center overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-primary/5">
        <div className="absolute inset-0">
          {/* Floating orbs with Framer Motion */}
          <motion.div 
            className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/10 rounded-full blur-3xl"
            animate={{
              x: [0, 100, 50, 0],
              y: [0, -50, -25, 0],
              scale: [1, 1.2, 1.1, 1],
            }}
            transition={{
              duration: 8,
              ease: "easeInOut",
              repeat: Infinity,
            }}
          />
          <motion.div 
            className="absolute top-3/4 right-1/4 w-96 h-96 bg-secondary/10 rounded-full blur-3xl"
            animate={{
              x: [0, -80, -40, 0],
              y: [0, 60, 30, 0],
              scale: [1, 0.8, 0.9, 1],
            }}
            transition={{
              duration: 10,
              ease: "easeInOut",
              repeat: Infinity,
            }}
          />
          <motion.div 
            className="absolute top-1/2 right-1/3 w-48 h-48 bg-accent/10 rounded-full blur-3xl"
            animate={{
              x: [0, 60, 30, 0],
              y: [0, -80, -40, 0],
              scale: [1, 1.1, 1.05, 1],
            }}
            transition={{
              duration: 6,
              ease: "easeInOut",
              repeat: Infinity,
            }}
          />
        </div>

        {/* Grid pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]" />
      </div>

      <motion.div 
        className="relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center flex flex-col justify-center items-center"
        variants={staggerContainer}
        initial="initial"
        animate="animate"
      >
        <motion.h1 
          className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl xl:text-8xl font-bold mb-6 sm:mb-8 leading-tight"
          variants={fadeInUp}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent block">Speak Your Truth</span>
          <span className="text-foreground block">Without Consequences</span>
        </motion.h1>

        <motion.p 
          className="text-lg sm:text-xl md:text-2xl text-muted-foreground mb-8 sm:mb-12 max-w-4xl mx-auto leading-relaxed px-2"
          variants={fadeInUp}
          transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
        >
          Create secure, anonymous meeting rooms with AI-powered moderation. Complete privacy, instant setup, no registration required.
        </motion.p>

        <motion.div 
          className="flex flex-col gap-6 justify-center items-center mb-12 sm:mb-16 px-2 max-w-2xl mx-auto"
          variants={fadeInUp}
          transition={{ duration: 0.8, ease: "easeOut", delay: 0.4 }}
        >
          {/* Create Room Button */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <Button
              size="lg"
              className="w-full sm:w-auto bg-primary hover:bg-primary/90 text-primary-foreground px-6 sm:px-8 py-4 sm:py-6 text-base sm:text-lg font-semibold transition-all duration-300 group min-h-[48px] shadow-lg hover:shadow-xl"
              onClick={handleCreateRoom}
            >
              <span className="whitespace-nowrap">Create Anonymous Room</span>
              <motion.div
                className="ml-2"
                animate={{ x: [0, 4, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              >
                <ArrowRight className="h-4 w-4 sm:h-5 sm:w-5" />
              </motion.div>
            </Button>
          </motion.div>

          {/* Divider */}
          <div className="flex items-center gap-4 w-full max-w-xs">
            <div className="flex-1 h-px bg-border"></div>
            <span className="text-sm text-muted-foreground whitespace-nowrap">or join existing</span>
            <div className="flex-1 h-px bg-border"></div>
          </div>

          {/* Join Room */}
          <div className="flex flex-col sm:flex-row gap-3 w-full">
            <motion.div
              whileFocus={{ scale: 1.02 }}
              className="flex-1"
            >
              <Input
                placeholder="Enter room code (e.g., sunset-wolf-1234)"
                value={roomCode}
                onChange={(e) => setRoomCode(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleJoinRoom()}
                className="min-h-[48px] text-center sm:text-left"
              />
            </motion.div>
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <Button
                variant="outline"
                size="lg"
                onClick={handleJoinRoom}
                className="min-h-[48px] px-6 sm:px-8 whitespace-nowrap"
              >
                <Users className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
                Join Room
              </Button>
            </motion.div>
          </div>
        </motion.div>

        {/* Trust Indicators */}
        <motion.div 
          className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-center px-2"
          variants={fadeInUp}
          transition={{ duration: 0.8, ease: "easeOut", delay: 0.6 }}
        >
          <motion.div 
            className="flex items-center gap-2"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <Shield className="w-4 h-4 sm:w-5 sm:h-5 text-primary flex-shrink-0" />
            <span className="text-sm sm:text-base whitespace-nowrap">100% Anonymous</span>
          </motion.div>
          <motion.div 
            className="flex items-center gap-2"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <Users className="w-4 h-4 sm:w-5 sm:h-5 text-primary flex-shrink-0" />
            <span className="text-sm sm:text-base whitespace-nowrap">No Registration Required</span>
          </motion.div>
          <motion.div 
            className="flex items-center gap-2"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <Brain className="w-4 h-4 sm:w-5 sm:h-5 text-primary flex-shrink-0" />
            <span className="text-sm sm:text-base whitespace-nowrap">AI-Moderated Discussions</span>
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Scroll indicator */}
      <motion.div 
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        variants={fadeIn}
        initial="initial"
        animate="animate"
        transition={{ duration: 0.8, ease: "easeOut", delay: 1 }}
      >
        <motion.div 
          className="w-6 h-10 border-2 border-primary/30 rounded-full flex justify-center"
          animate={{ y: [0, 10, 0] }}
          transition={{
            duration: 2,
            ease: "easeInOut",
            repeat: Infinity,
          }}
        >
          <motion.div 
            className="w-1 h-3 bg-primary rounded-full mt-2"
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          />
        </motion.div>
      </motion.div>
    </section>
  );
};