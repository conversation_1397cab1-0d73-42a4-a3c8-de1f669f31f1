import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Anonymous identity generation utilities
const ADJECTIVES = [
  "Thoughtful", "<PERSON>", "Cal<PERSON>", "Bright", "Silent", "Quick", "Gentle", "Bold", 
  "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Vibrant", "Mystic", "Serene", "Co<PERSON>",
  "Radiant", "Swift", "<PERSON>", "<PERSON><PERSON>", "Fierce", "Elegant", "Mysterious",
  "Luminous", "Tranquil", "Spirited", "Majestic", "Ethereal", "Resilient"
];

const ANIMALS = [
  "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Owl", "<PERSON>", "<PERSON>", "<PERSON>", 
  "<PERSON>", "<PERSON>", "Falcon", "Dragon", "Panther", "Swan", "Hawk",
  "Leopard", "Dolphin", "Butterfly", "Hummingbird", "Jaguar", "Crane",
  "Seahorse", "Firefly", "Lynx", "<PERSON>", "<PERSON>tter", "<PERSON>rrow", "Whale"
];

const COLORS = [
  "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", 
  "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Indigo", "<PERSON>", "<PERSON>pphire", "<PERSON>",
  "<PERSON>", "<PERSON>", "<PERSON>", "Onyx", "Turquoise", "Magenta", "<PERSON>"
];

// Generate anonymous alias
function generateAnonymousAlias(): string {
  const adjective = ADJECTIVES[Math.floor(Math.random() * ADJECTIVES.length)];
  const animal = ANIMALS[Math.floor(Math.random() * ANIMALS.length)];
  return `${adjective} ${animal}`;
}

// Generate avatar seed for consistent avatar generation
function generateAvatarSeed(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}

// Generate color theme
function generateColorTheme(): string {
  return COLORS[Math.floor(Math.random() * COLORS.length)];
}

// Generate session ID
function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
}

// Create anonymous identity for a room
export const createAnonymousIdentity = mutation({
  args: {
    roomCode: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const sessionId = generateSessionId();
    const anonymousAlias = generateAnonymousAlias();
    const avatarSeed = generateAvatarSeed();
    const colorTheme = generateColorTheme();

    // Store temporary identity (this could be used for session management)
    const identityId = await ctx.db.insert("temporaryIdentities", {
      sessionId,
      anonymousAlias,
      avatarSeed,
      colorTheme,
      roomCode: args.roomCode,
      createdAt: Date.now(),
      expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
      isActive: true,
    });

    return {
      identityId,
      sessionId,
      anonymousAlias,
      avatarSeed,
      colorTheme,
    };
  },
});

// Get identity by session ID
export const getIdentityBySession = query({
  args: { sessionId: v.string() },
  handler: async (ctx, args) => {
    const identity = await ctx.db
      .query("temporaryIdentities")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .first();

    if (!identity || identity.expiresAt < Date.now()) {
      return null;
    }

    return {
      sessionId: identity.sessionId,
      anonymousAlias: identity.anonymousAlias,
      avatarSeed: identity.avatarSeed,
      colorTheme: identity.colorTheme,
      createdAt: identity.createdAt,
    };
  },
});

// Update participant activity (heartbeat)
export const updateParticipantActivity = mutation({
  args: {
    roomId: v.id("rooms"),
    sessionId: v.string(),
    currentAction: v.optional(v.string()), // "typing", "speaking", "idle"
  },
  handler: async (ctx, args) => {
    // Update participant last active time
    const participant = await ctx.db
      .query("participants")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => q.eq(q.field("roomId"), args.roomId))
      .first();

    if (participant && participant.isActive) {
      await ctx.db.patch(participant._id, {
        lastActiveAt: Date.now(),
      });
    }

    // Update or create active connection
    const existingConnection = await ctx.db
      .query("activeConnections")
      .withIndex("by_room", (q) => q.eq("roomId", args.roomId))
      .filter((q) => q.eq(q.field("participantSessionId"), args.sessionId))
      .first();

    const now = Date.now();
    const expiresAt = now + (5 * 60 * 1000); // 5 minutes

    if (existingConnection) {
      await ctx.db.patch(existingConnection._id, {
        lastPing: now,
        expiresAt,
        currentAction: args.currentAction,
        isConnected: true,
      });
    } else {
      await ctx.db.insert("activeConnections", {
        roomId: args.roomId,
        participantSessionId: args.sessionId,
        connectionId: `conn_${now}_${Math.random().toString(36).substring(2, 8)}`,
        isConnected: true,
        lastPing: now,
        isAudioEnabled: false,
        isVideoEnabled: false,
        isScreenSharing: false,
        isInVoiceRoom: false,
        livekitConnected: false,
        currentAction: args.currentAction,
        connectedAt: now,
        expiresAt,
      });
    }
  },
});

// Get typing indicators for a room
export const getTypingIndicators = query({
  args: { roomId: v.id("rooms") },
  handler: async (ctx, args) => {
    const now = Date.now();
    const recentThreshold = now - (10 * 1000); // 10 seconds

    const typingConnections = await ctx.db
      .query("activeConnections")
      .withIndex("by_room", (q) => q.eq("roomId", args.roomId))
      .filter((q) => 
        q.and(
          q.eq(q.field("currentAction"), "typing"),
          q.gt(q.field("lastPing"), recentThreshold),
          q.eq(q.field("isConnected"), true)
        )
      )
      .collect();

    // Get participant aliases for typing indicators
    const typingParticipants = [];
    for (const connection of typingConnections) {
      const participant = await ctx.db
        .query("participants")
        .withIndex("by_session", (q) => q.eq("sessionId", connection.participantSessionId))
        .filter((q) => q.eq(q.field("roomId"), args.roomId))
        .first();

      if (participant && participant.isActive) {
        typingParticipants.push({
          sessionId: participant.sessionId,
          anonymousAlias: participant.anonymousAlias,
        });
      }
    }

    return typingParticipants;
  },
});

// Clean up expired identities and connections
export const cleanupExpiredData = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();

    // Clean up expired temporary identities
    const expiredIdentities = await ctx.db
      .query("temporaryIdentities")
      .withIndex("by_expires_at", (q) => q.lt("expiresAt", now))
      .collect();

    for (const identity of expiredIdentities) {
      await ctx.db.delete(identity._id);
    }

    // Clean up expired connections
    const expiredConnections = await ctx.db
      .query("activeConnections")
      .withIndex("by_expires_at", (q) => q.lt("expiresAt", now))
      .collect();

    for (const connection of expiredConnections) {
      await ctx.db.delete(connection._id);
    }

    // Mark inactive participants
    const staleThreshold = now - (30 * 60 * 1000); // 30 minutes
    const staleParticipants = await ctx.db
      .query("participants")
      .filter((q) => 
        q.and(
          q.eq(q.field("isActive"), true),
          q.lt(q.field("lastActiveAt"), staleThreshold)
        )
      )
      .collect();

    for (const participant of staleParticipants) {
      await ctx.db.patch(participant._id, {
        isActive: false,
        leftAt: now,
      });

      // Update room participant count
      const room = await ctx.db.get(participant.roomId);
      if (room) {
        await ctx.db.patch(participant.roomId, {
          currentParticipants: Math.max(0, room.currentParticipants - 1),
        });
      }
    }

    return {
      expiredIdentities: expiredIdentities.length,
      expiredConnections: expiredConnections.length,
      staleParticipants: staleParticipants.length,
    };
  },
});

// Validate session and room access
export const validateSessionAccess = query({
  args: {
    roomId: v.id("rooms"),
    sessionId: v.string(),
  },
  handler: async (ctx, args) => {
    // Check if participant exists and is active
    const participant = await ctx.db
      .query("participants")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => q.eq(q.field("roomId"), args.roomId))
      .first();

    if (!participant) {
      return { hasAccess: false, reason: "Not a participant" };
    }

    if (!participant.isActive) {
      return { hasAccess: false, reason: "Participant inactive" };
    }

    if (participant.isBlocked) {
      return { hasAccess: false, reason: "Participant blocked" };
    }

    // Check room status
    const room = await ctx.db.get(args.roomId);
    if (!room) {
      return { hasAccess: false, reason: "Room not found" };
    }

    if (room.status !== "active") {
      return { hasAccess: false, reason: "Room not active" };
    }

    if (room.expiresAt < Date.now()) {
      return { hasAccess: false, reason: "Room expired" };
    }

    return {
      hasAccess: true,
      participant: {
        sessionId: participant.sessionId,
        anonymousAlias: participant.anonymousAlias,
        avatarSeed: participant.avatarSeed,
        joinedAt: participant.joinedAt,
        isMuted: participant.isMuted,
      },
      room: {
        roomCode: room.roomCode,
        topic: room.topic,
        status: room.status,
        currentParticipants: room.currentParticipants,
        maxParticipants: room.maxParticipants,
      },
    };
  },
});
