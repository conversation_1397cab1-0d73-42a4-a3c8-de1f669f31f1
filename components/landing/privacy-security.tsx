"use client";

import { motion } from "motion/react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Shield, 
  Lock, 
  Eye, 
  Server,
  Trash2,
  UserX,
  CheckCircle,
  AlertTriangle
} from "lucide-react";

const securityFeatures = [
  {
    icon: Shield,
    title: "Military-Grade Encryption",
    description: "End-to-end AES-256 encryption ensures your conversations remain completely private and secure.",
    color: "green-500"
  },
  {
    icon: UserX,
    title: "Zero Identity Storage",
    description: "We never collect, store, or track any personal information. Complete anonymity guaranteed.",
    color: "blue-500"
  },
  {
    icon: Trash2,
    title: "Auto-Delete Sessions",
    description: "All conversation data is automatically deleted after sessions end. No permanent records.",
    color: "primary"
  },
  {
    icon: Server,
    title: "Distributed Infrastructure",
    description: "Conversations are processed across multiple secure servers to prevent data concentration.",
    color: "cyan-500"
  },
  {
    icon: Eye,
    title: "No Tracking Technology",
    description: "Zero cookies, fingerprinting, or tracking mechanisms. Your digital footprint stays clean.",
    color: "orange-500"
  },
  {
    icon: Lock,
    title: "Secure Voice Modulation",
    description: "Advanced voice changing happens locally on your device before transmission.",
    color: "red-500"
  }
];

const certifications = [
  { name: "SOC 2 Type II", status: "Certified" },
  { name: "ISO 27001", status: "Certified" },
  { name: "GDPR Compliant", status: "Verified" },
  { name: "HIPAA Ready", status: "Available" }
];

export function PrivacySecurity() {
  return (
    <section className="py-16 sm:py-20 lg:py-24 px-4 sm:px-6 lg:px-8 relative">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          className="text-center mb-12 sm:mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="mb-4 border-green-500/30 text-green-500 text-xs sm:text-sm">
            <Shield className="w-3 h-3 mr-1" />
            Privacy & Security
          </Badge>
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 px-2">
            <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">Fort Knox</span> Level Security
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed px-2">
            Your privacy isn't just protected—it's our foundation. Every technical decision
            prioritizes your anonymity and security above all else.
          </p>
        </motion.div>

        {/* Security Features Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mb-12 sm:mb-16">
          {securityFeatures.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
              className="group"
            >
              <Card className="h-full bg-card/50 backdrop-blur-sm border-border/50 hover:border-green-500/30 transition-all duration-300 group-hover:shadow-lg">
                <CardContent className="p-4 sm:p-6">
                  <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-lg sm:rounded-xl bg-${feature.color}/10 flex items-center justify-center mb-3 sm:mb-4 group-hover:bg-${feature.color}/20 transition-colors`}>
                    <feature.icon className={`w-5 h-5 sm:w-6 sm:h-6 text-${feature.color}`} />
                  </div>

                  <h3 className="text-base sm:text-lg font-semibold mb-2 sm:mb-3 group-hover:text-green-500 transition-colors leading-tight">
                    {feature.title}
                  </h3>

                  <p className="text-muted-foreground text-xs sm:text-sm leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Privacy Promise Section */}
        <motion.div
          className="bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-3xl p-8 lg:p-12 border border-green-500/20 mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-8">
            <h3 className="text-3xl font-bold mb-4">
              Our <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">Privacy Promise</span>
            </h3>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              We've built VeilMeet on the principle that true anonymity requires zero compromise.
              Here's our commitment to you:
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold mb-1">No Data Collection</h4>
                  <p className="text-sm text-muted-foreground">We never collect personal information, IP addresses, or device fingerprints.</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold mb-1">No Permanent Storage</h4>
                  <p className="text-sm text-muted-foreground">All conversation data is deleted immediately after sessions end.</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold mb-1">No Third-Party Sharing</h4>
                  <p className="text-sm text-muted-foreground">Your data never leaves our secure infrastructure or gets shared with anyone.</p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold mb-1">Open Source Security</h4>
                  <p className="text-sm text-muted-foreground">Our security protocols are open source and independently audited.</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold mb-1">Legal Protection</h4>
                  <p className="text-sm text-muted-foreground">We cannot comply with data requests because we don't have your data.</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold mb-1">Transparent Operations</h4>
                  <p className="text-sm text-muted-foreground">Regular security audits and transparency reports keep us accountable.</p>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Certifications */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-semibold mb-8">Security Certifications & Compliance</h3>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {certifications.map((cert, index) => (
              <motion.div
                key={cert.name}
                className="bg-card/30 backdrop-blur-sm rounded-xl p-4 border border-border/50 hover:border-green-500/30 transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="flex items-center justify-center mb-2">
                  <CheckCircle className="w-6 h-6 text-green-500" />
                </div>
                <h4 className="font-semibold text-sm mb-1">{cert.name}</h4>
                <p className="text-xs text-muted-foreground">{cert.status}</p>
              </motion.div>
            ))}
          </div>

          <div className="mt-8 inline-flex items-center gap-2 text-muted-foreground bg-card/30 px-6 py-3 rounded-full border border-border/50">
            <AlertTriangle className="w-4 h-4 text-orange-500" />
            <span className="text-sm">Independently audited by leading cybersecurity firms</span>
          </div>
        </motion.div>
      </div>

      {/* Background decoration */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-green-500/5 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 left-1/4 w-72 h-72 bg-blue-500/5 rounded-full blur-3xl" />
      </div>
    </section>
  );
}
