"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Brain, 
  MessageSquare, 
  Shield,
  Eye,
  AlertTriangle,
  Users,
  Lightbulb,
  BarChart3,
  Zap,
  CheckCircle,
  TrendingUp,
  Clock
} from "lucide-react";

interface AIFeature {
  icon: React.ElementType;
  title: string;
  description: string;
  capabilities: string[];
  color: string;
}

export const AICapabilitiesSection: React.FC = () => {
  const aiFeatures: AIFeature[] = [
    {
      icon: Brain,
      title: "Intelligent Moderation",
      description: "Advanced AI algorithms ensure conversations stay productive and respectful.",
      capabilities: [
        "Real-time toxicity detection and prevention",
        "Bias identification and mitigation",
        "Context-aware content analysis",
        "Automatic escalation management"
      ],
      color: "text-purple-600 dark:text-purple-400"
    },
    {
      icon: MessageSquare,
      title: "Smart Facilitation",
      description: "AI-powered conversation enhancement that keeps discussions flowing naturally.",
      capabilities: [
        "Intelligent topic suggestions",
        "Turn-taking management",
        "Engagement level tracking",
        "Conversation flow optimization"
      ],
      color: "text-blue-600 dark:text-blue-400"
    },
    {
      icon: Shield,
      title: "Privacy Protection",
      description: "AI-driven privacy safeguards that protect identity while enabling authentic conversations.",
      capabilities: [
        "Dynamic identity anonymization",
        "Content filtering and sanitization",
        "Secure data processing",
        "Minimal data retention policies"
      ],
      color: "text-green-600 dark:text-green-400"
    }
  ];

  const keyBenefits = [
    {
      icon: AlertTriangle,
      title: "Real-time Toxicity Prevention",
      description: "Advanced algorithms detect and prevent harmful content before it affects conversations"
    },
    {
      icon: Users,
      title: "Context-Aware Conversation Guidance",
      description: "AI understands conversation context to provide relevant suggestions and maintain flow"
    },
    {
      icon: BarChart3,
      title: "Intelligent Participant Management",
      description: "Automatically balances participation and ensures everyone has a voice"
    },
    {
      icon: TrendingUp,
      title: "Conversation Quality Enhancement",
      description: "Continuous improvement in conversation depth and participant satisfaction"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <section id="ai-capabilities" className="py-24 bg-muted/30">
      <div className="container mx-auto px-4">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="max-w-6xl mx-auto"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
              AI-Powered
              <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                {" "}Conversation Intelligence
              </span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Advanced artificial intelligence that doesn't just moderate—it enhances every 
              conversation while maintaining complete anonymity and privacy.
            </p>
          </motion.div>

          {/* Main AI Features - Less boxy design */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 lg:grid-cols-3 gap-12 mb-16"
          >
            {aiFeatures.map((feature, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="group relative"
                whileHover={{ y: -8 }}
              >
                <div className="relative h-full p-8 rounded-3xl bg-gradient-to-br from-background/80 to-muted/20 backdrop-blur-sm border border-muted/30 hover:border-primary/30 transition-all duration-500 hover:shadow-2xl hover:shadow-primary/10">
                  {/* Floating icon */}
                  <div className="absolute -top-6 left-8">
                    <div className="p-4 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20 shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                      <feature.icon className={`h-8 w-8 ${feature.color}`} />
                    </div>
                  </div>

                  <div className="pt-8">
                    <div className="flex justify-end mb-4">
                      <div className="inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary text-xs font-medium">
                        AI Powered
                      </div>
                    </div>

                    <h3 className="text-2xl font-bold mb-4">{feature.title}</h3>
                    <p className="text-muted-foreground leading-relaxed mb-6">
                      {feature.description}
                    </p>

                    {/* Capabilities */}
                    <div className="mb-6">
                      <h4 className="font-semibold mb-3 text-sm">Key Capabilities</h4>
                      <ul className="space-y-3">
                        {feature.capabilities.map((capability, capIndex) => (
                          <li key={capIndex} className="flex items-start gap-3 text-sm">
                            <CheckCircle className="h-3 w-3 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
                            <span className="leading-relaxed">{capability}</span>
                          </li>
                        ))}
                      </ul>
                    </div>


                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Key Benefits Grid - Cleaner design */}
          <motion.div variants={itemVariants} className="mb-12">
            <h3 className="text-2xl font-bold text-center mb-8">
              Measurable AI Impact
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {keyBenefits.map((benefit, index) => (
                <motion.div
                  key={index}
                  className="group hover:scale-105 transition-transform duration-300"
                  whileHover={{ y: -4 }}
                >
                  <div className="p-6 rounded-2xl bg-gradient-to-br from-background/80 to-muted/20 backdrop-blur-sm border border-muted/30 hover:border-primary/30 transition-all duration-300 hover:shadow-lg">
                    <div className="flex items-start gap-4">
                      <div className="p-3 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10 flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                        <benefit.icon className="h-6 w-6 text-primary" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-bold text-lg mb-3">{benefit.title}</h4>
                        <p className="text-muted-foreground leading-relaxed">
                          {benefit.description}
                        </p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* AI Technology Highlight - More organic design */}
          <motion.div
            variants={itemVariants}
            className="text-center relative overflow-hidden"
          >
            {/* Background gradient */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 rounded-3xl" />
            <div className="absolute inset-0 bg-gradient-to-br from-transparent via-background/50 to-transparent rounded-3xl" />

            <div className="relative max-w-4xl mx-auto p-12">
              <div className="flex items-center justify-center gap-3 mb-6">
                <div className="p-3 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10">
                  <Zap className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                  Next-Generation Conversation AI
                </h3>
              </div>
              <p className="text-muted-foreground text-lg leading-relaxed">
                Our proprietary AI models are specifically trained for anonymous conversations,
                understanding context without compromising privacy. Unlike generic moderation tools,
                VeilMeet's AI enhances the human experience while maintaining complete anonymity.
              </p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};
