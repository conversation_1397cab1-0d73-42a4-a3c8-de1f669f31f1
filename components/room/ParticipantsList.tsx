'use client';

import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  <PERSON>,
  Mic,
  MicOff,
  MessageSquare,
  Clock
} from 'lucide-react';
import { generateAvatarUrl, formatRelativeTime } from '@/lib/identity';

interface Participant {
  sessionId: string;
  anonymousAlias: string;
  avatarSeed: string;
  joinedAt: number;
  messageCount: number;
  isActive: boolean;
  isMuted: boolean;
}

interface VoiceParticipant {
  sessionId: string;
  anonymousAlias: string;
  isMuted: boolean;
  isSpeaking: boolean;
  audioEnabled: boolean;
  joinedAt: number;
}

interface ParticipantsListProps {
  participants: Participant[];
  voiceParticipants?: VoiceParticipant[];
  currentSessionId?: string;
  showDetails?: boolean;
}

export function ParticipantsList({
  participants,
  voiceParticipants = [],
  currentSessionId,
  showDetails = false
}: ParticipantsListProps) {
  // Create a map of voice participants for quick lookup
  const voiceParticipantMap = new Map(
    voiceParticipants.map(vp => [vp.sessionId, vp])
  );

  // Sort participants: current user first, then by join time
  const sortedParticipants = [...participants].sort((a, b) => {
    if (a.sessionId === currentSessionId) return -1;
    if (b.sessionId === currentSessionId) return 1;
    return a.joinedAt - b.joinedAt;
  });

  if (participants.length === 0) {
    return (
      <div className="text-center py-4 text-sm text-muted-foreground">
        No participants yet
      </div>
    );
  }

  return (
    <ScrollArea className="max-h-64 scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent">
      <div className="space-y-2 pr-2">
        {sortedParticipants.map((participant) => {
          const isCurrentUser = participant.sessionId === currentSessionId;
          const isCreator = false; // TODO: Implement creator detection
          const voiceParticipant = voiceParticipantMap.get(participant.sessionId);
          const isInVoice = !!voiceParticipant;

          return (
            <div
              key={participant.sessionId}
              className={`flex items-center gap-3 p-2 rounded-lg transition-colors ${
                isCurrentUser 
                  ? 'bg-primary/10 border border-primary/20' 
                  : 'hover:bg-muted/50'
              }`}
            >
              {/* Avatar */}
              <div className="relative">
                <img
                  src={generateAvatarUrl(participant.avatarSeed)}
                  alt={participant.anonymousAlias}
                  className="w-8 h-8 rounded-full"
                />
                {/* Online indicator */}
                {participant.isActive && (
                  <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-background" />
                )}
              </div>

              {/* Participant Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className={`text-sm font-medium truncate ${
                    isCurrentUser ? 'text-primary' : 'text-foreground'
                  }`}>
                    {participant.anonymousAlias}
                  </span>
                  
                  {/* Badges */}
                  <div className="flex items-center gap-1">
                    {isCurrentUser && (
                      <Badge variant="outline" className="text-xs px-1 py-0">
                        You
                      </Badge>
                    )}
                    {isCreator && (
                      <Crown className="h-3 w-3 text-yellow-500" />
                    )}
                  </div>
                </div>

                {/* Details */}
                {showDetails && (
                  <div className="flex items-center gap-3 mt-1 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <MessageSquare className="h-3 w-3" />
                      <span>{participant.messageCount}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{formatRelativeTime(participant.joinedAt)}</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Status Icons */}
              <div className="flex items-center gap-1">
                {/* Voice status indicator */}
                {isInVoice && (
                  <div className={`p-1 rounded-full ${
                    voiceParticipant.isSpeaking
                      ? 'bg-blue-500/20 text-blue-600'
                      : voiceParticipant.isMuted
                        ? 'bg-red-500/20 text-red-600'
                        : 'bg-green-500/20 text-green-600'
                  }`}>
                    {voiceParticipant.isMuted ? (
                      <MicOff className="h-3 w-3" />
                    ) : (
                      <Mic className="h-3 w-3" />
                    )}
                  </div>
                )}
                {!participant.isActive && (
                  <div className="w-2 h-2 bg-muted-foreground rounded-full opacity-50" />
                )}
              </div>
            </div>
          );
        })}
      </div>
    </ScrollArea>
  );
}
