import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Room code generation utilities
const ADJECTIVES = [
  "sunset", "calm", "bright", "silent", "quick", "gentle", "bold", "clever", 
  "wise", "thoughtful", "peaceful", "vibrant", "mystic", "serene", "cosmic"
];

const ANIMALS = [
  "wolf", "phoenix", "eagle", "fox", "owl", "tiger", "bear", "raven", 
  "lion", "deer", "falcon", "dragon", "panther", "swan", "hawk"
];

function generateRoomCode(): string {
  const adjective = ADJECTIVES[Math.floor(Math.random() * ADJECTIVES.length)];
  const animal = ANIMALS[Math.floor(Math.random() * ANIMALS.length)];
  const digits = Math.floor(1000 + Math.random() * 9000); // 4-digit number
  return `${adjective}-${animal}-${digits}`;
}

// Create a new room
export const createRoom = mutation({
  args: {
    topic: v.optional(v.string()),
    isPublic: v.optional(v.boolean()),
    maxParticipants: v.optional(v.number()),
    allowVoice: v.optional(v.boolean()),
    allowVideo: v.optional(v.boolean()),
    aiModerationLevel: v.optional(v.union(v.literal("light"), v.literal("moderate"), v.literal("strict"))),
  },
  handler: async (ctx, args) => {
    // Generate unique room code
    let roomCode: string;
    let attempts = 0;
    do {
      roomCode = generateRoomCode();
      const existing = await ctx.db
        .query("rooms")
        .withIndex("by_room_code", (q) => q.eq("roomCode", roomCode))
        .first();
      attempts++;
      if (!existing) break;
    } while (attempts < 10);

    if (attempts >= 10) {
      throw new Error("Failed to generate unique room code");
    }

    // Set default values
    const now = Date.now();
    const expiresAt = now + (24 * 60 * 60 * 1000); // 24 hours from now

    const allowVoice = args.allowVoice ?? true;

    // Create room
    const roomId = await ctx.db.insert("rooms", {
      roomCode,
      status: "active",
      topic: args.topic,
      isPublic: args.isPublic ?? false,
      maxParticipants: args.maxParticipants ?? 10,
      currentParticipants: 0,
      createdAt: now,
      expiresAt,

      // AI Configuration
      aiModerationLevel: args.aiModerationLevel ?? "moderate",
      aiAgentsEnabled: ["moderator"],

      // Room Settings
      allowVoice,
      allowVideo: args.allowVideo ?? false,
      allowScreenShare: false,
      voiceModulationEnabled: true,

      // LiveKit Voice Configuration
      voiceRoomEnabled: allowVoice,
      voiceRoomStatus: allowVoice ? "inactive" : "inactive",
      maxVoiceParticipants: args.maxParticipants ?? 10,
      currentVoiceParticipants: 0,
      voiceRoomSettings: {
        muteOnJoin: false,
        pushToTalk: false,
        noiseSuppressionEnabled: true,
        echoCancellationEnabled: true,
        autoGainControlEnabled: true,
        spatialAudioEnabled: false,
        recordVoiceSession: false,
      },

      // Privacy Settings
      recordingAllowed: false,
      ephemeralMode: true,

      // Metadata
      totalMessages: 0,
    });

    // Create LiveKit voice room if voice is enabled
    let voiceRoomId = null;
    if (allowVoice) {
      try {
        // Create voice room directly (inline logic to avoid mutation-in-mutation issue)
        const livekitRoomName = `voice_${roomCode}_${now}`;

        voiceRoomId = await ctx.db.insert("voiceRooms", {
          roomId,
          livekitRoomName,
          maxParticipants: args.maxParticipants ?? 10,
          currentParticipants: 0,
          isRecording: false,
          status: "active",
          createdAt: now,
          startedAt: now,
          totalSpeakingTime: 0,
          totalParticipants: 0,
          peakParticipants: 0,
          aiVoiceModerationEnabled: (args.aiModerationLevel ?? "moderate") !== "light",
          moderationEvents: 0,
          expiresAt,
        });
      } catch (error) {
        console.error("Failed to create voice room:", error);
        // Continue without voice room - voice can be enabled later
      }
    }

    return {
      roomId,
      roomCode,
      shareUrl: `/join/${roomCode}`,
      voiceRoomId,
      voiceEnabled: allowVoice,
    };
  },
});

// Get room by code
export const getRoomByCode = query({
  args: { roomCode: v.string() },
  handler: async (ctx, args) => {
    const room = await ctx.db
      .query("rooms")
      .withIndex("by_room_code", (q) => q.eq("roomCode", args.roomCode))
      .first();

    if (!room) {
      return null;
    }

    // Check if room is expired
    if (room.expiresAt < Date.now() && room.status !== "ended") {
      // Return expired status without modifying (queries can't mutate)
      return { ...room, status: "ended" as const };
    }

    return room;
  },
});

// Get room by ID
export const getRoomById = query({
  args: { roomId: v.id("rooms") },
  handler: async (ctx, args) => {
    const room = await ctx.db.get(args.roomId);

    if (!room) {
      return null;
    }

    // Check if room is expired
    if (room.expiresAt < Date.now() && room.status !== "ended") {
      // Return expired status without modifying (queries can't mutate)
      return { ...room, status: "ended" as const };
    }

    return room;
  },
});

// Join a room
export const joinRoom = mutation({
  args: {
    roomCode: v.string(),
    sessionId: v.string(),
    anonymousAlias: v.string(),
    avatarSeed: v.string(),
  },
  handler: async (ctx, args) => {
    // Get room
    const room = await ctx.db
      .query("rooms")
      .withIndex("by_room_code", (q) => q.eq("roomCode", args.roomCode))
      .first();

    if (!room) {
      throw new Error("Room not found");
    }

    if (room.status !== "active") {
      throw new Error("Room is not active");
    }

    if (room.currentParticipants >= room.maxParticipants) {
      throw new Error("Room is full");
    }

    // Check if participant already exists
    const existingParticipant = await ctx.db
      .query("participants")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => q.eq(q.field("roomId"), room._id))
      .first();

    if (existingParticipant) {
      // Reactivate existing participant
      await ctx.db.patch(existingParticipant._id, {
        isActive: true,
        lastActiveAt: Date.now(),
      });
      return { participantId: existingParticipant._id, roomId: room._id };
    }

    // Create new participant
    const now = Date.now();
    const participantId = await ctx.db.insert("participants", {
      roomId: room._id,
      sessionId: args.sessionId,
      anonymousAlias: args.anonymousAlias,
      avatarSeed: args.avatarSeed,
      voiceModulationSettings: {
        pitchShift: 0,
        toneType: "neutral",
        enabled: false,
      },
      voiceEnabled: true,
      preferredVoiceMode: "voice_activation",
      joinedAt: now,
      lastActiveAt: now,
      isCurrentlyInVoice: false,
      totalVoiceTime: 0,
      messageCount: 0,
      speakingTime: 0,
      reactionsGiven: 0,
      reactionsReceived: 0,
      isActive: true,
      isMuted: false,
      isBlocked: false,
    });

    // Update room participant count
    await ctx.db.patch(room._id, {
      currentParticipants: room.currentParticipants + 1,
    });

    return { participantId, roomId: room._id };
  },
});

// Leave a room
export const leaveRoom = mutation({
  args: {
    roomId: v.id("rooms"),
    sessionId: v.string(),
  },
  handler: async (ctx, args) => {
    // Find participant
    const participant = await ctx.db
      .query("participants")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId))
      .filter((q) => q.eq(q.field("roomId"), args.roomId))
      .first();

    if (!participant) {
      return; // Already left or never joined
    }

    // Mark as inactive
    await ctx.db.patch(participant._id, {
      isActive: false,
      leftAt: Date.now(),
    });

    // Update room participant count
    const room = await ctx.db.get(args.roomId);
    if (room) {
      await ctx.db.patch(args.roomId, {
        currentParticipants: Math.max(0, room.currentParticipants - 1),
      });
    }
  },
});

// Get room participants
export const getRoomParticipants = query({
  args: { roomId: v.id("rooms") },
  handler: async (ctx, args) => {
    const participants = await ctx.db
      .query("participants")
      .withIndex("by_room_active", (q) => 
        q.eq("roomId", args.roomId).eq("isActive", true)
      )
      .collect();

    return participants.map(p => ({
      sessionId: p.sessionId,
      anonymousAlias: p.anonymousAlias,
      avatarSeed: p.avatarSeed,
      joinedAt: p.joinedAt,
      messageCount: p.messageCount,
      isActive: p.isActive,
      isMuted: p.isMuted,
    }));
  },
});

// End a room (creator only)
export const endRoom = mutation({
  args: {
    roomId: v.id("rooms"),
    creatorSessionId: v.string(),
  },
  handler: async (ctx, args) => {
    const room = await ctx.db.get(args.roomId);
    if (!room) {
      throw new Error("Room not found");
    }

    // For now, we'll allow any participant to end the room
    // In a real implementation, you'd verify creator privileges
    await ctx.db.patch(args.roomId, {
      status: "ended",
      expiresAt: Date.now(), // Expire immediately
    });

    // Mark all participants as inactive
    const participants = await ctx.db
      .query("participants")
      .withIndex("by_room", (q) => q.eq("roomId", args.roomId))
      .collect();

    for (const participant of participants) {
      await ctx.db.patch(participant._id, {
        isActive: false,
        leftAt: Date.now(),
      });
    }
  },
});

// Get active rooms (for discovery)
export const getActiveRooms = query({
  args: {},
  handler: async (ctx) => {
    const rooms = await ctx.db
      .query("rooms")
      .withIndex("by_public_active", (q) =>
        q.eq("isPublic", true).eq("status", "active")
      )
      .filter((q) => q.gt(q.field("expiresAt"), Date.now()))
      .take(20);

    return rooms.map(room => ({
      roomCode: room.roomCode,
      topic: room.topic,
      currentParticipants: room.currentParticipants,
      maxParticipants: room.maxParticipants,
      createdAt: room.createdAt,
    }));
  },
});

// Update room settings (creator only)
export const updateRoomSettings = mutation({
  args: {
    roomId: v.id("rooms"),
    creatorSessionId: v.string(),
    updates: v.object({
      topic: v.optional(v.string()),
      isPublic: v.optional(v.boolean()),
      maxParticipants: v.optional(v.number()),
      allowVoice: v.optional(v.boolean()),
      allowVideo: v.optional(v.boolean()),
      allowScreenShare: v.optional(v.boolean()),
      voiceModulationEnabled: v.optional(v.boolean()),
      recordingAllowed: v.optional(v.boolean()),
      ephemeralMode: v.optional(v.boolean()),
      aiModerationLevel: v.optional(v.union(v.literal("light"), v.literal("moderate"), v.literal("strict"))),
    }),
  },
  handler: async (ctx, args) => {
    const room = await ctx.db.get(args.roomId);
    if (!room) {
      throw new Error("Room not found");
    }

    // For now, we'll allow any participant to update settings
    // In a real implementation, you'd verify creator privileges
    await ctx.db.patch(args.roomId, args.updates);

    return { success: true };
  },
});

// Mute participant (creator/moderator only)
export const muteParticipant = mutation({
  args: {
    roomId: v.id("rooms"),
    targetSessionId: v.string(),
    moderatorSessionId: v.string(),
  },
  handler: async (ctx, args) => {
    // Find target participant
    const participant = await ctx.db
      .query("participants")
      .withIndex("by_session", (q) => q.eq("sessionId", args.targetSessionId))
      .filter((q) => q.eq(q.field("roomId"), args.roomId))
      .first();

    if (!participant) {
      throw new Error("Participant not found");
    }

    // Update participant status
    await ctx.db.patch(participant._id, {
      isMuted: true,
    });

    // Send system message
    await ctx.db.insert("messages", {
      roomId: args.roomId,
      participantSessionId: "system",
      anonymousAlias: "System",
      content: `${participant.anonymousAlias} has been muted`,
      messageType: "system",
      createdAt: Date.now(),
      editedAt: Date.now(),
      isEdited: false,
      isDeleted: false,
      isImportant: false,
      reactions: [],
      replyToMessageId: undefined,
      threadRootId: undefined,
      threadDepth: 0,
      threadReplyCount: 0,
      threadParticipantCount: 0,
      threadLastReplyAt: undefined,
      mentions: [],
      hasMentions: false,
      engagementMetrics: {
        totalReactions: 0,
        uniqueReactors: 0,
        replyCount: 0,
        mentionCount: 0,
        viewCount: 0,
        lastEngagementAt: undefined,
      },
      attachments: [],
      sentiment: undefined,
      keyTopics: undefined,
      moderationScore: undefined,
      moderationReason: undefined,
      metadata: {},
    });

    return { success: true };
  },
});

// Unmute participant (creator/moderator only)
export const unmuteParticipant = mutation({
  args: {
    roomId: v.id("rooms"),
    targetSessionId: v.string(),
    moderatorSessionId: v.string(),
  },
  handler: async (ctx, args) => {
    // Find target participant
    const participant = await ctx.db
      .query("participants")
      .withIndex("by_session", (q) => q.eq("sessionId", args.targetSessionId))
      .filter((q) => q.eq(q.field("roomId"), args.roomId))
      .first();

    if (!participant) {
      throw new Error("Participant not found");
    }

    // Update participant status
    await ctx.db.patch(participant._id, {
      isMuted: false,
    });

    // Send system message
    await ctx.db.insert("messages", {
      roomId: args.roomId,
      participantSessionId: "system",
      anonymousAlias: "System",
      content: `${participant.anonymousAlias} has been unmuted`,
      messageType: "system",
      createdAt: Date.now(),
      editedAt: Date.now(),
      isEdited: false,
      isDeleted: false,
      isImportant: false,
      reactions: [],
      replyToMessageId: undefined,
      threadRootId: undefined,
      threadDepth: 0,
      threadReplyCount: 0,
      threadParticipantCount: 0,
      threadLastReplyAt: undefined,
      mentions: [],
      hasMentions: false,
      engagementMetrics: {
        totalReactions: 0,
        uniqueReactors: 0,
        replyCount: 0,
        mentionCount: 0,
        viewCount: 0,
        lastEngagementAt: undefined,
      },
      attachments: [],
      sentiment: undefined,
      keyTopics: undefined,
      moderationScore: undefined,
      moderationReason: undefined,
      metadata: {},
    });

    return { success: true };
  },
});

// Kick participant from room (creator/moderator only)
export const kickParticipant = mutation({
  args: {
    roomId: v.id("rooms"),
    targetSessionId: v.string(),
    moderatorSessionId: v.string(),
  },
  handler: async (ctx, args) => {
    // Find target participant
    const participant = await ctx.db
      .query("participants")
      .withIndex("by_session", (q) => q.eq("sessionId", args.targetSessionId))
      .filter((q) => q.eq(q.field("roomId"), args.roomId))
      .first();

    if (!participant) {
      throw new Error("Participant not found");
    }

    // Mark participant as inactive
    await ctx.db.patch(participant._id, {
      isActive: false,
      leftAt: Date.now(),
    });

    // Update room participant count
    const room = await ctx.db.get(args.roomId);
    if (room) {
      await ctx.db.patch(args.roomId, {
        currentParticipants: Math.max(0, room.currentParticipants - 1),
      });
    }

    // Send system message
    await ctx.db.insert("messages", {
      roomId: args.roomId,
      participantSessionId: "system",
      anonymousAlias: "System",
      content: `${participant.anonymousAlias} has been removed from the room`,
      messageType: "system",
      createdAt: Date.now(),
      editedAt: Date.now(),
      isEdited: false,
      isDeleted: false,
      isImportant: false,
      reactions: [],
      replyToMessageId: undefined,
      threadRootId: undefined,
      threadDepth: 0,
      threadReplyCount: 0,
      threadParticipantCount: 0,
      threadLastReplyAt: undefined,
      mentions: [],
      hasMentions: false,
      engagementMetrics: {
        totalReactions: 0,
        uniqueReactors: 0,
        replyCount: 0,
        mentionCount: 0,
        viewCount: 0,
        lastEngagementAt: undefined,
      },
      attachments: [],
      sentiment: undefined,
      keyTopics: undefined,
      moderationScore: undefined,
      moderationReason: undefined,
      metadata: {},
    });

    return { success: true };
  },
});
