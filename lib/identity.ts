// Anonymous identity utilities for the frontend

export interface AnonymousIdentity {
  sessionId: string;
  anonymousAlias: string;
  avatarSeed: string;
  colorTheme: string;
  createdAt: number;
}

export interface ParticipantInfo {
  sessionId: string;
  anonymousAlias: string;
  avatarSeed: string;
  joinedAt: number;
  messageCount: number;
  isActive: boolean;
  isMuted: boolean;
}

// Local storage keys
const IDENTITY_STORAGE_KEY = 'veilmind_identity';
const SESSION_STORAGE_KEY = 'veilmind_session';



// Generate avatar URL using DiceBear API
export function generateAvatarUrl(seed: string, style: string = 'avataaars'): string {
  const baseUrl = 'https://api.dicebear.com/7.x';
  const params = new URLSearchParams({
    seed,
    backgroundColor: 'transparent',
    size: '128',
  });
  
  return `${baseUrl}/${style}/svg?${params.toString()}`;
}


// Store identity in local storage
export function storeIdentity(identity: AnonymousIdentity): void {
  try {
    localStorage.setItem(IDENTITY_STORAGE_KEY, JSON.stringify(identity));
  } catch (error) {
    console.warn('Failed to store identity in localStorage:', error);
  }
}

// Retrieve identity from local storage
export function getStoredIdentity(): AnonymousIdentity | null {
  try {
    const stored = localStorage.getItem(IDENTITY_STORAGE_KEY);
    if (!stored) return null;
    
    const identity = JSON.parse(stored) as AnonymousIdentity;
    
    // Check if identity is expired (24 hours)
    const isExpired = Date.now() - identity.createdAt > (24 * 60 * 60 * 1000);
    if (isExpired) {
      clearStoredIdentity();
      return null;
    }
    
    return identity;
  } catch (error) {
    console.warn('Failed to retrieve identity from localStorage:', error);
    return null;
  }
}

// Clear stored identity
export function clearStoredIdentity(): void {
  try {
    localStorage.removeItem(IDENTITY_STORAGE_KEY);
    localStorage.removeItem(SESSION_STORAGE_KEY);
  } catch (error) {
    console.warn('Failed to clear identity from localStorage:', error);
  }
}

// Store current session info
export function storeSessionInfo(roomCode: string, roomId: string): void {
  try {
    const sessionInfo = {
      roomCode,
      roomId,
      joinedAt: Date.now(),
    };
    sessionStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(sessionInfo));
  } catch (error) {
    console.warn('Failed to store session info:', error);
  }
}

// Get current session info
export function getSessionInfo(): { roomCode: string; roomId: string; joinedAt: number } | null {
  try {
    const stored = sessionStorage.getItem(SESSION_STORAGE_KEY);
    if (!stored) return null;
    
    return JSON.parse(stored);
  } catch (error) {
    console.warn('Failed to retrieve session info:', error);
    return null;
  }
}

// Clear session info
export function clearSessionInfo(): void {
  try {
    sessionStorage.removeItem(SESSION_STORAGE_KEY);
  } catch (error) {
    console.warn('Failed to clear session info:', error);
  }
}

// Format relative time for display
export function formatRelativeTime(timestamp: number): string {
  const now = Date.now();
  const diff = now - timestamp;
  
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) return `${days}d ago`;
  if (hours > 0) return `${hours}h ago`;
  if (minutes > 0) return `${minutes}m ago`;
  if (seconds > 10) return `${seconds}s ago`;
  return 'just now';
}

// Format duration for display
export function formatDuration(startTime: number, endTime?: number): string {
  const duration = (endTime || Date.now()) - startTime;
  const seconds = Math.floor(duration / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  }
  
  if (minutes > 0) {
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  }
  
  return `${seconds}s`;
}

// Generate a unique session ID
export function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
}

// Validate room code format
export function isValidRoomCode(roomCode: string): boolean {
  // Format: adjective-animal-4digits
  const pattern = /^[a-z]+-[a-z]+-\d{4}$/;
  return pattern.test(roomCode);
}

// Extract room code from URL or input
export function extractRoomCode(input: string): string | null {
  // Handle full URLs
  if (input.includes('/')) {
    const parts = input.split('/');
    const lastPart = parts[parts.length - 1];
    return isValidRoomCode(lastPart) ? lastPart : null;
  }
  
  // Handle direct room codes
  return isValidRoomCode(input) ? input : null;
}

// Generate shareable room URL
export function generateShareUrl(roomCode: string, baseUrl?: string): string {
  const base = baseUrl || (typeof window !== 'undefined' ? window.location.origin : '');
  return `${base}/join/${roomCode}`;
}

// Copy text to clipboard
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const result = document.execCommand('copy');
      textArea.remove();
      return result;
    }
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
}

// Debounce function for typing indicators
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Throttle function for activity updates
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}
