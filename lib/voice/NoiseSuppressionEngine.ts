/**
 * VeilMind AI-Powered Noise Suppression Engine
 * Real-time noise cancellation with adaptive learning
 */

export type NoiseType = 'mechanical' | 'environmental' | 'human' | 'digital' | 'household';

export interface NoiseProfile {
  type: NoiseType;
  frequencyRange: [number, number]; // Hz
  characteristics: number[]; // Frequency signature
  suppressionLevel: number; // 0 to 1
  adaptiveWeight: number; // Learning weight
}

export interface NoiseSuppressionSettings {
  enabled: boolean;
  suppressionLevel: 'light' | 'medium' | 'strong';
  adaptiveLearning: boolean;
  preserveVoiceQuality: boolean;
  noiseProfiles: NoiseProfile[];
  spectralSubtraction: number; // 0 to 1
  wienerFiltering: number; // 0 to 1
  gateThreshold: number; // dB
}

export interface NoiseAnalysis {
  overallNoiseLevel: number; // 0 to 1
  dominantNoiseType: NoiseType | null;
  noiseSpectrum: Float32Array;
  voicePresence: number; // 0 to 1
  snrEstimate: number; // Signal-to-noise ratio in dB
}

export class NoiseSuppressionEngine {
  private audioContext: AudioContext | null = null;
  private workletNode: AudioWorkletNode | null = null;
  private analyserNode: AnalyserNode | null = null;
  private inputStream: MediaStream | null = null;
  private outputStream: MediaStream | null = null;
  
  private settings: NoiseSuppressionSettings;
  private isInitialized = false;
  private isProcessing = false;
  
  // Analysis buffers
  private frequencyData: Float32Array | null = null;
  private timeData: Float32Array | null = null;
  private noiseSpectrum: Float32Array | null = null;
  private voiceSpectrum: Float32Array | null = null;
  
  // Adaptive learning
  private noiseHistory: Float32Array[] = [];
  private voiceHistory: Float32Array[] = [];
  private readonly HISTORY_SIZE = 100;
  
  // Built-in noise profiles
  private readonly DEFAULT_NOISE_PROFILES: NoiseProfile[] = [
    {
      type: 'mechanical',
      frequencyRange: [50, 500],
      characteristics: [60, 120, 180, 240, 300], // Harmonic series
      suppressionLevel: 0.8,
      adaptiveWeight: 0.3
    },
    {
      type: 'environmental',
      frequencyRange: [20, 2000],
      characteristics: [100, 200, 400, 800, 1600], // Broadband
      suppressionLevel: 0.6,
      adaptiveWeight: 0.4
    },
    {
      type: 'human',
      frequencyRange: [80, 4000],
      characteristics: [150, 300, 600, 1200, 2400], // Voice harmonics
      suppressionLevel: 0.4, // Be careful with human voices
      adaptiveWeight: 0.2
    },
    {
      type: 'digital',
      frequencyRange: [1000, 8000],
      characteristics: [2000, 4000, 6000, 8000], // High frequency
      suppressionLevel: 0.9,
      adaptiveWeight: 0.5
    },
    {
      type: 'household',
      frequencyRange: [30, 1000],
      characteristics: [50, 100, 200, 400, 800], // Mixed frequencies
      suppressionLevel: 0.7,
      adaptiveWeight: 0.3
    }
  ];
  
  constructor(settings: Partial<NoiseSuppressionSettings> = {}) {
    this.settings = {
      enabled: true,
      suppressionLevel: 'medium',
      adaptiveLearning: true,
      preserveVoiceQuality: true,
      noiseProfiles: [...this.DEFAULT_NOISE_PROFILES],
      spectralSubtraction: 0.6,
      wienerFiltering: 0.4,
      gateThreshold: -40,
      ...settings
    };
  }
  
  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    try {
      // Create audio context
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      // Load noise suppression worklet
      await this.audioContext.audioWorklet.addModule('/noise-suppression-worklet.js');
      
      // Create worklet node
      this.workletNode = new AudioWorkletNode(this.audioContext, 'noise-suppression');
      
      // Create analyser for real-time analysis
      this.analyserNode = this.audioContext.createAnalyser();
      this.analyserNode.fftSize = 2048;
      this.analyserNode.smoothingTimeConstant = 0.3;
      
      // Initialize data arrays
      this.frequencyData = new Float32Array(this.analyserNode.frequencyBinCount);
      this.timeData = new Float32Array(this.analyserNode.fftSize);
      this.noiseSpectrum = new Float32Array(this.analyserNode.frequencyBinCount);
      this.voiceSpectrum = new Float32Array(this.analyserNode.frequencyBinCount);
      
      // Set up worklet communication
      this.workletNode.port.onmessage = this.handleWorkletMessage.bind(this);
      
      this.isInitialized = true;
      console.log('Noise Suppression Engine initialized');
      
    } catch (error) {
      console.error('Failed to initialize Noise Suppression Engine:', error);
      throw error;
    }
  }
  
  async startProcessing(inputStream: MediaStream): Promise<MediaStream> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    if (!this.audioContext || !this.workletNode || !this.analyserNode) {
      throw new Error('Noise Suppression Engine not properly initialized');
    }
    
    try {
      this.inputStream = inputStream;
      
      // Create source and destination
      const sourceNode = this.audioContext.createMediaStreamSource(inputStream);
      const destinationNode = this.audioContext.createMediaStreamDestination();
      
      // Connect audio graph
      sourceNode.connect(this.analyserNode);
      this.analyserNode.connect(this.workletNode);
      this.workletNode.connect(destinationNode);
      
      // Get output stream
      this.outputStream = destinationNode.stream;
      
      // Apply current settings
      this.updateSettings(this.settings);
      
      // Start analysis loop
      this.startAnalysis();
      
      this.isProcessing = true;
      console.log('Noise suppression processing started');
      
      return this.outputStream;
      
    } catch (error) {
      console.error('Failed to start noise suppression:', error);
      throw error;
    }
  }
  
  stopProcessing(): void {
    this.isProcessing = false;
    
    if (this.inputStream) {
      this.inputStream.getTracks().forEach(track => track.stop());
      this.inputStream = null;
    }
    
    this.outputStream = null;
    console.log('Noise suppression processing stopped');
  }
  
  private startAnalysis(): void {
    if (!this.isProcessing || !this.analyserNode || !this.frequencyData) {
      return;
    }
    
    // Get frequency data
    this.analyserNode.getFloatFrequencyData(this.frequencyData);
    
    // Analyze noise
    const analysis = this.analyzeNoise(this.frequencyData);
    
    // Update noise profiles if adaptive learning is enabled
    if (this.settings.adaptiveLearning) {
      this.updateNoiseProfiles(analysis);
    }
    
    // Send analysis to worklet
    this.workletNode!.port.postMessage({
      type: 'noiseAnalysis',
      data: analysis
    });
    
    // Schedule next analysis
    requestAnimationFrame(() => this.startAnalysis());
  }
  
  private analyzeNoise(frequencyData: Float32Array): NoiseAnalysis {
    const sampleRate = this.audioContext!.sampleRate;
    const binSize = sampleRate / (2 * frequencyData.length);
    
    let totalEnergy = 0;
    let voiceEnergy = 0;
    let noiseEnergy = 0;
    
    // Voice frequency range (80Hz - 4kHz)
    const voiceStartBin = Math.floor(80 / binSize);
    const voiceEndBin = Math.floor(4000 / binSize);
    
    // Calculate energy in different frequency ranges
    for (let i = 0; i < frequencyData.length; i++) {
      const magnitude = Math.pow(10, frequencyData[i] / 20); // Convert dB to linear
      totalEnergy += magnitude;
      
      if (i >= voiceStartBin && i <= voiceEndBin) {
        voiceEnergy += magnitude;
      }
    }
    
    noiseEnergy = totalEnergy - voiceEnergy;
    
    // Detect dominant noise type
    const dominantNoiseType = this.detectNoiseType(frequencyData);
    
    // Calculate SNR
    const snrLinear = voiceEnergy / Math.max(noiseEnergy, 0.001);
    const snrDb = 20 * Math.log10(snrLinear);
    
    return {
      overallNoiseLevel: Math.min(1, noiseEnergy / 1000), // Normalize
      dominantNoiseType,
      noiseSpectrum: new Float32Array(frequencyData),
      voicePresence: Math.min(1, voiceEnergy / 100), // Normalize
      snrEstimate: snrDb
    };
  }
  
  private detectNoiseType(frequencyData: Float32Array): NoiseType | null {
    const sampleRate = this.audioContext!.sampleRate;
    const binSize = sampleRate / (2 * frequencyData.length);
    
    let bestMatch: NoiseType | null = null;
    let bestScore = 0;
    
    for (const profile of this.settings.noiseProfiles) {
      let score = 0;
      let count = 0;
      
      // Check energy in characteristic frequencies
      for (const freq of profile.characteristics) {
        const bin = Math.floor(freq / binSize);
        if (bin < frequencyData.length) {
          const magnitude = Math.pow(10, frequencyData[bin] / 20);
          score += magnitude;
          count++;
        }
      }
      
      if (count > 0) {
        score /= count;
        
        // Weight by frequency range coverage
        const [minFreq, maxFreq] = profile.frequencyRange;
        const minBin = Math.floor(minFreq / binSize);
        const maxBin = Math.floor(maxFreq / binSize);
        
        let rangeEnergy = 0;
        for (let i = minBin; i <= Math.min(maxBin, frequencyData.length - 1); i++) {
          rangeEnergy += Math.pow(10, frequencyData[i] / 20);
        }
        
        score *= rangeEnergy / (maxBin - minBin + 1);
        
        if (score > bestScore) {
          bestScore = score;
          bestMatch = profile.type;
        }
      }
    }
    
    return bestScore > 0.1 ? bestMatch : null;
  }
  
  private updateNoiseProfiles(analysis: NoiseAnalysis): void {
    if (!analysis.dominantNoiseType) return;
    
    // Find the matching profile
    const profile = this.settings.noiseProfiles.find(p => p.type === analysis.dominantNoiseType);
    if (!profile) return;
    
    // Update noise history
    this.noiseHistory.push(new Float32Array(analysis.noiseSpectrum));
    if (this.noiseHistory.length > this.HISTORY_SIZE) {
      this.noiseHistory.shift();
    }
    
    // Update voice history if voice is present
    if (analysis.voicePresence > 0.3) {
      this.voiceHistory.push(new Float32Array(analysis.noiseSpectrum));
      if (this.voiceHistory.length > this.HISTORY_SIZE) {
        this.voiceHistory.shift();
      }
    }
    
    // Adapt profile characteristics
    if (this.noiseHistory.length >= 10) {
      this.adaptProfile(profile);
    }
  }
  
  private adaptProfile(profile: NoiseProfile): void {
    const sampleRate = this.audioContext!.sampleRate;
    const binSize = sampleRate / (2 * this.frequencyData!.length);
    
    // Calculate average noise spectrum
    const avgNoise = new Float32Array(this.frequencyData!.length);
    for (const spectrum of this.noiseHistory) {
      for (let i = 0; i < spectrum.length; i++) {
        avgNoise[i] += spectrum[i];
      }
    }
    
    for (let i = 0; i < avgNoise.length; i++) {
      avgNoise[i] /= this.noiseHistory.length;
    }
    
    // Find peaks in the noise spectrum within the profile's frequency range
    const [minFreq, maxFreq] = profile.frequencyRange;
    const minBin = Math.floor(minFreq / binSize);
    const maxBin = Math.floor(maxFreq / binSize);
    
    const peaks: number[] = [];
    for (let i = minBin + 1; i < Math.min(maxBin, avgNoise.length - 1); i++) {
      if (avgNoise[i] > avgNoise[i - 1] && avgNoise[i] > avgNoise[i + 1]) {
        const frequency = i * binSize;
        const magnitude = Math.pow(10, avgNoise[i] / 20);
        
        if (magnitude > 0.1) { // Significant peak
          peaks.push(frequency);
        }
      }
    }
    
    // Update profile characteristics with adaptive weight
    if (peaks.length > 0) {
      const weight = profile.adaptiveWeight;
      const newCharacteristics = peaks.slice(0, 5); // Keep top 5 peaks
      
      // Blend old and new characteristics
      for (let i = 0; i < Math.min(profile.characteristics.length, newCharacteristics.length); i++) {
        profile.characteristics[i] = 
          profile.characteristics[i] * (1 - weight) + 
          newCharacteristics[i] * weight;
      }
    }
  }
  
  private handleWorkletMessage(event: MessageEvent): void {
    const { type, data } = event.data;
    
    switch (type) {
      case 'processingStats':
        // Handle processing statistics from worklet
        console.log('Noise suppression stats:', data);
        break;
      
      case 'error':
        console.error('Noise suppression worklet error:', data);
        break;
    }
  }
  
  updateSettings(newSettings: Partial<NoiseSuppressionSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    
    if (this.workletNode && this.isProcessing) {
      // Send updated settings to worklet
      this.workletNode.port.postMessage({
        type: 'updateSettings',
        data: this.settings
      });
    }
  }
  
  setSuppressionLevel(level: 'light' | 'medium' | 'strong'): void {
    const levels = {
      light: { spectralSubtraction: 0.3, wienerFiltering: 0.2, gateThreshold: -35 },
      medium: { spectralSubtraction: 0.6, wienerFiltering: 0.4, gateThreshold: -40 },
      strong: { spectralSubtraction: 0.8, wienerFiltering: 0.6, gateThreshold: -45 }
    };
    
    this.updateSettings({
      suppressionLevel: level,
      ...levels[level]
    });
  }
  
  getSettings(): NoiseSuppressionSettings {
    return { ...this.settings };
  }
  
  getCurrentAnalysis(): NoiseAnalysis | null {
    if (!this.frequencyData || !this.isProcessing) {
      return null;
    }
    
    return this.analyzeNoise(this.frequencyData);
  }
  
  isReady(): boolean {
    return this.isInitialized;
  }
  
  isActive(): boolean {
    return this.isProcessing;
  }
  
  getOutputStream(): MediaStream | null {
    return this.outputStream;
  }
  
  // Cleanup
  dispose(): void {
    this.stopProcessing();
    
    if (this.workletNode) {
      this.workletNode.disconnect();
      this.workletNode = null;
    }
    
    if (this.analyserNode) {
      this.analyserNode.disconnect();
      this.analyserNode = null;
    }
    
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
      this.audioContext = null;
    }
    
    // Clear history
    this.noiseHistory = [];
    this.voiceHistory = [];
    
    this.isInitialized = false;
    console.log('Noise Suppression Engine disposed');
  }
}
