"use client";

import { motion } from "motion/react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Search,
  Users,
  Clock,
  ArrowRight,
  Sparkles,
  Shield,
  MessageSquare
} from "lucide-react";
import { useState } from "react";

const featuredRooms = [
  {
    title: "Tech Industry Feedback",
    description: "Anonymous discussions about workplace culture in tech companies",
    participants: 24,
    category: "Professional",
    isActive: true,
    tags: ["Tech", "Workplace", "Culture"]
  },
  {
    title: "Mental Health Support",
    description: "Safe space for sharing experiences and supporting each other",
    participants: 18,
    category: "Support",
    isActive: true,
    tags: ["Mental Health", "Support", "Community"]
  },
  {
    title: "Student Voice",
    description: "University students discussing campus issues and improvements",
    participants: 31,
    category: "Education",
    isActive: true,
    tags: ["Education", "Students", "Campus"]
  },
  {
    title: "Healthcare Workers",
    description: "Anonymous forum for healthcare professionals to share experiences",
    participants: 15,
    category: "Professional",
    isActive: true,
    tags: ["Healthcare", "Professional", "Support"]
  }
];

export function RoomDiscovery() {
  const [searchQuery, setSearchQuery] = useState("");

  return (
    <section className="py-20 px-4 bg-gradient-to-b from-primary/5 to-background">
      <div className="container mx-auto max-w-7xl">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge variant="secondary" className="mb-4">
            <Sparkles className="w-4 h-4 mr-2" />
            Discover Rooms
          </Badge>
          <h2 className="text-3xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-foreground to-primary bg-clip-text text-transparent">
            Join Active
            <br />
            Anonymous Discussions
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Explore ongoing conversations or create your own room for meaningful anonymous discussions.
          </p>
        </motion.div>

        {/* Search and Create */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="max-w-2xl mx-auto mb-16"
        >
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
              <Input
                placeholder="Search for rooms or topics..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 py-6 text-lg bg-background/50 backdrop-blur-sm border-border/50"
              />
            </div>
            <Button size="lg" className="px-8 py-6 bg-gradient-to-r from-primary to-primary/80" asChild>
              <Link href="/create-room">
                Create Room
                <ArrowRight className="ml-2 w-5 h-5" />
              </Link>
            </Button>
          </div>
        </motion.div>

        {/* Featured Rooms */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <h3 className="text-2xl font-bold text-foreground mb-8 text-center">
            Featured Active Rooms
          </h3>
          <div className="grid md:grid-cols-2 gap-6">
            {featuredRooms.map((room, index) => (
              <motion.div
                key={room.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="group hover:shadow-xl transition-all duration-300 hover:scale-105 border-border/50 bg-card/50 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-semibold text-foreground group-hover:text-primary transition-colors">
                            {room.title}
                          </h4>
                          {room.isActive && (
                            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                          )}
                        </div>
                        <p className="text-muted-foreground text-sm mb-3 leading-relaxed">
                          {room.description}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-4 mb-4">
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Users className="w-4 h-4" />
                        <span>{room.participants} active</span>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {room.category}
                      </Badge>
                    </div>

                    <div className="flex flex-wrap gap-2 mb-4">
                      {room.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>

                    <Button
                      variant="outline"
                      className="w-full group-hover:bg-primary/10 transition-colors"
                      asChild
                    >
                      <Link href={`/join/${room.title.toLowerCase().replace(/\s+/g, '-')}`}>
                        Join Discussion
                        <ArrowRight className="ml-2 w-4 h-4" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="grid md:grid-cols-3 gap-6"
        >
          <Card className="bg-gradient-to-br from-primary/10 to-primary/5 border-primary/20">
            <CardContent className="p-6 text-center">
              <Shield className="w-12 h-12 text-primary mx-auto mb-4" />
              <h3 className="font-semibold text-foreground mb-2">Create Private Room</h3>
              <p className="text-muted-foreground text-sm mb-4">
                Start your own anonymous discussion with custom settings
              </p>
              <Button variant="outline" size="sm" asChild>
                <Link href="/create-room">Create Now</Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-secondary/10 to-secondary/5 border-secondary/20">
            <CardContent className="p-6 text-center">
              <MessageSquare className="w-12 h-12 text-secondary mx-auto mb-4" />
              <h3 className="font-semibold text-foreground mb-2">Join with Code</h3>
              <p className="text-muted-foreground text-sm mb-4">
                Have a room code? Join an existing discussion instantly
              </p>
              <Button variant="outline" size="sm" asChild>
                <Link href="/join">Join Room</Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-accent/10 to-accent/5 border-accent/20">
            <CardContent className="p-6 text-center">
              <Clock className="w-12 h-12 text-accent mx-auto mb-4" />
              <h3 className="font-semibold text-foreground mb-2">Browse Categories</h3>
              <p className="text-muted-foreground text-sm mb-4">
                Explore rooms by topic, industry, or interest
              </p>
              <Button variant="outline" size="sm" asChild>
                <Link href="/browse">Browse All</Link>
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
}
