"use client";

import { motion } from "motion/react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Shield, 
  Mail, 
  FileText, 
  Lock,
  Github,
  Twitter,
  Linkedin,
  ExternalLink,
  CheckCircle,
  Globe
} from "lucide-react";

const footerLinks = {
  product: [
    { name: "How It Works", href: "#how-it-works" },
    { name: "Features", href: "#features" },
    { name: "Use Cases", href: "#use-cases" },
    { name: "AI Technology", href: "#ai-capabilities" },
    { name: "Pricing", href: "#pricing" },
    { name: "Demo", href: "#demo" }
  ],
  privacy: [
    { name: "Privacy Policy", href: "/privacy" },
    { name: "Security Overview", href: "/security" },
    { name: "Data Protection", href: "/data-protection" },
    { name: "Compliance", href: "/compliance" },
    { name: "Transparency Report", href: "/transparency" },
    { name: "Security Audits", href: "/audits" }
  ],
  support: [
    { name: "Help Center", href: "/help" },
    { name: "Contact Support", href: "/support" },
    { name: "Community Forum", href: "/community" },
    { name: "API Documentation", href: "/docs" },
    { name: "Status Page", href: "/status" },
    { name: "Bug Reports", href: "/bugs" }
  ],
  company: [
    { name: "About Us", href: "/about" },
    { name: "Careers", href: "/careers" },
    { name: "Press Kit", href: "/press" },
    { name: "Blog", href: "/blog" },
    { name: "Partners", href: "/partners" },
    { name: "Investors", href: "/investors" }
  ]
};

const certifications = [
  { name: "SOC 2", icon: Shield },
  { name: "ISO 27001", icon: Lock },
  { name: "GDPR", icon: CheckCircle },
  { name: "HIPAA", icon: FileText }
];

const socialLinks = [
  { name: "Twitter", icon: Twitter, href: "https://twitter.com/veilmind" },
  { name: "LinkedIn", icon: Linkedin, href: "https://linkedin.com/company/veilmind" },
  { name: "GitHub", icon: Github, href: "https://github.com/veilmind" }
];

export function Footer() {
  return (
    <footer className="bg-gradient-to-b from-background to-primary/5 border-t border-border/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-12 sm:py-16">
          <div className="grid grid-cols-1 lg:grid-cols-6 gap-8 sm:gap-12">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <div className="flex items-center gap-2 mb-4 sm:mb-6">
                  <div className="w-6 h-6 sm:w-8 sm:h-8 bg-primary rounded-lg flex items-center justify-center">
                    <Shield className="w-4 h-4 sm:w-5 sm:h-5 text-primary-foreground" />
                  </div>
                  <span className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">VeilMeet</span>
                </div>

                <p className="text-muted-foreground mb-4 sm:mb-6 leading-relaxed text-sm sm:text-base">
                  Creating a world where every voice can be heard without revealing the speaker.
                  AI-powered anonymous conferencing that protects your identity while enhancing your conversations.
                </p>

                <div className="space-y-2 sm:space-y-3 mb-4 sm:mb-6">
                  <div className="flex items-center gap-2 text-xs sm:text-sm">
                    <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-chart-3 flex-shrink-0" />
                    <span>10,000+ anonymous sessions</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs sm:text-sm">
                    <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-chart-3 flex-shrink-0" />
                    <span>Zero privacy breaches</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs sm:text-sm">
                    <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-chart-3 flex-shrink-0" />
                    <span>Military-grade encryption</span>
                  </div>
                </div>

                <Button className="w-full sm:w-auto bg-primary hover:bg-primary/90 text-primary-foreground text-sm min-h-[40px]">
                  <Mail className="w-3 h-3 sm:w-4 sm:h-4 mr-2" />
                  Get Updates
                </Button>
              </motion.div>
            </div>

            {/* Links Sections */}
            <div className="lg:col-span-4 grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8">
              {/* Product */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <h3 className="font-semibold mb-3 sm:mb-4 text-foreground text-sm sm:text-base">Product</h3>
                <ul className="space-y-2 sm:space-y-3">
                  {footerLinks.product.map((link) => (
                    <li key={link.name}>
                      <a
                        href={link.href}
                        className="text-muted-foreground hover:text-primary transition-colors text-xs sm:text-sm"
                      >
                        {link.name}
                      </a>
                    </li>
                  ))}
                </ul>
              </motion.div>

              {/* Privacy & Security */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <h3 className="font-semibold mb-4 text-foreground">Privacy & Security</h3>
                <ul className="space-y-3">
                  {footerLinks.privacy.map((link) => (
                    <li key={link.name}>
                      <a 
                        href={link.href}
                        className="text-muted-foreground hover:text-chart-3 transition-colors text-sm"
                      >
                        {link.name}
                      </a>
                    </li>
                  ))}
                </ul>
              </motion.div>

              {/* Support */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <h3 className="font-semibold mb-4 text-foreground">Support</h3>
                <ul className="space-y-3">
                  {footerLinks.support.map((link) => (
                    <li key={link.name}>
                      <a 
                        href={link.href}
                        className="text-muted-foreground hover:text-chart-2 transition-colors text-sm"
                      >
                        {link.name}
                      </a>
                    </li>
                  ))}
                </ul>
              </motion.div>

              {/* Company */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                viewport={{ once: true }}
              >
                <h3 className="font-semibold mb-4 text-foreground">Company</h3>
                <ul className="space-y-3">
                  {footerLinks.company.map((link) => (
                    <li key={link.name}>
                      <a 
                        href={link.href}
                        className="text-muted-foreground hover:text-chart-4 transition-colors text-sm"
                      >
                        {link.name}
                      </a>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>
          </div>
        </div>

        <Separator className="bg-border/50" />

        {/* Certifications Section */}
        <motion.div
          className="py-8"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          viewport={{ once: true }}
        >
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            <div>
              <h4 className="font-semibold mb-3 text-foreground">Security Certifications</h4>
              <div className="flex items-center gap-6">
                {certifications.map((cert) => (
                  <div key={cert.name} className="flex items-center gap-2">
                    <cert.icon className="w-4 h-4 text-chart-3" />
                    <span className="text-sm text-muted-foreground">{cert.name}</span>
                  </div>
                ))}
              </div>
            </div>

            <Badge variant="outline" className="border-chart-3/30 text-chart-3">
              <Globe className="w-3 h-3 mr-1" />
              Globally Compliant
            </Badge>
          </div>
        </motion.div>

        <Separator className="bg-border/50" />

        {/* Bottom Section */}
        <motion.div
          className="py-8 flex flex-col md:flex-row items-center justify-between gap-6"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="flex flex-col md:flex-row items-center gap-6">
            <p className="text-sm text-muted-foreground">
              © 2024 VeilMeet. All rights reserved.
            </p>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <a href="/terms" className="hover:text-primary transition-colors">Terms</a>
              <a href="/privacy" className="hover:text-primary transition-colors">Privacy</a>
              <a href="/cookies" className="hover:text-primary transition-colors">Cookies</a>
            </div>
          </div>

          <div className="flex items-center gap-4">
            {socialLinks.map((social) => (
              <a
                key={social.name}
                href={social.href}
                className="w-10 h-10 rounded-lg bg-card/50 border border-border/50 flex items-center justify-center hover:border-primary/30 hover:bg-primary/10 transition-all duration-300 group"
                target="_blank"
                rel="noopener noreferrer"
              >
                <social.icon className="w-4 h-4 text-muted-foreground group-hover:text-primary transition-colors" />
              </a>
            ))}
          </div>
        </motion.div>
      </div>
    </footer>
  );
}
