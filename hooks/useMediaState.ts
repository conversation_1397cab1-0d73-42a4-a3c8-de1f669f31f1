// Enhanced media state management hook
// Production-ready hook for audio, video, and screen sharing controls

'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { MediaState, MediaPermissions, UseMediaReturn } from '@/lib/types';

// ============================================================================
// MEDIA STATE HOOK
// ============================================================================

export function useMediaState(): UseMediaReturn {
  const [mediaState, setMediaState] = useState<MediaState>({
    isAudioEnabled: false,
    isVideoEnabled: false,
    isScreenSharing: false,
    isMuted: false,
    audioLevel: 0,
    videoQuality: 'medium',
  });

  const [permissions, setPermissions] = useState<MediaPermissions>({
    audio: 'prompt',
    video: 'prompt',
    screen: 'prompt',
  });

  const [isSupported, setIsSupported] = useState(false);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const animationFrameRef = useRef<number>();

  // Check browser support
  useEffect(() => {
    const checkSupport = () => {
      const hasGetUserMedia = !!(
        navigator.mediaDevices?.getUserMedia ||
        navigator.getUserMedia ||
        (navigator as any).webkitGetUserMedia ||
        (navigator as any).mozGetUserMedia
      );

      const hasGetDisplayMedia = !!(navigator.mediaDevices?.getDisplayMedia);
      
      setIsSupported(hasGetUserMedia);
      
      // Update screen sharing permission based on support
      setPermissions(prev => ({
        ...prev,
        screen: hasGetDisplayMedia ? 'prompt' : 'denied',
      }));
    };

    checkSupport();
  }, []);

  // Check permissions
  useEffect(() => {
    const checkPermissions = async () => {
      if (!navigator.permissions) return;

      try {
        const [audioPermission, videoPermission] = await Promise.all([
          navigator.permissions.query({ name: 'microphone' as PermissionName }),
          navigator.permissions.query({ name: 'camera' as PermissionName }),
        ]);

        setPermissions(prev => ({
          ...prev,
          audio: audioPermission.state,
          video: videoPermission.state,
        }));

        // Listen for permission changes
        audioPermission.addEventListener('change', () => {
          setPermissions(prev => ({ ...prev, audio: audioPermission.state }));
        });

        videoPermission.addEventListener('change', () => {
          setPermissions(prev => ({ ...prev, video: videoPermission.state }));
        });
      } catch (error) {
        console.warn('Permission API not supported:', error);
      }
    };

    checkPermissions();
  }, []);

  // Audio level monitoring
  const startAudioLevelMonitoring = useCallback((stream: MediaStream) => {
    if (!audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
    }

    const audioContext = audioContextRef.current;
    const source = audioContext.createMediaStreamSource(stream);
    const analyser = audioContext.createAnalyser();
    
    analyser.fftSize = 256;
    analyser.smoothingTimeConstant = 0.8;
    source.connect(analyser);
    
    analyserRef.current = analyser;

    const dataArray = new Uint8Array(analyser.frequencyBinCount);
    
    const updateAudioLevel = () => {
      if (!analyserRef.current) return;
      
      analyserRef.current.getByteFrequencyData(dataArray);
      const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
      const normalizedLevel = Math.min(average / 128, 1);
      
      setMediaState(prev => ({ ...prev, audioLevel: normalizedLevel }));
      animationFrameRef.current = requestAnimationFrame(updateAudioLevel);
    };

    updateAudioLevel();
  }, []);

  const stopAudioLevelMonitoring = useCallback(() => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    if (audioContextRef.current?.state !== 'closed') {
      audioContextRef.current?.close();
      audioContextRef.current = null;
    }
    analyserRef.current = null;
  }, []);

  // Toggle audio
  const toggleAudio = useCallback(async () => {
    try {
      if (mediaState.isAudioEnabled) {
        // Disable audio
        if (streamRef.current) {
          streamRef.current.getAudioTracks().forEach(track => {
            track.stop();
          });
        }
        stopAudioLevelMonitoring();
        setMediaState(prev => ({ 
          ...prev, 
          isAudioEnabled: false, 
          audioLevel: 0 
        }));
      } else {
        // Enable audio
        const stream = await navigator.mediaDevices.getUserMedia({ 
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
          }
        });
        
        streamRef.current = stream;
        startAudioLevelMonitoring(stream);
        setMediaState(prev => ({ ...prev, isAudioEnabled: true }));
      }
    } catch (error) {
      console.error('Error toggling audio:', error);
      throw new Error('Failed to toggle audio. Please check your microphone permissions.');
    }
  }, [mediaState.isAudioEnabled, startAudioLevelMonitoring, stopAudioLevelMonitoring]);

  // Toggle video
  const toggleVideo = useCallback(async () => {
    try {
      if (mediaState.isVideoEnabled) {
        // Disable video
        if (streamRef.current) {
          streamRef.current.getVideoTracks().forEach(track => {
            track.stop();
          });
        }
        setMediaState(prev => ({ ...prev, isVideoEnabled: false }));
      } else {
        // Enable video
        const constraints = {
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            frameRate: { ideal: 30 },
            facingMode: 'user',
          }
        };

        // Adjust quality based on current setting
        if (mediaState.videoQuality === 'low') {
          constraints.video.width = { ideal: 640 };
          constraints.video.height = { ideal: 480 };
          constraints.video.frameRate = { ideal: 15 };
        } else if (mediaState.videoQuality === 'high') {
          constraints.video.width = { ideal: 1920 };
          constraints.video.height = { ideal: 1080 };
        }

        const stream = await navigator.mediaDevices.getUserMedia(constraints);
        streamRef.current = stream;
        setMediaState(prev => ({ ...prev, isVideoEnabled: true }));
      }
    } catch (error) {
      console.error('Error toggling video:', error);
      throw new Error('Failed to toggle video. Please check your camera permissions.');
    }
  }, [mediaState.isVideoEnabled, mediaState.videoQuality]);

  // Toggle screen sharing
  const toggleScreenShare = useCallback(async () => {
    try {
      if (mediaState.isScreenSharing) {
        // Stop screen sharing
        if (streamRef.current) {
          streamRef.current.getVideoTracks().forEach(track => {
            track.stop();
          });
        }
        setMediaState(prev => ({ ...prev, isScreenSharing: false }));
      } else {
        // Start screen sharing
        if (!navigator.mediaDevices?.getDisplayMedia) {
          throw new Error('Screen sharing is not supported in this browser');
        }

        const stream = await navigator.mediaDevices.getDisplayMedia({
          video: {
            mediaSource: 'screen',
          },
          audio: true, // Include system audio if available
        });

        // Listen for user stopping screen share via browser UI
        stream.getVideoTracks()[0].addEventListener('ended', () => {
          setMediaState(prev => ({ ...prev, isScreenSharing: false }));
        });

        streamRef.current = stream;
        setMediaState(prev => ({ ...prev, isScreenSharing: true }));
      }
    } catch (error) {
      console.error('Error toggling screen share:', error);
      throw new Error('Failed to toggle screen sharing. Please try again.');
    }
  }, [mediaState.isScreenSharing]);

  // Set muted state
  const setMuted = useCallback((muted: boolean) => {
    if (streamRef.current) {
      streamRef.current.getAudioTracks().forEach(track => {
        track.enabled = !muted;
      });
    }
    setMediaState(prev => ({ ...prev, isMuted: muted }));
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      stopAudioLevelMonitoring();
    };
  }, [stopAudioLevelMonitoring]);

  return {
    mediaState,
    toggleAudio,
    toggleVideo,
    toggleScreenShare,
    setMuted,
    isSupported,
    permissions,
  };
}

// ============================================================================
// UTILITY HOOKS
// ============================================================================

/**
 * Hook for detecting media device changes
 */
export function useMediaDevices() {
  const [devices, setDevices] = useState<MediaDeviceInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const updateDevices = async () => {
      try {
        const deviceList = await navigator.mediaDevices.enumerateDevices();
        setDevices(deviceList);
      } catch (error) {
        console.error('Error enumerating devices:', error);
      } finally {
        setIsLoading(false);
      }
    };

    updateDevices();

    // Listen for device changes
    navigator.mediaDevices?.addEventListener('devicechange', updateDevices);

    return () => {
      navigator.mediaDevices?.removeEventListener('devicechange', updateDevices);
    };
  }, []);

  const audioInputs = devices.filter(device => device.kind === 'audioinput');
  const videoInputs = devices.filter(device => device.kind === 'videoinput');
  const audioOutputs = devices.filter(device => device.kind === 'audiooutput');

  return {
    devices,
    audioInputs,
    videoInputs,
    audioOutputs,
    isLoading,
  };
}
