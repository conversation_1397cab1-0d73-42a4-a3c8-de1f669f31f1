'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Mic,
  MicOff,
  Phone,
  PhoneOff,
  Volume2,
  VolumeX,
  Settings,
  WifiOff,
  Signal,
  Loader2,
  AlertCircle,
  Headphones,
  Users,
  Clock,
  Waves
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useViewport } from '@/hooks/useViewport';
import { useVoiceRoom } from '@/hooks/useVoiceRoom';
import { Id } from '@/convex/_generated/dataModel';
import { toast } from 'sonner';

interface ModernVoiceControlsProps {
  roomId: Id<'rooms'>;
  participantCount?: number;
  disabled?: boolean;
  className?: string;
  enableKeyboardShortcuts?: boolean;
}

interface ConnectionIndicatorProps {
  quality: 'excellent' | 'good' | 'poor' | 'disconnected' | null;
  className?: string;
}

function ConnectionIndicator({ quality, className }: ConnectionIndicatorProps) {
  if (!quality) return null;

  const getIndicatorProps = () => {
    switch (quality) {
      case 'excellent':
        return {
          color: 'text-green-500',
          bgColor: 'bg-green-500',
          icon: Signal,
          label: 'Excellent connection',
          bars: 4
        };
      case 'good':
        return {
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-500',
          icon: Signal,
          label: 'Good connection',
          bars: 3
        };
      case 'poor':
        return {
          color: 'text-orange-500',
          bgColor: 'bg-orange-500',
          icon: Signal,
          label: 'Poor connection',
          bars: 2
        };
      case 'disconnected':
        return {
          color: 'text-red-500',
          bgColor: 'bg-red-500',
          icon: WifiOff,
          label: 'Disconnected',
          bars: 0
        };
    }
  };

  const { color, bgColor, icon: Icon, label, bars } = getIndicatorProps();

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={cn("flex items-center gap-1", className)}>
            <Icon className={cn("h-3 w-3", color)} />
            <div className="flex gap-0.5">
              {[1, 2, 3, 4].map((bar) => (
                <div
                  key={bar}
                  className={cn(
                    "w-0.5 h-2 rounded-full transition-colors duration-200",
                    bar <= bars ? bgColor : 'bg-muted'
                  )}
                />
              ))}
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p className="text-xs">{label}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Custom hook for keyboard shortcuts
function useKeyboardShortcuts(
  enabled: boolean,
  callbacks: {
    onToggleMute: () => void;
    onToggleVoice: () => void;
  }
) {
  useEffect(() => {
    if (!enabled) return;

    const handleKeyPress = (event: KeyboardEvent) => {
      // Don't trigger shortcuts if user is typing in an input
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (event.key.toLowerCase()) {
        case 'm':
          event.preventDefault();
          callbacks.onToggleMute();
          break;
        case 'v':
          event.preventDefault();
          callbacks.onToggleVoice();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [enabled, callbacks]);
}

export function ModernVoiceControls({
  roomId,
  participantCount = 0,
  disabled = false,
  className,
  enableKeyboardShortcuts = true
}: ModernVoiceControlsProps) {
  const { isMobile } = useViewport();
  const [isLoading, setIsLoading] = useState<string | null>(null);
  
  const {
    voiceState,
    participants: voiceParticipants,
    error,
    joinVoiceRoom,
    leaveVoiceRoom,
    toggleMute,
    enableVoiceForRoom,
    retryConnection,
    isVoiceEnabled,
    isVoiceActive,
    canJoinVoice,
    clearError,
  } = useVoiceRoom(roomId);

  // Handle voice room actions
  const handleJoinVoice = useCallback(async () => {
    if (!canJoinVoice || voiceState.isConnecting) return;
    
    try {
      setIsLoading('join');
      await joinVoiceRoom();
      toast.success('Joined voice chat');
    } catch (err) {
      toast.error('Failed to join voice chat');
    } finally {
      setIsLoading(null);
    }
  }, [canJoinVoice, voiceState.isConnecting, joinVoiceRoom]);

  const handleLeaveVoice = useCallback(async () => {
    try {
      setIsLoading('leave');
      await leaveVoiceRoom();
      toast.success('Left voice chat');
    } catch (err) {
      toast.error('Failed to leave voice chat');
    } finally {
      setIsLoading(null);
    }
  }, [leaveVoiceRoom]);

  const handleToggleMute = useCallback(async () => {
    if (!voiceState.isConnected) return;
    
    try {
      setIsLoading('mute');
      await toggleMute();
    } catch (err) {
      toast.error('Failed to toggle mute');
    } finally {
      setIsLoading(null);
    }
  }, [voiceState.isConnected, toggleMute]);

  const handleEnableVoice = useCallback(async () => {
    try {
      setIsLoading('enable');
      await enableVoiceForRoom();
      toast.success('Voice chat enabled for room');
    } catch (err) {
      toast.error('Failed to enable voice chat');
    } finally {
      setIsLoading(null);
    }
  }, [enableVoiceForRoom]);

  // Set up keyboard shortcuts
  useKeyboardShortcuts(enableKeyboardShortcuts && !disabled, {
    onToggleMute: handleToggleMute,
    onToggleVoice: voiceState.isConnected ? handleLeaveVoice : handleJoinVoice
  });

  // If voice is not enabled for the room
  if (!isVoiceEnabled) {
    return (
      <Card className={cn("border-border bg-card/50 backdrop-blur-sm py-2.5 shadow-none", className)}>
        <CardContent className="px-2.5 py-0">
          <div className="flex items-center justify-between gap-2.5">
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Volume2 className="h-4 w-4" />
                <span>Voice chat not enabled</span>
              </div>
            </div>
            <Button 
              onClick={handleEnableVoice}
              disabled={disabled || isLoading === 'enable'}
              size="sm"
              variant="outline"
            >
              {isLoading === 'enable' && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Enable Voice
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("border-border bg-card/50 backdrop-blur-sm py-2.5 shadow-none", className)}>
      <CardContent className="px-2.5 py-0">
        <div className="flex items-center justify-between gap-2.5">
          {/* Voice Controls */}
          <div className="flex items-center gap-2">
            {voiceState.isConnected ? (
              <>
                {/* Mute/Unmute Button */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant={voiceState.isMuted ? "outline" : "default"}
                        size={isMobile ? "sm" : "default"}
                        onClick={handleToggleMute}
                        disabled={disabled || isLoading === 'mute'}
                        className={cn(
                          "relative transition-all duration-200",
                          isMobile ? "h-9 w-9 p-0" : "h-10 w-10 p-0",
                          !voiceState.isMuted && "ring-2 ring-primary/20"
                        )}
                      >
                        {isLoading === 'mute' ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : voiceState.isMuted ? (
                          <MicOff className="h-4 w-4" />
                        ) : (
                          <Mic className="h-4 w-4" />
                        )}
                        
                        {/* Active indicator */}
                        {!voiceState.isMuted && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background"
                          />
                        )}

                        {/* Speaking indicator */}
                        {voiceState.isSpeaking && !voiceState.isMuted && (
                          <motion.div
                            className="absolute inset-0 rounded-md bg-green-500/20 border border-green-500/30"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 0.8, scale: 1.05 }}
                            transition={{ duration: 0.1 }}
                          />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="text-center">
                        <p className="text-xs">
                          {voiceState.isMuted ? 'Unmute microphone' : 'Mute microphone'}
                        </p>
                        {!isMobile && enableKeyboardShortcuts && (
                          <p className="text-xs text-muted-foreground mt-1">Press M</p>
                        )}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* Leave Voice Button */}
                <div className="ml-2 pl-2 border-l border-border">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="destructive"
                          size={isMobile ? "sm" : "default"}
                          onClick={handleLeaveVoice}
                          disabled={disabled || isLoading === 'leave'}
                          className={cn(
                            "transition-all duration-200",
                            isMobile ? "h-9 w-9 p-0" : "h-10 w-10 p-0"
                          )}
                        >
                          {isLoading === 'leave' ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <PhoneOff className="h-4 w-4" />
                          )}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="text-center">
                          <p className="text-xs">Leave voice chat</p>
                          {!isMobile && enableKeyboardShortcuts && (
                            <p className="text-xs text-muted-foreground mt-1">Press V</p>
                          )}
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </>
            ) : (
              /* Join Voice Button */
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="default"
                      size={isMobile ? "sm" : "default"}
                      onClick={handleJoinVoice}
                      disabled={disabled || !canJoinVoice || voiceState.isConnecting || isLoading === 'join'}
                      className={cn(
                        "transition-all duration-200",
                        isMobile ? "h-9 px-3" : "h-10 px-4"
                      )}
                    >
                      {isLoading === 'join' || voiceState.isConnecting ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          {voiceState.isConnecting ? 'Connecting...' : 'Joining...'}
                        </>
                      ) : (
                        <>
                          <Headphones className="h-4 w-4 mr-2" />
                          Join Voice
                        </>
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="text-center">
                      <p className="text-xs">Join voice chat</p>
                      {!isMobile && enableKeyboardShortcuts && (
                        <p className="text-xs text-muted-foreground mt-1">Press V</p>
                      )}
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>

          {/* Status & Info */}
          <div className="flex items-center gap-3">
            {/* Error Display */}
            {error && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearError}
                      className="h-8 w-8 p-0 text-red-500 hover:text-red-600"
                    >
                      <AlertCircle className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs max-w-48">{error}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}

            {/* Connection Status */}
            <div className="flex items-center gap-2">
              <ConnectionIndicator quality={voiceState.connectionQuality} />
              
              {/* Voice Participants Count */}
              {voiceState.isConnected && voiceState.participantCount > 0 && (
                <Badge variant="secondary" className="text-xs flex items-center gap-1">
                  <Mic className="h-3 w-3" />
                  {voiceState.participantCount}
                </Badge>
              )}

              {/* Total Participants Count */}
              {participantCount > 0 && (
                <Badge variant="outline" className="text-xs flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  {participantCount}
                </Badge>
              )}
            </div>

            {/* Settings Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  disabled={disabled}
                >
                  <Settings className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuItem disabled={!voiceState.isConnected}>
                  <Mic className="mr-2 h-4 w-4" />
                  Microphone Settings
                </DropdownMenuItem>
                <DropdownMenuItem disabled={!voiceState.isConnected}>
                  <Volume2 className="mr-2 h-4 w-4" />
                  Audio Settings
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem disabled={!voiceState.isConnected}>
                  <Signal className="mr-2 h-4 w-4" />
                  Connection Info
                </DropdownMenuItem>
                {error && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={retryConnection}>
                      <Loader2 className="mr-2 h-4 w-4" />
                      Retry Connection
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Compact version for mobile bottom bar
export function CompactModernVoiceControls({
  roomId,
  participantCount = 0,
  disabled = false,
  className
}: Pick<ModernVoiceControlsProps, 'roomId' | 'participantCount' | 'disabled' | 'className'>) {
  const [isLoading, setIsLoading] = useState<string | null>(null);

  const {
    voiceState,
    joinVoiceRoom,
    leaveVoiceRoom,
    toggleMute,
    enableVoiceForRoom,
    isVoiceEnabled,
    canJoinVoice,
  } = useVoiceRoom(roomId);

  const handleJoinVoice = useCallback(async () => {
    if (!canJoinVoice || voiceState.isConnecting) return;

    try {
      setIsLoading('join');
      await joinVoiceRoom();
      toast.success('Joined voice chat');
    } catch (err) {
      toast.error('Failed to join voice chat');
    } finally {
      setIsLoading(null);
    }
  }, [canJoinVoice, voiceState.isConnecting, joinVoiceRoom]);

  const handleLeaveVoice = useCallback(async () => {
    try {
      setIsLoading('leave');
      await leaveVoiceRoom();
      toast.success('Left voice chat');
    } catch (err) {
      toast.error('Failed to leave voice chat');
    } finally {
      setIsLoading(null);
    }
  }, [leaveVoiceRoom]);

  const handleToggleMute = useCallback(async () => {
    if (!voiceState.isConnected) return;

    try {
      setIsLoading('mute');
      await toggleMute();
    } catch (err) {
      toast.error('Failed to toggle mute');
    } finally {
      setIsLoading(null);
    }
  }, [voiceState.isConnected, toggleMute]);

  const handleEnableVoice = useCallback(async () => {
    try {
      setIsLoading('enable');
      await enableVoiceForRoom();
      toast.success('Voice chat enabled for room');
    } catch (err) {
      toast.error('Failed to enable voice chat');
    } finally {
      setIsLoading(null);
    }
  }, [enableVoiceForRoom]);

  if (!isVoiceEnabled) {
    return (
      <div className={cn("flex items-center justify-between gap-2 px-4 py-2 bg-card/90 backdrop-blur-sm border-t border-border", className)}>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Volume2 className="h-4 w-4" />
          <span>Voice disabled</span>
        </div>
        <Button
          onClick={handleEnableVoice}
          disabled={disabled || isLoading === 'enable'}
          size="sm"
          variant="outline"
          className="h-8"
        >
          {isLoading === 'enable' && <Loader2 className="h-4 w-4 mr-1 animate-spin" />}
          Enable
        </Button>
      </div>
    );
  }

  return (
    <div className={cn("flex items-center justify-between gap-2 px-4 py-2 bg-card/90 backdrop-blur-sm border-t border-border", className)}>
      <div className="flex items-center gap-2">
        {voiceState.isConnected ? (
          <Button
            variant={voiceState.isMuted ? "outline" : "default"}
            size="sm"
            onClick={handleToggleMute}
            disabled={disabled || isLoading === 'mute'}
            className="h-9 w-9 p-0 relative"
          >
            {isLoading === 'mute' ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : voiceState.isMuted ? (
              <MicOff className="h-4 w-4" />
            ) : (
              <Mic className="h-4 w-4" />
            )}

            {/* Speaking indicator */}
            {voiceState.isSpeaking && !voiceState.isMuted && (
              <motion.div
                className="absolute inset-0 rounded-md bg-green-500/20 border border-green-500/30"
                initial={{ opacity: 0 }}
                animate={{ opacity: 0.8, scale: 1.05 }}
                transition={{ duration: 0.1 }}
              />
            )}
          </Button>
        ) : (
          <Button
            onClick={handleJoinVoice}
            disabled={disabled || !canJoinVoice || voiceState.isConnecting || isLoading === 'join'}
            size="sm"
            className="h-9"
          >
            {isLoading === 'join' || voiceState.isConnecting ? (
              <Loader2 className="h-4 w-4 mr-1 animate-spin" />
            ) : (
              <Headphones className="h-4 w-4 mr-1" />
            )}
            {voiceState.isConnecting ? 'Connecting' : 'Join Voice'}
          </Button>
        )}
      </div>

      <div className="flex items-center gap-2">
        {/* Voice Participants Count */}
        {voiceState.isConnected && voiceState.participantCount > 0 && (
          <Badge variant="secondary" className="text-xs flex items-center gap-1">
            <Mic className="h-3 w-3" />
            {voiceState.participantCount}
          </Badge>
        )}

        {/* Total Participants Count */}
        {participantCount > 0 && (
          <Badge variant="outline" className="text-xs flex items-center gap-1">
            <Users className="h-3 w-3" />
            {participantCount}
          </Badge>
        )}

        {voiceState.isConnected && (
          <Button
            variant="destructive"
            size="sm"
            onClick={handleLeaveVoice}
            disabled={disabled || isLoading === 'leave'}
            className="h-9 w-9 p-0"
          >
            {isLoading === 'leave' ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <PhoneOff className="h-4 w-4" />
            )}
          </Button>
        )}
      </div>
    </div>
  );
}
