import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Shield, Home, ArrowLeft, Search } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 flex items-center justify-center">
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="text-center">
          {/* VeilMeet Logo & Brand */}
          <div className="flex items-center justify-center gap-3 mb-8">
            <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center">
              <Shield className="h-7 w-7 text-primary-foreground" />
            </div>
            <div className="flex flex-col items-start">
              <span className="text-xl font-semibold text-foreground">VeilMeet</span>
              <span className="text-sm text-muted-foreground">Anonymous Conferencing</span>
            </div>
          </div>

          <Card className="bg-card/50 backdrop-blur-sm border-border/50 shadow-xl">
            <CardContent className="p-8 text-center">
              <div className="mb-6">
                <div className="text-6xl font-bold text-primary mb-4">404</div>
                <h1 className="text-2xl md:text-3xl font-bold text-foreground mb-4">
                  Page Not Found
                </h1>
                <p className="text-muted-foreground text-lg mb-6">
                  The page you're looking for doesn't exist or has been moved.
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button size="lg" asChild>
                    <Link href="/">
                      <Home className="mr-2 w-4 h-4" />
                      Go Home
                    </Link>
                  </Button>
                  <Button size="lg" variant="outline" asChild>
                    <Link href="/create-room">
                      Create Room
                    </Link>
                  </Button>
                </div>
                
                <Button variant="ghost" asChild>
                  <Link href="/browse">
                    <Search className="mr-2 w-4 h-4" />
                    Browse Rooms
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          <div className="mt-8 text-center">
            <p className="text-sm text-muted-foreground">
              Need help? This page might be under development.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
