"use client";

import React from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";

interface FeatureCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  details?: string[];
  badge?: string;
  variant?: "default" | "floating" | "minimal";
  className?: string;
  iconColor?: string;
}

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.5,
      ease: "easeOut",
    },
  },
};

export function FeatureCard({
  icon: Icon,
  title,
  description,
  details,
  badge,
  variant = "default",
  className,
  iconColor = "text-primary",
}: FeatureCardProps) {
  const renderFloatingCard = () => (
    <motion.div
      variants={cardVariants}
      className={cn("group relative", className)}
      whileHover={{ y: -8 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
    >
      <div className="relative p-8 rounded-3xl veil-glass hover:border-primary/30 transition-all duration-500 hover:shadow-2xl hover:shadow-primary/10">
        {/* Floating icon */}
        <div className="absolute -top-6 left-8">
          <div className="p-4 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20 shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
            <Icon className={cn("h-8 w-8", iconColor)} />
          </div>
        </div>

        <div className="pt-8">
          {badge && (
            <div className="inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary text-xs font-medium mb-4">
              {badge}
            </div>
          )}

          <h3 className="text-2xl font-bold mb-4">{title}</h3>
          <p className="text-muted-foreground mb-6 leading-relaxed">
            {description}
          </p>

          {details && (
            <ul className="space-y-3">
              {details.map((detail, index) => (
                <li key={index} className="flex items-start gap-3 text-sm">
                  <div className="w-2 h-2 bg-gradient-to-r from-primary to-primary/70 rounded-full mt-2 flex-shrink-0" />
                  <span className="leading-relaxed">{detail}</span>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </motion.div>
  );

  const renderDefaultCard = () => (
    <motion.div
      variants={cardVariants}
      className={cn("group", className)}
      whileHover={{ scale: 1.02 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
    >
      <Card className="h-full bg-card/50 backdrop-blur-sm border-border/50 hover:border-primary/30 transition-all duration-300 hover:shadow-lg">
        <CardContent className="p-6">
          <div className="p-3 rounded-xl bg-primary/10 hover:bg-primary/20 w-fit mb-4 group-hover:scale-110 transition-all duration-300">
            <Icon className={cn("w-6 h-6", iconColor)} />
          </div>
          
          {badge && (
            <Badge variant="outline" className="mb-3 text-xs">
              {badge}
            </Badge>
          )}
          
          <h3 className="text-lg font-semibold mb-3">{title}</h3>
          <p className="text-sm text-muted-foreground leading-relaxed">
            {description}
          </p>
          
          {details && (
            <ul className="mt-4 space-y-2">
              {details.map((detail, index) => (
                <li key={index} className="flex items-start gap-2 text-xs text-muted-foreground">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                  <span>{detail}</span>
                </li>
              ))}
            </ul>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );

  const renderMinimalCard = () => (
    <motion.div
      variants={cardVariants}
      className={cn("text-center group hover:scale-105 transition-transform duration-300", className)}
      whileHover={{ y: -4 }}
    >
      <div className="p-6 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 hover:from-primary/20 hover:to-primary/10 transition-all duration-300 shadow-md hover:shadow-lg">
        <div className="p-4 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10 w-fit mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
          <Icon className={cn("h-6 w-6", iconColor)} />
        </div>
        
        {badge && (
          <Badge variant="outline" className="mb-2 text-xs">
            {badge}
          </Badge>
        )}
        
        <h4 className="font-semibold mb-2 text-sm">{title}</h4>
        <p className="text-xs text-muted-foreground leading-relaxed">
          {description}
        </p>
      </div>
    </motion.div>
  );

  switch (variant) {
    case "floating":
      return renderFloatingCard();
    case "minimal":
      return renderMinimalCard();
    default:
      return renderDefaultCard();
  }
}
