"use client";

import { motion } from "motion/react";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Building2, 
  Heart, 
  GraduationCap, 
  AlertTriangle,
  Users,
  MessageSquare,
  Lightbulb,
  Shield
} from "lucide-react";

const useCases = [
  {
    icon: Building2,
    title: "Corporate Feedback",
    description: "Enable honest employee feedback, whistleblowing, and sensitive discussions without fear of retaliation.",
    scenarios: ["Anonymous employee surveys", "Whistleblowing protection", "Leadership feedback", "Workplace concerns"],
    color: "veil-blue",
    badge: "Enterprise"
  },
  {
    icon: Heart,
    title: "Mental Health Support",
    description: "Create safe spaces for support groups, therapy sessions, and mental health discussions with complete privacy.",
    scenarios: ["Support group meetings", "Peer counseling", "Crisis intervention", "Therapy sessions"],
    color: "veil-green",
    badge: "Healthcare"
  },
  {
    icon: GraduationCap,
    title: "Educational Discussions",
    description: "Foster open academic discourse, student feedback, and sensitive topic discussions in educational settings.",
    scenarios: ["Student feedback", "Academic debates", "Sensitive topics", "Peer discussions"],
    color: "veil-purple",
    badge: "Education"
  },
  {
    icon: <PERSON><PERSON><PERSON>rian<PERSON>,
    title: "Crisis Communication",
    description: "Provide secure channels for reporting incidents, sharing sensitive information, and crisis management.",
    scenarios: ["Incident reporting", "Emergency coordination", "Sensitive intel sharing", "Crisis management"],
    color: "veil-orange",
    badge: "Emergency"
  },
  {
    icon: Lightbulb,
    title: "Innovation & Brainstorming",
    description: "Encourage creative thinking and idea sharing without judgment or attribution concerns.",
    scenarios: ["Anonymous brainstorming", "Idea generation", "Creative workshops", "Innovation sessions"],
    color: "veil-cyan",
    badge: "Innovation"
  },
  {
    icon: Users,
    title: "Community Discussions",
    description: "Enable honest community conversations about sensitive local issues, politics, and social topics.",
    scenarios: ["Local politics", "Community issues", "Social discussions", "Public forums"],
    color: "veil-red",
    badge: "Community"
  }
];

export function UseCases() {
  return (
    <section className="py-16 sm:py-20 lg:py-24 px-4 sm:px-6 lg:px-8 relative">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          className="text-center mb-12 sm:mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="mb-4 border-chart-3/30 text-chart-3 text-xs sm:text-sm">
            <Users className="w-3 h-3 mr-1" />
            Real-World Applications
          </Badge>
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 px-2">
            Where <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">Truth Matters</span>
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed px-2">
            From corporate boardrooms to support groups, VeilMind empowers honest conversations
            across industries and communities where anonymity enables authentic dialogue.
          </p>
        </motion.div>

        {/* Use Cases Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
          {useCases.map((useCase, index) => (
            <motion.div
              key={useCase.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
              className="group"
            >
              <Card className="h-full bg-card/50 backdrop-blur-sm border-border/50 hover:border-primary/30 transition-all duration-300 group-hover:shadow-lg">
                <CardContent className="p-4 sm:p-6 lg:p-8">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4 sm:mb-6">
                    <div className={`p-3 sm:p-4 rounded-xl sm:rounded-2xl bg-${useCase.color}/10 group-hover:bg-${useCase.color}/20 transition-colors flex-shrink-0`}>
                      <useCase.icon className={`w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-${useCase.color}`} />
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {useCase.badge}
                    </Badge>
                  </div>

                  {/* Content */}
                  <h3 className="text-lg sm:text-xl font-semibold mb-3 group-hover:text-veil-purple transition-colors leading-tight">
                    {useCase.title}
                  </h3>

                  <p className="text-sm sm:text-base text-muted-foreground mb-4 sm:mb-6 leading-relaxed">
                    {useCase.description}
                  </p>

                  {/* Scenarios */}
                  <div className="space-y-2">
                    <h4 className="text-xs sm:text-sm font-medium text-foreground/80 mb-2 sm:mb-3">Common Scenarios:</h4>
                    <ul className="space-y-2">
                      {useCase.scenarios.map((scenario, scenarioIndex) => (
                        <li key={scenarioIndex} className="flex items-center gap-2 text-xs sm:text-sm text-muted-foreground">
                          <div className={`w-1.5 h-1.5 rounded-full bg-${useCase.color} flex-shrink-0`} />
                          <span>{scenario}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Stats Section */}
        <motion.div
          className="mt-16 sm:mt-20 grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 px-4"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="text-center">
            <div className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent mb-2">10,000+</div>
            <div className="text-sm sm:text-base text-muted-foreground">Anonymous Sessions</div>
          </div>
          <div className="text-center">
            <div className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent mb-2">500+</div>
            <div className="text-sm sm:text-base text-muted-foreground">Organizations Trust Us</div>
          </div>
          <div className="text-center">
            <div className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent mb-2">99.9%</div>
            <div className="text-sm sm:text-base text-muted-foreground">Privacy Protection</div>
          </div>
        </motion.div>

        {/* Bottom message */}
        <motion.div
          className="text-center mt-12 sm:mt-16 px-4"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center gap-2 text-muted-foreground bg-card/30 px-4 sm:px-6 py-3 rounded-full border border-border/50 text-sm sm:text-base">
            <Shield className="w-4 h-4 text-veil-green flex-shrink-0" />
            <span className="text-center">Every conversation protected by military-grade encryption</span>
          </div>
        </motion.div>
      </div>

      {/* Background decoration */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-1/3 right-0 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl" />
        <div className="absolute bottom-1/3 left-0 w-72 h-72 bg-green-500/5 rounded-full blur-3xl" />
      </div>
    </section>
  );
}
