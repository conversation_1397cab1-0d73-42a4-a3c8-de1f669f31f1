'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { 
  Users, 
  MoreVertical, 
  Crown, 
  Shield, 
  Mic, 
  MicOff, 
  Volume2, 
  VolumeX,
  UserX,
  Flag,
  MessageSquare,
  Clock,
  Activity,
  Loader2
} from 'lucide-react';
import { generateAvatarUrl, formatRelativeTime, formatDuration } from '@/lib/identity';
import { toast } from 'sonner';

interface Participant {
  sessionId: string;
  anonymousAlias: string;
  avatarSeed: string;
  joinedAt: number;
  messageCount: number;
  isActive: boolean;
  isMuted: boolean;
  isBlocked: boolean;
  lastActiveAt: number;
  speakingTime: number;
  reactionsGiven: number;
  reactionsReceived: number;
}

interface ParticipantManagementProps {
  participants: Participant[];
  currentSessionId?: string;
  isCreator: boolean;
  onMuteParticipant?: (sessionId: string) => Promise<void>;
  onUnmuteParticipant?: (sessionId: string) => Promise<void>;
  onKickParticipant?: (sessionId: string) => Promise<void>;
  onBlockParticipant?: (sessionId: string) => Promise<void>;
  onPromoteToModerator?: (sessionId: string) => Promise<void>;
}

export function ParticipantManagement({
  participants,
  currentSessionId,
  isCreator,
  onMuteParticipant,
  onUnmuteParticipant,
  onKickParticipant,
  onBlockParticipant,
  onPromoteToModerator
}: ParticipantManagementProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedParticipant, setSelectedParticipant] = useState<Participant | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // Sort participants: current user first, then by activity
  const sortedParticipants = [...participants].sort((a, b) => {
    if (a.sessionId === currentSessionId) return -1;
    if (b.sessionId === currentSessionId) return 1;
    if (a.isActive !== b.isActive) return a.isActive ? -1 : 1;
    return b.lastActiveAt - a.lastActiveAt;
  });

  const handleParticipantAction = async (
    action: string,
    sessionId: string,
    actionFn?: (sessionId: string) => Promise<void>
  ) => {
    if (!actionFn) return;

    try {
      setActionLoading(`${action}-${sessionId}`);
      await actionFn(sessionId);
      toast.success(`Participant ${action} successfully`);
    } catch (error) {
      toast.error(`Failed to ${action} participant`);
    } finally {
      setActionLoading(null);
    }
  };

  const getParticipantStats = (participant: Participant) => {
    const sessionDuration = Date.now() - participant.joinedAt;
    const activityRate = participant.messageCount / (sessionDuration / (1000 * 60 * 60)); // messages per hour
    
    return {
      sessionDuration: formatDuration(participant.joinedAt),
      activityRate: activityRate.toFixed(1),
      lastSeen: formatRelativeTime(participant.lastActiveAt),
    };
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className="flex items-center gap-2">
          <Users className="h-4 w-4" />
          Manage Participants ({participants.length})
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-3xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Participant Management
            {isCreator && (
              <Badge variant="outline">
                <Crown className="h-3 w-3 mr-1" />
                Creator
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            {isCreator 
              ? "Manage participants and their permissions"
              : "View participant information"
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Summary Stats */}
          <div className="grid grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">{participants.length}</div>
                <div className="text-sm text-muted-foreground">Total Participants</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-green-500">
                  {participants.filter(p => p.isActive).length}
                </div>
                <div className="text-sm text-muted-foreground">Active Now</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-orange-500">
                  {participants.filter(p => p.isMuted).length}
                </div>
                <div className="text-sm text-muted-foreground">Muted</div>
              </CardContent>
            </Card>
          </div>

          {/* Participants List */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">All Participants</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-3">
                  {sortedParticipants.map((participant) => {
                    const isCurrentUser = participant.sessionId === currentSessionId;
                    const stats = getParticipantStats(participant);
                    const isActionLoading = actionLoading?.includes(participant.sessionId);

                    return (
                      <div
                        key={participant.sessionId}
                        className={`flex items-center gap-3 p-3 rounded-lg border transition-colors ${
                          isCurrentUser 
                            ? 'bg-primary/5 border-primary/20' 
                            : 'hover:bg-muted/50'
                        }`}
                      >
                        {/* Avatar and Status */}
                        <div className="relative">
                          <img
                            src={generateAvatarUrl(participant.avatarSeed)}
                            alt={participant.anonymousAlias}
                            className="w-10 h-10 rounded-full"
                          />
                          {/* Status indicators */}
                          <div className="absolute -bottom-1 -right-1 flex gap-1">
                            {participant.isActive && (
                              <div className="w-3 h-3 bg-green-500 rounded-full border-2 border-background" />
                            )}
                            {participant.isMuted && (
                              <div className="w-3 h-3 bg-red-500 rounded-full border-2 border-background flex items-center justify-center">
                                <MicOff className="h-1.5 w-1.5 text-white" />
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Participant Info */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <span className={`font-medium truncate ${
                              isCurrentUser ? 'text-primary' : 'text-foreground'
                            }`}>
                              {participant.anonymousAlias}
                            </span>
                            
                            {/* Badges */}
                            <div className="flex items-center gap-1">
                              {isCurrentUser && (
                                <Badge variant="outline" className="text-xs px-1 py-0">
                                  You
                                </Badge>
                              )}
                              {participant.isBlocked && (
                                <Badge variant="destructive" className="text-xs px-1 py-0">
                                  Blocked
                                </Badge>
                              )}
                            </div>
                          </div>

                          {/* Stats */}
                          <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <MessageSquare className="h-3 w-3" />
                              <span>{participant.messageCount}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span>{stats.sessionDuration}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Activity className="h-3 w-3" />
                              <span>Last: {stats.lastSeen}</span>
                            </div>
                          </div>
                        </div>

                        {/* Actions */}
                        {isCreator && !isCurrentUser && (
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" disabled={isActionLoading}>
                                {isActionLoading ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <MoreVertical className="h-4 w-4" />
                                )}
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              {/* Mute/Unmute */}
                              {participant.isMuted ? (
                                <DropdownMenuItem
                                  onClick={() => handleParticipantAction('unmuted', participant.sessionId, onUnmuteParticipant)}
                                >
                                  <Mic className="mr-2 h-4 w-4" />
                                  Unmute
                                </DropdownMenuItem>
                              ) : (
                                <DropdownMenuItem
                                  onClick={() => handleParticipantAction('muted', participant.sessionId, onMuteParticipant)}
                                >
                                  <MicOff className="mr-2 h-4 w-4" />
                                  Mute
                                </DropdownMenuItem>
                              )}

                              <DropdownMenuItem
                                onClick={() => setSelectedParticipant(participant)}
                              >
                                <Shield className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>

                              <DropdownMenuSeparator />

                              {/* Kick */}
                              <DropdownMenuItem
                                onClick={() => handleParticipantAction('kicked', participant.sessionId, onKickParticipant)}
                                className="text-orange-600 focus:text-orange-600"
                              >
                                <UserX className="mr-2 h-4 w-4" />
                                Kick from Room
                              </DropdownMenuItem>

                              {/* Block */}
                              <DropdownMenuItem
                                onClick={() => handleParticipantAction('blocked', participant.sessionId, onBlockParticipant)}
                                className="text-destructive focus:text-destructive"
                              >
                                <Flag className="mr-2 h-4 w-4" />
                                Block Participant
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        )}
                      </div>
                    );
                  })}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>

        {/* Participant Details Modal */}
        {selectedParticipant && (
          <Dialog open={!!selectedParticipant} onOpenChange={() => setSelectedParticipant(null)}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Participant Details</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <img
                    src={generateAvatarUrl(selectedParticipant.avatarSeed)}
                    alt={selectedParticipant.anonymousAlias}
                    className="w-12 h-12 rounded-full"
                  />
                  <div>
                    <div className="font-medium">{selectedParticipant.anonymousAlias}</div>
                    <div className="text-sm text-muted-foreground">
                      Joined {formatRelativeTime(selectedParticipant.joinedAt)}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="font-medium">Messages Sent</div>
                    <div className="text-muted-foreground">{selectedParticipant.messageCount}</div>
                  </div>
                  <div>
                    <div className="font-medium">Reactions Given</div>
                    <div className="text-muted-foreground">{selectedParticipant.reactionsGiven}</div>
                  </div>
                  <div>
                    <div className="font-medium">Reactions Received</div>
                    <div className="text-muted-foreground">{selectedParticipant.reactionsReceived}</div>
                  </div>
                  <div>
                    <div className="font-medium">Speaking Time</div>
                    <div className="text-muted-foreground">{Math.round(selectedParticipant.speakingTime / 60)}m</div>
                  </div>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </DialogContent>
    </Dialog>
  );
}
