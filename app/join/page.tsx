'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'motion/react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  ArrowRight, 
  Home, 
  Users,
  Lock,
  Sparkles
} from 'lucide-react';
import { isValidRoomCode } from '@/lib/identity';
import { toast } from 'sonner';

export default function JoinPage() {
  const [roomCode, setRoomCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleJoinRoom = async () => {
    if (!roomCode.trim()) {
      toast.error('Please enter a room code');
      return;
    }

    if (!isValidRoomCode(roomCode.trim())) {
      toast.error('Invalid room code format');
      return;
    }

    setIsLoading(true);
    
    try {
      // Navigate to the room join page
      router.push(`/join/${roomCode.trim()}`);
    } catch (error) {
      toast.error('Failed to join room');
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleJoinRoom();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* App Branding & Page Header */}
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* VeilMeet Logo & Brand */}
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center">
              <Shield className="h-6 w-6 text-primary-foreground" />
            </div>
            <div className="flex flex-col items-start">
              <span className="text-lg font-semibold text-foreground">VeilMeet</span>
              <span className="text-xs text-muted-foreground">Anonymous Conferencing</span>
            </div>
          </div>

          <Badge variant="secondary" className="mb-4">
            <Sparkles className="w-4 h-4 mr-2" />
            Join Anonymous Room
          </Badge>
          
          <h1 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-foreground to-primary bg-clip-text text-transparent">
            Join a Room
          </h1>
          <p className="text-muted-foreground text-lg">
            Enter a room code to join an anonymous discussion
          </p>
        </motion.div>

        {/* Join Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Card className="bg-card/50 backdrop-blur-sm border-border/50 shadow-xl">
            <CardHeader className="text-center">
              <CardTitle className="flex items-center justify-center gap-2">
                <Users className="w-5 h-5 text-primary" />
                Enter Room Code
              </CardTitle>
              <CardDescription>
                Ask the room creator for the room code to join the discussion
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <label htmlFor="roomCode" className="block text-sm font-medium text-foreground mb-2">
                    Room Code
                  </label>
                  <Input
                    id="roomCode"
                    type="text"
                    placeholder="Enter room code (e.g., ABC123)"
                    value={roomCode}
                    onChange={(e) => setRoomCode(e.target.value.toUpperCase())}
                    onKeyPress={handleKeyPress}
                    className="text-center text-lg font-mono tracking-wider"
                    maxLength={6}
                    disabled={isLoading}
                  />
                  <p className="text-xs text-muted-foreground mt-2">
                    Room codes are 6 characters long (letters and numbers)
                  </p>
                </div>

                <Button 
                  onClick={handleJoinRoom}
                  disabled={!roomCode.trim() || isLoading}
                  className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
                  size="lg"
                >
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-primary-foreground/30 border-t-primary-foreground rounded-full animate-spin mr-2" />
                      Joining...
                    </>
                  ) : (
                    <>
                      Join Room
                      <ArrowRight className="ml-2 w-4 h-4" />
                    </>
                  )}
                </Button>
              </div>

              {/* Divider */}
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-border" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-card px-2 text-muted-foreground">Or</span>
                </div>
              </div>

              {/* Alternative Actions */}
              <div className="space-y-3">
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/create-room">
                    Create New Room
                  </Link>
                </Button>
                
                <Button variant="ghost" className="w-full" asChild>
                  <Link href="/">
                    <Home className="mr-2 w-4 h-4" />
                    Back to Home
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Security Notice */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-8"
        >
          <Card className="bg-primary/5 border-primary/20">
            <CardContent className="p-6">
              <div className="flex items-start gap-3">
                <Lock className="w-5 h-5 text-primary flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-foreground mb-2">Privacy & Security</h3>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Your identity remains completely anonymous</li>
                    <li>• All communications are end-to-end encrypted</li>
                    <li>• No personal data is collected or stored</li>
                    <li>• Room data is automatically deleted when sessions end</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
