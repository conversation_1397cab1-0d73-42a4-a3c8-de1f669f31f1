'use client';

import React, { useCallback, useMemo } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { AlertCircle, Shield, Loader2 } from 'lucide-react';
import { useRoom, useRoomActions } from '@/hooks/useRoom';
import { useAnonymousIdentity, useRoomParticipation } from '@/hooks/useAnonymousIdentity';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { useEnhancedMessaging } from '@/hooks/useEnhancedMessaging';
// Voice functionality removed - keeping placeholder for future implementation
import { RoomInterface } from './_components/RoomInterface';
import { PageErrorBoundary } from '@/components/common/ErrorBoundary';
import { PerformanceMonitor } from '@/components/performance/OptimizedComponents';
import { generateShareUrl, copyToClipboard } from '@/lib/identity';
import { announceToScreenReader } from '@/lib/utils/accessibility';
import { toast } from 'sonner';


export default function RoomPage() {
  return (
    <PageErrorBoundary
      onError={(error, errorInfo) => {
        console.error('Room page error:', error, errorInfo);
      }}
    >
      <PerformanceMonitor name="RoomPage">
        <RoomPageContent />
      </PerformanceMonitor>
    </PageErrorBoundary>
  );
}

function RoomPageContent() {
  const params = useParams();
  const router = useRouter();
  const roomCode = params.roomCode as string;

  const { identity, isLoading: identityLoading } = useAnonymousIdentity();
  const {
    room,
    participants,
    messages,
    typingIndicators,
    currentParticipant,
    isParticipant,
    isRoomValid,
    isLoading: roomLoading
  } = useRoom(roomCode);

  const { joinRoom, isJoining } = useRoomParticipation();
  const { leaveRoom, endRoom } = useRoomActions(room?._id);
  const { handleError } = useErrorHandler({
    enableRetry: true,
    maxRetries: 3,
    enableToast: true,
  });

  const {
    toggleReaction,
    editMessage,
    deleteMessage,
    startReply,
    cancelReply,
    replyingTo
  } = useEnhancedMessaging(room?._id);

  // Voice functionality removed - keeping placeholder for future implementation
  const voiceRoom = null;


  const handleReaction = useCallback(async (messageId: string, emoji: string) => {
    try {
      await toggleReaction(messageId, emoji);
    } catch (error) {
      handleError(error as Error);
      throw error;
    }
  }, [toggleReaction, handleError]);

  const handleReply = useCallback((messageId: string, content: string, author: string) => {
    startReply(messageId as any, content, author);
  }, [startReply]);

  const handleEdit = useCallback(async (messageId: string, newContent: string) => {
    try {
      await editMessage(messageId, newContent);
    } catch (error) {
      handleError(error as Error);
      throw error;
    }
  }, [editMessage, handleError]);

  const handleDelete = useCallback(async (messageId: string) => {
    try {
      await deleteMessage(messageId);
    } catch (error) {
      handleError(error as Error);
      throw error;
    }
  }, [deleteMessage, handleError]);

  const handleMentionClick = useCallback((_participantSessionId: string) => {
    // Handle mention click
  }, []);

  const handleLeave = useCallback(async () => {
    try {
      await leaveRoom();
      router.push('/');
      toast.success('Left room successfully');
      announceToScreenReader('Left room successfully');
    } catch (error) {
      handleError(error as Error);
    }
  }, [leaveRoom, router, handleError]);

  const handleShare = useCallback(async () => {
    try {
      const shareUrl = generateShareUrl(roomCode);
      const success = await copyToClipboard(shareUrl);

      if (success) {
        toast.success('Room link copied to clipboard!');
        announceToScreenReader('Room link copied to clipboard');
      } else {
        if (navigator.share) {
          await navigator.share({
            title: 'Join VeilMeet Room',
            text: `Join this anonymous discussion room: ${room?.topic || 'Anonymous Discussion'}`,
            url: shareUrl,
          });
        } else {
          toast.error('Failed to copy link. Please copy manually: ' + shareUrl);
        }
      }
    } catch (error) {
      handleError(error as Error);
    }
  }, [roomCode, room?.topic, handleError]);

  const handleEndRoom = useCallback(async () => {
    try {
      await endRoom();
      router.push('/');
      toast.success('Room ended successfully');
      announceToScreenReader('Room ended successfully');
    } catch (error) {
      handleError(error as Error);
    }
  }, [endRoom, router, handleError]);

  const isCreator = useMemo(() => {
    return false;
  }, []);

  const transformedParticipants = useMemo(() =>
    participants.map(p => ({
      sessionId: p.sessionId,
      anonymousAlias: p.anonymousAlias,
      avatarSeed: p.avatarSeed,
      colorTheme: identity?.colorTheme || '#6366f1',
      joinedAt: p.joinedAt,
      messageCount: p.messageCount,
      isActive: p.isActive,
      isMuted: p.isMuted,
      isOnline: p.isActive
    })),
    [participants, identity?.colorTheme]
  );

  const transformedCurrentParticipant = useMemo(() =>
    currentParticipant ? {
      sessionId: currentParticipant.sessionId,
      anonymousAlias: currentParticipant.anonymousAlias,
      avatarSeed: currentParticipant.avatarSeed,
      colorTheme: identity?.colorTheme || '#6366f1',
      joinedAt: currentParticipant.joinedAt,
      messageCount: currentParticipant.messageCount,
      isActive: currentParticipant.isActive,
      isMuted: currentParticipant.isMuted,
      isOnline: currentParticipant.isActive
    } : undefined,
    [currentParticipant, identity?.colorTheme]
  );

  const transformedMessages = useMemo(() =>
    messages.map(msg => ({
      _id: msg.id,
      roomId: msg.roomId,
      participantSessionId: msg.participantSessionId,
      anonymousAlias: msg.anonymousAlias,
      content: msg.content,
      messageType: msg.messageType,
      timestamp: msg.timestamp,
      isEdited: msg.isEdited,
      editedAt: msg.editedAt,
      reactions: msg.reactions,
      replyToMessageId: msg.replyToMessageId,
      threadRootId: msg.threadRootId,
      threadDepth: msg.threadDepth,
      threadReplyCount: msg.threadReplyCount,
      mentions: msg.mentions,
      hasMentions: msg.hasMentions,
      engagementMetrics: msg.engagementMetrics
    })),
    [messages]
  );

  const handleAutoJoin = useCallback(async () => {
    if (!room || !identity || isParticipant || isJoining) return;

    try {
      await joinRoom(roomCode);
      announceToScreenReader('Successfully joined the room');
    } catch (err) {
      handleError(err as Error);
    }
  }, [room, identity, isParticipant, isJoining, joinRoom, roomCode, handleError]);

  React.useEffect(() => {
    handleAutoJoin();
  }, [handleAutoJoin]);
  if (identityLoading || roomLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg mx-auto">
            <Shield className="h-8 w-8 text-white" />
          </div>
          <div className="space-y-2">
            <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              VeilMeet
            </h1>
            <div className="flex justify-center">
              <Loader2 className="h-6 w-6 animate-spin text-purple-500" />
            </div>
            <p className="text-muted-foreground">
              {identityLoading ? 'Setting up your anonymous identity...' : 'Loading room...'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!room || !isRoomValid) {
    return (
      <div className="h-screen bg-background flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-6">
          <Card className="p-8">
            <CardContent className="space-y-6">
              <div className="mx-auto w-16 h-16 rounded-full bg-destructive/10 flex items-center justify-center">
                <AlertCircle className="w-8 h-8 text-destructive" />
              </div>
              <div className="space-y-2">
                <h1 className="text-2xl font-bold text-destructive">Room Not Available</h1>
                <p className="text-muted-foreground">
                  This room doesn't exist, has ended, or you don't have permission to access it.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button onClick={() => router.push('/')}>
                  Go Home
                </Button>
                <Button variant="outline" onClick={() => window.location.reload()}>
                  Try Again
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!isParticipant && isJoining) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg mx-auto">
            <Loader2 className="h-8 w-8 text-white animate-spin" />
          </div>
          <div className="space-y-2">
            <h1 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
              Joining Room
            </h1>
            <p className="text-muted-foreground">
              {room?.topic || 'Anonymous Discussion'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen">
      <RoomInterface
        room={room}
        participants={transformedParticipants}
        messages={transformedMessages}
        typingIndicators={typingIndicators}
        currentParticipant={transformedCurrentParticipant}
        isCreator={isCreator}

        onReaction={handleReaction}
        onReply={handleReply}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onMentionClick={handleMentionClick}
        onLeave={handleLeave}
        onShare={handleShare}
        onEndRoom={isCreator ? handleEndRoom : undefined}
        replyingTo={replyingTo}
        onCancelReply={cancelReply}
        voiceRoom={voiceRoom}
      />
    </div>
  );
}
