// Performance-optimized components
// Production-ready components with lazy loading, memoization, and efficient rendering

'use client';

import React, { 
  memo, 
  useMemo, 
  useCallback, 
  useState, 
  useEffect, 
  useRef,
  Suspense,
  lazy
} from 'react';
import { cn } from '@/lib/utils';
import { LoadingSpinner, Skeleton } from '@/components/common/LoadingStates';

// ============================================================================
// VIRTUALIZED LIST
// ============================================================================

interface VirtualizedListProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number;
  className?: string;
  onScroll?: (scrollTop: number) => void;
}

export function VirtualizedList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className,
  onScroll,
}: VirtualizedListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const scrollElementRef = useRef<HTMLDivElement>(null);

  const visibleRange = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );
    return { startIndex, endIndex };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);

  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex + 1);
  }, [items, visibleRange]);

  const totalHeight = items.length * itemHeight;
  const offsetY = visibleRange.startIndex * itemHeight;

  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = event.currentTarget.scrollTop;
    setScrollTop(newScrollTop);
    onScroll?.(newScrollTop);
  }, [onScroll]);

  return (
    <div
      ref={scrollElementRef}
      className={cn('overflow-auto', className)}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item, index) => (
            <div
              key={visibleRange.startIndex + index}
              style={{ height: itemHeight }}
            >
              {renderItem(item, visibleRange.startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// LAZY IMAGE
// ============================================================================

interface LazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  fallback?: React.ReactNode;
  placeholder?: React.ReactNode;
  threshold?: number;
  className?: string;
}

export const LazyImage = memo(function LazyImage({
  src,
  alt,
  fallback,
  placeholder,
  threshold = 0.1,
  className,
  ...props
}: LazyImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [threshold]);

  const handleLoad = useCallback(() => {
    setIsLoaded(true);
  }, []);

  const handleError = useCallback(() => {
    setHasError(true);
  }, []);

  if (hasError && fallback) {
    return <>{fallback}</>;
  }

  return (
    <div ref={imgRef} className={cn('relative', className)}>
      {!isLoaded && placeholder && (
        <div className="absolute inset-0 flex items-center justify-center">
          {placeholder}
        </div>
      )}
      
      {isInView && (
        <img
          src={src}
          alt={alt}
          onLoad={handleLoad}
          onError={handleError}
          className={cn(
            'transition-opacity duration-300',
            isLoaded ? 'opacity-100' : 'opacity-0',
            className
          )}
          {...props}
        />
      )}
    </div>
  );
});

// ============================================================================
// MEMOIZED COMPONENT WRAPPER
// ============================================================================

interface MemoizedProps {
  children: React.ReactNode;
  deps?: React.DependencyList;
  name?: string;
}

export function Memoized({ children, deps = [], name }: MemoizedProps) {
  const MemoizedComponent = useMemo(() => {
    return () => <>{children}</>;
  }, deps);

  MemoizedComponent.displayName = name || 'MemoizedComponent';

  return <MemoizedComponent />;
}

// ============================================================================
// DEBOUNCED INPUT
// ============================================================================

interface DebouncedInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  onChange: (value: string) => void;
  debounceMs?: number;
  immediate?: boolean;
}

export const DebouncedInput = memo(function DebouncedInput({
  onChange,
  debounceMs = 300,
  immediate = false,
  ...props
}: DebouncedInputProps) {
  const [value, setValue] = useState(props.value || '');
  const timeoutRef = useRef<NodeJS.Timeout>();

  const debouncedOnChange = useCallback((newValue: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    if (immediate) {
      onChange(newValue);
    } else {
      timeoutRef.current = setTimeout(() => {
        onChange(newValue);
      }, debounceMs);
    }
  }, [onChange, debounceMs, immediate]);

  const handleChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setValue(newValue);
    debouncedOnChange(newValue);
  }, [debouncedOnChange]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <input
      {...props}
      value={value}
      onChange={handleChange}
    />
  );
});

// ============================================================================
// INTERSECTION OBSERVER HOOK
// ============================================================================

export function useIntersectionObserver(
  threshold = 0.1,
  rootMargin = '0px'
) {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null);
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        setEntry(entry);
      },
      { threshold, rootMargin }
    );

    observer.observe(element);

    return () => {
      observer.disconnect();
    };
  }, [threshold, rootMargin]);

  return { elementRef, isIntersecting, entry };
}

// ============================================================================
// LAZY COMPONENT WRAPPER
// ============================================================================

interface LazyComponentProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  threshold?: number;
  rootMargin?: string;
  once?: boolean;
}

export function LazyComponent({
  children,
  fallback = <Skeleton className="h-20 w-full" />,
  threshold = 0.1,
  rootMargin = '100px',
  once = true,
}: LazyComponentProps) {
  const { elementRef, isIntersecting } = useIntersectionObserver(threshold, rootMargin);
  const [hasBeenVisible, setHasBeenVisible] = useState(false);

  useEffect(() => {
    if (isIntersecting && !hasBeenVisible) {
      setHasBeenVisible(true);
    }
  }, [isIntersecting, hasBeenVisible]);

  const shouldRender = once ? hasBeenVisible : isIntersecting;

  return (
    <div ref={elementRef}>
      {shouldRender ? children : fallback}
    </div>
  );
}

// ============================================================================
// OPTIMIZED LIST ITEM
// ============================================================================

interface OptimizedListItemProps {
  children: React.ReactNode;
  id: string | number;
  isSelected?: boolean;
  onClick?: () => void;
  className?: string;
}

export const OptimizedListItem = memo(function OptimizedListItem({
  children,
  id,
  isSelected = false,
  onClick,
  className,
}: OptimizedListItemProps) {
  const handleClick = useCallback(() => {
    onClick?.();
  }, [onClick]);

  return (
    <div
      className={cn(
        'p-2 cursor-pointer transition-colors',
        isSelected ? 'bg-primary/10' : 'hover:bg-muted/50',
        className
      )}
      onClick={handleClick}
    >
      {children}
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function for better performance
  return (
    prevProps.id === nextProps.id &&
    prevProps.isSelected === nextProps.isSelected &&
    prevProps.className === nextProps.className
  );
});

// ============================================================================
// BATCH UPDATER
// ============================================================================

export function useBatchedUpdates<T>(initialValue: T, batchSize = 10) {
  const [value, setValue] = useState(initialValue);
  const [pendingUpdates, setPendingUpdates] = useState<Array<(prev: T) => T>>([]);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const batchUpdate = useCallback((updater: (prev: T) => T) => {
    setPendingUpdates(prev => [...prev, updater]);

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      setPendingUpdates(updates => {
        if (updates.length > 0) {
          setValue(currentValue => {
            return updates.reduce((acc, update) => update(acc), currentValue);
          });
          return [];
        }
        return updates;
      });
    }, 16); // Next frame
  }, []);

  const flushUpdates = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    setPendingUpdates(updates => {
      if (updates.length > 0) {
        setValue(currentValue => {
          return updates.reduce((acc, update) => update(acc), currentValue);
        });
        return [];
      }
      return updates;
    });
  }, []);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return { value, batchUpdate, flushUpdates, hasPendingUpdates: pendingUpdates.length > 0 };
}

// ============================================================================
// PERFORMANCE MONITOR
// ============================================================================

interface PerformanceMonitorProps {
  children: React.ReactNode;
  name: string;
  onMetrics?: (metrics: { renderTime: number; name: string }) => void;
}

export function PerformanceMonitor({ 
  children, 
  name, 
  onMetrics 
}: PerformanceMonitorProps) {
  const startTimeRef = useRef<number>();

  useEffect(() => {
    startTimeRef.current = performance.now();
  });

  useEffect(() => {
    if (startTimeRef.current) {
      const renderTime = performance.now() - startTimeRef.current;
      onMetrics?.({ renderTime, name });
      
      if (process.env.NODE_ENV === 'development' && renderTime > 16) {
        console.warn(`Slow render detected in ${name}: ${renderTime.toFixed(2)}ms`);
      }
    }
  });

  return <>{children}</>;
}

// ============================================================================
// THROTTLED SCROLL HANDLER
// ============================================================================

export function useThrottledScroll(
  callback: (scrollY: number) => void,
  delay = 16
) {
  const lastCallRef = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const throttledCallback = useCallback((scrollY: number) => {
    const now = Date.now();
    
    if (now - lastCallRef.current >= delay) {
      callback(scrollY);
      lastCallRef.current = now;
    } else {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        callback(scrollY);
        lastCallRef.current = Date.now();
      }, delay - (now - lastCallRef.current));
    }
  }, [callback, delay]);

  useEffect(() => {
    const handleScroll = () => {
      throttledCallback(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [throttledCallback]);

  return throttledCallback;
}
