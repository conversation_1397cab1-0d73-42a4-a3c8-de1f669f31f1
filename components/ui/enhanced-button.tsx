"use client";

import React, { forwardRef } from "react";
import { motion } from "framer-motion";
import { Button, ButtonProps } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";

interface EnhancedButtonProps extends ButtonProps {
  icon?: LucideIcon;
  iconPosition?: "left" | "right";
  loading?: boolean;
  loadingText?: string;
  animated?: boolean;
  gradient?: boolean;
}

const EnhancedButton = forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({ 
    className, 
    children, 
    icon: Icon, 
    iconPosition = "left",
    loading = false,
    loadingText = "Loading...",
    animated = true,
    gradient = false,
    disabled,
    ...props 
  }, ref) => {
    const isDisabled = disabled || loading;

    const buttonContent = (
      <>
        {Icon && iconPosition === "left" && !loading && (
          <Icon className="h-4 w-4" />
        )}
        
        {loading ? (
          <>
            <motion.div
              className="h-4 w-4 border-2 border-current border-t-transparent rounded-full"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
            {loadingText}
          </>
        ) : (
          children
        )}
        
        {Icon && iconPosition === "right" && !loading && (
          <motion.div
            animate={animated ? { x: [0, 4, 0] } : {}}
            transition={{ 
              duration: 2, 
              repeat: Infinity, 
              ease: "easeInOut",
              repeatDelay: 1 
            }}
          >
            <Icon className="h-4 w-4" />
          </motion.div>
        )}
      </>
    );

    const buttonElement = (
      <Button
        ref={ref}
        className={cn(
          "min-h-[48px] font-semibold transition-all duration-300 focus-ring",
          Icon && "flex items-center gap-2",
          gradient && "veil-gradient-primary text-white hover:opacity-90",
          className
        )}
        disabled={isDisabled}
        {...props}
      >
        {buttonContent}
      </Button>
    );

    if (!animated || isDisabled) {
      return buttonElement;
    }

    return (
      <motion.div
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        transition={{ type: "spring", stiffness: 400, damping: 17 }}
      >
        {buttonElement}
      </motion.div>
    );
  }
);

EnhancedButton.displayName = "EnhancedButton";

export { EnhancedButton };
