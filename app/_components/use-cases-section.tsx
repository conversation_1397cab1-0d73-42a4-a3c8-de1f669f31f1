"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Building2, 
  Heart, 
  GraduationCap,
  Users,
  Shield,
  AlertTriangle,
  MessageSquare,
  UserCheck,
  BookOpen,
  Megaphone,
  ArrowRight,
  CheckCircle
} from "lucide-react";

interface UseCase {
  icon: React.ElementType;
  title: string;
  description: string;
  applications: string[];
  benefits: string[];
  badge: string;
  color: string;
  bgGradient: string;
}

export const UseCasesSection: React.FC = () => {
  const useCases: UseCase[] = [
    {
      icon: Building2,
      title: "Corporate Feedback",
      description: "Enable honest workplace conversations without fear of retaliation or judgment.",
      applications: [
        "Anonymous employee surveys",
        "Whistleblowing protection",
        "Leadership feedback sessions",
        "Workplace culture discussions"
      ],
      benefits: [
        "Honest feedback without retaliation",
        "Improved workplace culture",
        "Better leadership insights",
        "Protected whistleblowing"
      ],
      badge: "Enterprise",
      color: "text-blue-600 dark:text-blue-400",
      bgGradient: "from-blue-500/10 to-cyan-500/10"
    },
    {
      icon: Heart,
      title: "Mental Health Support",
      description: "Create safe spaces for mental health discussions and peer support groups.",
      applications: [
        "Support group meetings",
        "Peer counseling sessions",
        "Crisis intervention",
        "Therapy group discussions"
      ],
      benefits: [
        "Stigma-free environment",
        "Peer support without judgment",
        "Crisis intervention safety",
        "Therapeutic anonymity"
      ],
      badge: "Healthcare",
      color: "text-red-600 dark:text-red-400",
      bgGradient: "from-red-500/10 to-pink-500/10"
    },
    {
      icon: GraduationCap,
      title: "Educational Discussions",
      description: "Foster open academic discussions and student feedback without social pressure.",
      applications: [
        "Student feedback sessions",
        "Academic discussions",
        "Campus issue reporting",
        "Peer learning groups"
      ],
      benefits: [
        "Honest student feedback",
        "Improved campus policies",
        "Enhanced learning environment",
        "Safe issue reporting"
      ],
      badge: "Education",
      color: "text-green-600 dark:text-green-400",
      bgGradient: "from-green-500/10 to-emerald-500/10"
    }
  ];

  const additionalUseCases = [
    {
      icon: Users,
      title: "Community Organizing",
      description: "Grassroots movements and community discussions"
    },
    {
      icon: UserCheck,
      title: "Research Studies",
      description: "Anonymous participant interviews and surveys"
    },
    {
      icon: Megaphone,
      title: "Public Forums",
      description: "Town halls and public policy discussions"
    },
    {
      icon: BookOpen,
      title: "Book Clubs",
      description: "Literature discussions without social bias"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <section id="use-cases" className="py-24 bg-background">
      <div className="container mx-auto px-4">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="max-w-6xl mx-auto"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
              Perfect for Every
              <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                {" "}Anonymous Conversation
              </span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              From corporate boardrooms to support groups, VeilMeet enables authentic 
              conversations across industries and communities.
            </p>
          </motion.div>

          {/* Main Use Cases - Less boxy design */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 lg:grid-cols-3 gap-12 mb-16"
          >
            {useCases.map((useCase, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="group relative"
                whileHover={{ y: -8 }}
              >
                <div className={`relative h-full p-8 rounded-3xl bg-gradient-to-br ${useCase.bgGradient} backdrop-blur-sm border border-muted/30 hover:border-primary/30 transition-all duration-500 hover:shadow-2xl hover:shadow-primary/10`}>
                  {/* Floating icon */}
                  <div className="absolute -top-6 left-8">
                    <div className="p-4 rounded-2xl bg-gradient-to-br from-background/90 to-background/70 border border-muted/30 shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                      <useCase.icon className={`h-8 w-8 ${useCase.color}`} />
                    </div>
                  </div>

                  <div className="pt-8">
                    <div className="flex justify-end mb-4">
                      <div className="inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary text-xs font-medium">
                        {useCase.badge}
                      </div>
                    </div>

                    <h3 className="text-2xl font-bold mb-4">{useCase.title}</h3>
                    <p className="text-muted-foreground leading-relaxed mb-6">
                      {useCase.description}
                    </p>

                    {/* Applications */}
                    <div className="mb-6">
                      <h4 className="font-semibold mb-3 flex items-center gap-2 text-sm">
                        <MessageSquare className="h-4 w-4" />
                        Common Applications
                      </h4>
                      <ul className="space-y-2">
                        {useCase.applications.map((app, appIndex) => (
                          <li key={appIndex} className="flex items-start gap-3 text-sm">
                            <div className="w-2 h-2 bg-gradient-to-r from-primary to-primary/70 rounded-full mt-2 flex-shrink-0" />
                            <span className="leading-relaxed">{app}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Benefits */}
                    <div className="mb-6">
                      <h4 className="font-semibold mb-3 flex items-center gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                        Key Benefits
                      </h4>
                      <ul className="space-y-2">
                        {useCase.benefits.map((benefit, benefitIndex) => (
                          <li key={benefitIndex} className="flex items-start gap-3 text-sm">
                            <CheckCircle className="h-3 w-3 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
                            <span className="leading-relaxed">{benefit}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="inline-flex items-center text-primary font-medium text-sm group-hover:gap-3 gap-2 transition-all duration-300 cursor-pointer">
                      Learn More
                      <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Additional Use Cases - Cleaner design */}
          <motion.div variants={itemVariants} className="mb-12">
            <h3 className="text-2xl font-bold text-center mb-8">
              And Many More Applications
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {additionalUseCases.map((useCase, index) => (
                <motion.div
                  key={index}
                  className="text-center group hover:scale-105 transition-transform duration-300"
                  whileHover={{ y: -4 }}
                >
                  <div className="p-4 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 w-fit mx-auto mb-4 group-hover:from-primary/20 group-hover:to-primary/10 transition-all duration-300 shadow-md group-hover:shadow-lg">
                    <useCase.icon className="h-6 w-6 text-primary" />
                  </div>
                  <h4 className="font-semibold mb-2">{useCase.title}</h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {useCase.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Security Assurance - More organic design */}
          <motion.div
            variants={itemVariants}
            className="text-center relative overflow-hidden"
          >
            {/* Background gradient */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-background/50 to-secondary/5 rounded-3xl" />

            <div className="relative p-12">
              <div className="flex items-center justify-center gap-3 mb-6">
                <div className="p-3 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10">
                  <Shield className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                  Trusted Across Industries
                </h3>
              </div>
              <p className="text-muted-foreground mb-8 max-w-3xl mx-auto text-lg leading-relaxed">
                Whether you're handling sensitive corporate feedback, facilitating mental health support,
                or enabling educational discussions, VeilMeet provides the security and anonymity
                required for your most important conversations.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 text-primary font-medium border border-primary/20">
                  HIPAA Ready
                </div>
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-secondary/10 to-secondary/5 text-secondary font-medium border border-secondary/20">
                  SOC 2 Certified
                </div>
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 text-primary font-medium border border-primary/20">
                  GDPR Compliant
                </div>
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-secondary/10 to-secondary/5 text-secondary font-medium border border-secondary/20">
                  Enterprise Security
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};
