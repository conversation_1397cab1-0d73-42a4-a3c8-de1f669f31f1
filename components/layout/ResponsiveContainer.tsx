// Responsive container components
// Production-ready responsive layout system with mobile-first approach

'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { useViewport, useResponsiveValue } from '@/hooks/useViewport';

// ============================================================================
// RESPONSIVE CONTAINER TYPES
// ============================================================================

interface ResponsiveContainerProps {
  children: React.ReactNode;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full' | 'none';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  as?: keyof JSX.IntrinsicElements;
}

interface GridProps {
  children: React.ReactNode;
  cols?: {
    base?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

interface FlexProps {
  children: React.ReactNode;
  direction?: {
    base?: 'row' | 'col';
    sm?: 'row' | 'col';
    md?: 'row' | 'col';
    lg?: 'row' | 'col';
    xl?: 'row' | 'col';
    '2xl'?: 'row' | 'col';
  };
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  wrap?: boolean;
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

// ============================================================================
// RESPONSIVE CONTAINER
// ============================================================================

export function ResponsiveContainer({
  children,
  maxWidth = 'lg',
  padding = 'md',
  className,
  as: Component = 'div',
}: ResponsiveContainerProps) {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-4xl',
    xl: 'max-w-6xl',
    '2xl': 'max-w-7xl',
    full: 'max-w-full',
    none: '',
  };

  const paddingClasses = {
    none: '',
    sm: 'px-2 sm:px-4',
    md: 'px-4 sm:px-6 lg:px-8',
    lg: 'px-6 sm:px-8 lg:px-12',
    xl: 'px-8 sm:px-12 lg:px-16',
  };

  return (
    <Component
      className={cn(
        'mx-auto w-full',
        maxWidthClasses[maxWidth],
        paddingClasses[padding],
        className
      )}
    >
      {children}
    </Component>
  );
}

// ============================================================================
// RESPONSIVE GRID
// ============================================================================

export function ResponsiveGrid({
  children,
  cols = { base: 1, md: 2, lg: 3 },
  gap = 'md',
  className,
}: GridProps) {
  const gridCols = useResponsiveValue({
    base: cols.base || 1,
    sm: cols.sm,
    md: cols.md,
    lg: cols.lg,
    xl: cols.xl,
    '2xl': cols['2xl'],
  });

  const gapClasses = {
    none: 'gap-0',
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8',
  };

  const colClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4',
    5: 'grid-cols-5',
    6: 'grid-cols-6',
    12: 'grid-cols-12',
  };

  return (
    <div
      className={cn(
        'grid',
        colClasses[gridCols as keyof typeof colClasses] || 'grid-cols-1',
        gapClasses[gap],
        className
      )}
    >
      {children}
    </div>
  );
}

// ============================================================================
// RESPONSIVE FLEX
// ============================================================================

export function ResponsiveFlex({
  children,
  direction = { base: 'col', md: 'row' },
  align = 'start',
  justify = 'start',
  wrap = false,
  gap = 'md',
  className,
}: FlexProps) {
  const flexDirection = useResponsiveValue(direction);

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch',
  };

  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly',
  };

  const gapClasses = {
    none: 'gap-0',
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8',
  };

  const directionClasses = {
    row: 'flex-row',
    col: 'flex-col',
  };

  return (
    <div
      className={cn(
        'flex',
        directionClasses[flexDirection],
        alignClasses[align],
        justifyClasses[justify],
        wrap && 'flex-wrap',
        gapClasses[gap],
        className
      )}
    >
      {children}
    </div>
  );
}

// ============================================================================
// RESPONSIVE STACK
// ============================================================================

interface StackProps {
  children: React.ReactNode;
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  align?: 'start' | 'center' | 'end' | 'stretch';
  className?: string;
}

export function Stack({
  children,
  spacing = 'md',
  align = 'stretch',
  className,
}: StackProps) {
  const spacingClasses = {
    none: 'space-y-0',
    sm: 'space-y-2',
    md: 'space-y-4',
    lg: 'space-y-6',
    xl: 'space-y-8',
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch',
  };

  return (
    <div
      className={cn(
        'flex flex-col',
        spacingClasses[spacing],
        alignClasses[align],
        className
      )}
    >
      {children}
    </div>
  );
}

// ============================================================================
// RESPONSIVE COLUMNS
// ============================================================================

interface ColumnsProps {
  children: React.ReactNode;
  count?: {
    base?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export function ResponsiveColumns({
  children,
  count = { base: 1, md: 2 },
  gap = 'md',
  className,
}: ColumnsProps) {
  const { isMobile, isTablet, isDesktop } = useViewport();

  const getColumnCount = () => {
    if (isDesktop && count.lg) return count.lg;
    if (isTablet && count.md) return count.md;
    if (count.sm) return count.sm;
    return count.base || 1;
  };

  const columnCount = getColumnCount();

  const gapClasses = {
    none: 'gap-0',
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8',
  };

  return (
    <div
      className={cn(
        'columns-1',
        columnCount === 2 && 'md:columns-2',
        columnCount === 3 && 'lg:columns-3',
        columnCount === 4 && 'xl:columns-4',
        gapClasses[gap],
        className
      )}
    >
      {children}
    </div>
  );
}

// ============================================================================
// RESPONSIVE SIDEBAR LAYOUT
// ============================================================================

interface SidebarLayoutProps {
  children: React.ReactNode;
  sidebar: React.ReactNode;
  sidebarPosition?: 'left' | 'right';
  sidebarWidth?: 'sm' | 'md' | 'lg';
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  className?: string;
}

export function ResponsiveSidebarLayout({
  children,
  sidebar,
  sidebarPosition = 'left',
  sidebarWidth = 'md',
  collapsible = true,
  defaultCollapsed = false,
  className,
}: SidebarLayoutProps) {
  const [isCollapsed, setIsCollapsed] = React.useState(defaultCollapsed);
  const { isMobile } = useViewport();

  const sidebarWidthClasses = {
    sm: 'w-64',
    md: 'w-80',
    lg: 'w-96',
  };

  const sidebarContent = (
    <aside
      className={cn(
        'flex-shrink-0 border-border bg-card/30',
        sidebarWidthClasses[sidebarWidth],
        sidebarPosition === 'left' ? 'border-r' : 'border-l',
        isMobile && 'hidden',
        isCollapsed && collapsible && 'hidden lg:flex'
      )}
    >
      {sidebar}
    </aside>
  );

  return (
    <div className={cn('flex h-full min-h-0', className)}>
      {sidebarPosition === 'left' && sidebarContent}
      
      <main className="flex-1 min-w-0 overflow-hidden">
        {children}
      </main>
      
      {sidebarPosition === 'right' && sidebarContent}
    </div>
  );
}

// ============================================================================
// RESPONSIVE MASONRY
// ============================================================================

interface MasonryProps {
  children: React.ReactNode;
  columns?: {
    base?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export function ResponsiveMasonry({
  children,
  columns = { base: 1, sm: 2, md: 3, lg: 4 },
  gap = 'md',
  className,
}: MasonryProps) {
  const columnCount = useResponsiveValue(columns);

  const gapClasses = {
    none: 'gap-0',
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8',
  };

  // Convert children to array and distribute across columns
  const childrenArray = React.Children.toArray(children);
  const columnsArray = Array.from({ length: columnCount }, () => [] as React.ReactNode[]);

  childrenArray.forEach((child, index) => {
    const columnIndex = index % columnCount;
    columnsArray[columnIndex].push(child);
  });

  return (
    <div
      className={cn(
        'flex',
        gapClasses[gap],
        className
      )}
    >
      {columnsArray.map((columnChildren, columnIndex) => (
        <div
          key={columnIndex}
          className={cn(
            'flex flex-col flex-1',
            gapClasses[gap]
          )}
        >
          {columnChildren}
        </div>
      ))}
    </div>
  );
}

// ============================================================================
// RESPONSIVE UTILITIES
// ============================================================================

interface ShowProps {
  children: React.ReactNode;
  above?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  below?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
}

export function Show({ children, above, below }: ShowProps) {
  const { width } = useViewport();
  
  const breakpoints = {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
    '2xl': 1536,
  };

  const shouldShow = () => {
    if (above && width < breakpoints[above]) return false;
    if (below && width >= breakpoints[below]) return false;
    return true;
  };

  return shouldShow() ? <>{children}</> : null;
}

export function Hide({ children, above, below }: ShowProps) {
  return (
    <Show above={above} below={below}>
      {null}
    </Show>
  );
}
