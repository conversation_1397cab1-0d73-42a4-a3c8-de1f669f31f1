"use node";

import { action } from "./_generated/server";
import { v } from "convex/values";
import { AccessToken } from "livekit-server-sdk";
import { api } from "./_generated/api";

// Environment variables for LiveKit
const LIVEKIT_API_KEY = process.env.LIVEKIT_API_KEY;
const LIVEKIT_API_SECRET = process.env.LIVEKIT_API_SECRET;
const LIVEKIT_WS_URL = process.env.LIVEKIT_WS_URL;

// Generate LiveKit JWT token (Node.js action)
export const generateLiveKitToken = action({
  args: {
    roomName: v.string(),
    identity: v.string(),
    permissions: v.object({
      canPublish: v.boolean(),
      canSubscribe: v.boolean(),
      canPublishData: v.boolean(),
    }),
  },
  handler: async (ctx, args) => {
    // Validate environment variables
    if (!LIVEKIT_API_KEY || !LIVEKIT_API_SECRET || !LIVEKIT_WS_URL) {
      throw new Error("LiveKit configuration missing. Please check your environment variables.");
    }

    try {
      // Create LiveKit access token
      const accessToken = new AccessToken(LIVEKIT_API_KEY, LIVEKIT_API_SECRET, {
        identity: args.identity,
        ttl: 3600, // 1 hour in seconds
      });

      // Add room permissions
      accessToken.addGrant({
        roomJoin: true,
        room: args.roomName,
        canPublish: args.permissions.canPublish,
        canSubscribe: args.permissions.canSubscribe,
        canPublishData: args.permissions.canPublishData,
      });

      // Generate the JWT token
      const livekitToken = await accessToken.toJwt();

      return {
        token: livekitToken,
        identity: args.identity,
        roomName: args.roomName,
        wsUrl: LIVEKIT_WS_URL,
        expiresAt: Date.now() + (60 * 60 * 1000), // 1 hour
      };
    } catch (error) {
      console.error("Failed to generate LiveKit token:", error);
      throw new Error("Failed to generate voice access token");
    }
  },
});

// Generate voice token for a participant (calls the Node.js action)
export const generateVoiceTokenForParticipant = action({
  args: {
    roomId: v.id("rooms"),
    participantSessionId: v.string(),
    permissions: v.optional(v.object({
      canPublish: v.optional(v.boolean()),
      canSubscribe: v.optional(v.boolean()),
      canPublishData: v.optional(v.boolean()),
      canModerate: v.optional(v.boolean()),
    })),
  },
  handler: async (ctx, args): Promise<{
    token: string;
    identity: string;
    roomName: string;
    wsUrl: string;
    expiresAt: number;
  }> => {
    // Get the room and voice room from the database
    const room = await ctx.runQuery(api.rooms.getRoomById, { roomId: args.roomId });
    if (!room) {
      throw new Error("Room not found");
    }

    const voiceRoomStatus = await ctx.runQuery(api.livekit.getVoiceRoomStatus, { roomId: args.roomId });
    if (!voiceRoomStatus?.voiceRoom) {
      throw new Error("Voice room not found or not active");
    }

    // Get participant info
    const participants = await ctx.runQuery(api.rooms.getRoomParticipants, { roomId: args.roomId });
    const participant = participants.find((p: any) => p.sessionId === args.participantSessionId);

    if (!participant) {
      throw new Error("Participant not found");
    }

    // Generate unique LiveKit identity using anonymous alias
    const livekitIdentity: string = `${participant.anonymousAlias}_${args.participantSessionId.slice(-8)}`;

    // Set default permissions
    const permissions = {
      canPublish: args.permissions?.canPublish ?? true,
      canSubscribe: args.permissions?.canSubscribe ?? true,
      canPublishData: args.permissions?.canPublishData ?? true,
    };

    // Generate the actual LiveKit token using the Node.js action
    const tokenInfo = await ctx.runAction(api.livekitActions.generateLiveKitToken, {
      roomName: voiceRoomStatus.voiceRoom.livekitRoomName,
      identity: livekitIdentity,
      permissions,
    });

    // Store token record in database
    const now = Date.now();
    const expiresAt = now + (60 * 60 * 1000); // 1 hour

    await ctx.runMutation(api.livekit.storeVoiceToken, {
      participantSessionId: args.participantSessionId,
      roomId: args.roomId,
      voiceRoomId: voiceRoomStatus.voiceRoom._id,
      livekitToken: tokenInfo.token,
      livekitIdentity,
      permissions: {
        canPublish: permissions.canPublish,
        canSubscribe: permissions.canSubscribe,
        canPublishData: permissions.canPublishData,
        canModerate: args.permissions?.canModerate ?? false,
      },
      issuedAt: now,
      expiresAt,
    });

    return tokenInfo;
  },
});
