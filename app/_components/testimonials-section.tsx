"use client";

import React from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  Quote,
  Building2,
  Heart,
  GraduationCap,
  Users,
  Star
} from "lucide-react";

interface Testimonial {
  quote: string;
  author: string;
  role: string;
  company: string;
  industry: string;
  icon: React.ElementType;
  rating: number;
  highlight: string;
  color: string;
}

export const TestimonialsSection: React.FC = () => {
  const testimonials: Testimonial[] = [
    {
      quote: "<PERSON><PERSON><PERSON><PERSON><PERSON> transformed our employee feedback process. For the first time, we're getting completely honest insights about workplace culture without fear of retaliation. The AI moderation keeps discussions productive while maintaining total anonymity.",
      author: "<PERSON>",
      role: "HR Director",
      company: "TechFlow Solutions",
      industry: "Corporate",
      icon: Building2,
      rating: 5,
      highlight: "Honest feedback without retaliation",
      color: "text-blue-600 dark:text-blue-400"
    },
    {
      quote: "As a therapist facilitating support groups, <PERSON><PERSON><PERSON><PERSON><PERSON> has been revolutionary. Participants feel safe to share their deepest struggles without judgment. The voice modulation and anonymous identities create a truly safe space for healing.",
      author: "Dr. <PERSON>",
      role: "Licensed Therapist",
      company: "Wellness Center",
      industry: "Healthcare",
      icon: Heart,
      rating: 5,
      highlight: "Safe space for healing",
      color: "text-red-600 dark:text-red-400"
    },
    {
      quote: "Our student feedback sessions have never been more authentic. Students finally feel comfortable discussing campus issues, mental health, and academic concerns. The AI keeps conversations respectful while preserving complete anonymity.",
      author: "Prof. Jennifer Walsh",
      role: "Dean of Students",
      company: "State University",
      industry: "Education",
      icon: GraduationCap,
      rating: 5,
      highlight: "Authentic student voice",
      color: "text-green-600 dark:text-green-400"
    },
    {
      quote: "VeilMeet enabled our grassroots organization to have sensitive discussions about community issues. The platform's security gave activists confidence to speak freely about systemic problems without fear of surveillance or retaliation.",
      author: "Alex Thompson",
      role: "Community Organizer",
      company: "Citizens United",
      industry: "Activism",
      icon: Users,
      rating: 5,
      highlight: "Fear-free communication",
      color: "text-purple-600 dark:text-purple-400"
    }
  ];

  const stats = [
    { value: "98%", label: "User Satisfaction", description: "Report improved conversation quality" },
    { value: "156%", label: "Engagement Increase", description: "More authentic participation" },
    { value: "10,000+", label: "Sessions Completed", description: "Across all industries" },
    { value: "99.2%", label: "Privacy Rating", description: "Complete anonymity maintained" }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <section id="testimonials" className="py-24 bg-muted/30">
      <div className="container mx-auto px-4">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="max-w-6xl mx-auto"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
              Trusted by
              <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                {" "}Industry Leaders
              </span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              From Fortune 500 companies to healthcare providers and educational institutions, 
              VeilMeet enables authentic conversations across every industry.
            </p>
          </motion.div>

          {/* Testimonials Grid - Less boxy design */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-16"
          >
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="group relative"
                whileHover={{ y: -6 }}
              >
                <div className="relative h-full p-8 rounded-3xl bg-gradient-to-br from-background/80 to-muted/20 backdrop-blur-sm border border-muted/30 hover:border-primary/30 transition-all duration-500 hover:shadow-2xl hover:shadow-primary/10">
                  {/* Quote Icon */}
                  <div className="absolute -top-4 -left-4">
                    <div className="p-3 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20 shadow-lg">
                      <Quote className="h-6 w-6 text-primary" />
                    </div>
                  </div>

                  <div className="pt-4">
                    {/* Testimonial Text */}
                    <blockquote className="text-lg leading-relaxed mb-6 text-foreground/90 italic">
                      "{testimonial.quote}"
                    </blockquote>

                    {/* Rating */}
                    <div className="flex items-center gap-1 mb-6">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>

                    {/* Author Info */}
                    <div className="flex items-center gap-4 mb-4">
                      <div className="h-12 w-12 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center">
                        <testimonial.icon className={`h-6 w-6 ${testimonial.color}`} />
                      </div>
                      <div>
                        <div className="font-bold">{testimonial.author}</div>
                        <div className="text-sm text-muted-foreground">
                          {testimonial.role} at {testimonial.company}
                        </div>
                      </div>
                    </div>

                    {/* Industry Badge and Highlight */}
                    <div className="flex items-center justify-between">
                      <div className="inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary text-xs font-medium">
                        {testimonial.industry}
                      </div>
                      <div className="text-sm font-bold text-primary">
                        {testimonial.highlight}
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Success Stats - Cleaner design */}
          <motion.div variants={itemVariants} className="mb-12">
            <h3 className="text-2xl font-bold text-center mb-8">
              Proven Results Across Industries
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  className="text-center group hover:scale-105 transition-transform duration-300"
                  whileHover={{ y: -4 }}
                >
                  <div className="p-6 rounded-2xl bg-gradient-to-br from-background/80 to-muted/20 backdrop-blur-sm border border-muted/30 hover:border-primary/30 transition-all duration-300 hover:shadow-lg">
                    <div className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform duration-300">
                      {stat.value}
                    </div>
                    <div className="font-bold mb-2">
                      {stat.label}
                    </div>
                    <div className="text-sm text-muted-foreground leading-relaxed">
                      {stat.description}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Common Themes - More organic design */}
          <motion.div
            variants={itemVariants}
            className="text-center relative overflow-hidden"
          >
            {/* Background gradient */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-background/50 to-secondary/5 rounded-3xl" />

            <div className="relative p-12">
              <h3 className="text-3xl font-bold mb-8 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                What Our Users Say Most
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <motion.div className="text-center group" whileHover={{ y: -4 }}>
                  <div className="p-4 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10 w-fit mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Quote className="h-6 w-6 text-primary" />
                  </div>
                  <h4 className="font-bold mb-2">Authentic Conversations</h4>
                  <p className="text-sm text-muted-foreground italic">
                    "Finally, people can speak their truth"
                  </p>
                </motion.div>
                <motion.div className="text-center group" whileHover={{ y: -4 }}>
                  <div className="p-4 rounded-2xl bg-gradient-to-br from-secondary/20 to-secondary/10 w-fit mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Building2 className="h-6 w-6 text-secondary" />
                  </div>
                  <h4 className="font-bold mb-2">Fear-Free Communication</h4>
                  <p className="text-sm text-muted-foreground italic">
                    "No more fear of retaliation or judgment"
                  </p>
                </motion.div>
                <motion.div className="text-center group" whileHover={{ y: -4 }}>
                  <div className="p-4 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10 w-fit mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Heart className="h-6 w-6 text-primary" />
                  </div>
                  <h4 className="font-bold mb-2">AI Moderation Excellence</h4>
                  <p className="text-sm text-muted-foreground italic">
                    "Keeps discussions productive and respectful"
                  </p>
                </motion.div>
                <motion.div className="text-center group" whileHover={{ y: -4 }}>
                  <div className="p-4 rounded-2xl bg-gradient-to-br from-secondary/20 to-secondary/10 w-fit mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <GraduationCap className="h-6 w-6 text-secondary" />
                  </div>
                  <h4 className="font-bold mb-2">Professional Use Success</h4>
                  <p className="text-sm text-muted-foreground italic">
                    "Perfect for sensitive workplace discussions"
                  </p>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};
