/**
 * VeilMind Spatial Audio Positioning System
 * 3D audio simulation with virtual room acoustics
 */

export interface SpatialPosition {
  x: number; // Left (-1) to Right (1)
  y: number; // Back (-1) to Front (1)
  z: number; // Down (-1) to Up (1)
}

export interface VirtualRoom {
  width: number;  // meters
  height: number; // meters
  depth: number;  // meters
  reverbLevel: number; // 0 to 1
  absorptionCoefficient: number; // 0 to 1
  roomType: 'small' | 'medium' | 'large' | 'hall' | 'outdoor';
}

export interface SpatialParticipant {
  id: string;
  position: SpatialPosition;
  audioElement: HTMLAudioElement;
  pannerNode: PannerNode;
  gainNode: GainNode;
  isLocalParticipant: boolean;
  volume: number; // 0 to 1
}

export interface SpatialAudioSettings {
  enabled: boolean;
  virtualRoom: VirtualRoom;
  listenerPosition: SpatialPosition;
  listenerOrientation: { forward: SpatialPosition; up: SpatialPosition };
  maxDistance: number; // Maximum audible distance
  rolloffFactor: number; // How quickly volume decreases with distance
  dopplerFactor: number; // Doppler effect strength
  enableReverb: boolean;
  enableOcclusion: boolean;
  spatialIntensity: number; // 0 to 1, overall spatial effect strength
}

export class SpatialAudioEngine {
  private audioContext: AudioContext | null = null;
  private participants: Map<string, SpatialParticipant> = new Map();
  private settings: SpatialAudioSettings;
  private convolver: ConvolverNode | null = null;
  private reverbGain: GainNode | null = null;
  private masterGain: GainNode | null = null;
  
  // Virtual room acoustics
  private impulseResponse: AudioBuffer | null = null;
  private roomResonances: OscillatorNode[] = [];
  
  // Auto-arrangement
  private autoArrangementEnabled = true;
  private arrangementPattern: 'circle' | 'grid' | 'line' | 'cluster' = 'circle';
  
  constructor(settings: Partial<SpatialAudioSettings> = {}) {
    this.settings = {
      enabled: true,
      virtualRoom: {
        width: 10,
        height: 3,
        depth: 10,
        reverbLevel: 0.3,
        absorptionCoefficient: 0.2,
        roomType: 'medium'
      },
      listenerPosition: { x: 0, y: 0, z: 0 },
      listenerOrientation: {
        forward: { x: 0, y: 0, z: -1 },
        up: { x: 0, y: 1, z: 0 }
      },
      maxDistance: 20,
      rolloffFactor: 1,
      dopplerFactor: 1,
      enableReverb: true,
      enableOcclusion: false,
      spatialIntensity: 1.0,
      ...settings
    };
  }
  
  async initialize(): Promise<void> {
    try {
      // Create audio context
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      // Create master gain node
      this.masterGain = this.audioContext.createGain();
      this.masterGain.connect(this.audioContext.destination);
      
      // Initialize reverb if enabled
      if (this.settings.enableReverb) {
        await this.initializeReverb();
      }
      
      // Set up listener
      this.updateListener();
      
      console.log('Spatial Audio Engine initialized');
    } catch (error) {
      console.error('Failed to initialize Spatial Audio Engine:', error);
      throw error;
    }
  }
  
  private async initializeReverb(): Promise<void> {
    if (!this.audioContext) return;
    
    try {
      // Create convolver for reverb
      this.convolver = this.audioContext.createConvolver();
      this.reverbGain = this.audioContext.createGain();
      
      // Generate impulse response for room acoustics
      this.impulseResponse = this.generateImpulseResponse();
      this.convolver.buffer = this.impulseResponse;
      
      // Connect reverb chain
      this.convolver.connect(this.reverbGain);
      this.reverbGain.connect(this.masterGain!);
      
      // Set reverb level
      this.reverbGain.gain.value = this.settings.virtualRoom.reverbLevel;
      
    } catch (error) {
      console.warn('Failed to initialize reverb:', error);
    }
  }
  
  private generateImpulseResponse(): AudioBuffer {
    if (!this.audioContext) throw new Error('Audio context not available');
    
    const room = this.settings.virtualRoom;
    const sampleRate = this.audioContext.sampleRate;
    
    // Calculate reverb time based on room size and absorption
    const volume = room.width * room.height * room.depth;
    const surfaceArea = 2 * (room.width * room.height + room.width * room.depth + room.height * room.depth);
    const rt60 = 0.161 * volume / (surfaceArea * room.absorptionCoefficient);
    
    // Generate impulse response
    const length = Math.floor(sampleRate * Math.min(rt60, 4)); // Max 4 seconds
    const buffer = this.audioContext.createBuffer(2, length, sampleRate);
    
    for (let channel = 0; channel < 2; channel++) {
      const data = buffer.getChannelData(channel);
      
      for (let i = 0; i < length; i++) {
        const t = i / sampleRate;
        const decay = Math.exp(-3 * t / rt60); // -60dB decay
        
        // Add early reflections
        let sample = 0;
        const numReflections = 8;
        
        for (let r = 0; r < numReflections; r++) {
          const delay = (r + 1) * 0.01; // 10ms intervals
          const delayIndex = Math.floor(delay * sampleRate);
          
          if (i >= delayIndex) {
            const reflectionDecay = Math.exp(-t / (rt60 * 0.3));
            const noise = (Math.random() - 0.5) * 2;
            sample += noise * reflectionDecay * 0.1;
          }
        }
        
        // Add diffuse reverb tail
        const diffuseNoise = (Math.random() - 0.5) * 2;
        sample += diffuseNoise * decay * 0.05;
        
        data[i] = sample;
      }
    }
    
    return buffer;
  }
  
  addParticipant(id: string, audioElement: HTMLAudioElement, isLocal = false): void {
    if (!this.audioContext || !this.settings.enabled) {
      return;
    }
    
    try {
      // Create audio nodes
      const source = this.audioContext.createMediaElementSource(audioElement);
      const pannerNode = this.audioContext.createPanner();
      const gainNode = this.audioContext.createGain();
      
      // Configure panner node
      pannerNode.panningModel = 'HRTF';
      pannerNode.distanceModel = 'inverse';
      pannerNode.refDistance = 1;
      pannerNode.maxDistance = this.settings.maxDistance;
      pannerNode.rolloffFactor = this.settings.rolloffFactor;
      pannerNode.coneInnerAngle = 360;
      pannerNode.coneOuterAngle = 0;
      pannerNode.coneOuterGain = 0;
      
      // Connect audio graph
      source.connect(gainNode);
      gainNode.connect(pannerNode);
      pannerNode.connect(this.masterGain!);
      
      // Add reverb send if enabled
      if (this.convolver && this.settings.enableReverb) {
        const reverbSend = this.audioContext.createGain();
        reverbSend.gain.value = 0.3; // 30% to reverb
        gainNode.connect(reverbSend);
        reverbSend.connect(this.convolver);
      }
      
      // Create participant
      const participant: SpatialParticipant = {
        id,
        position: { x: 0, y: 0, z: 0 },
        audioElement,
        pannerNode,
        gainNode,
        isLocalParticipant: isLocal,
        volume: 1.0
      };
      
      this.participants.set(id, participant);
      
      // Auto-arrange if enabled
      if (this.autoArrangementEnabled) {
        this.autoArrangeParticipants();
      }
      
      console.log(`Added spatial audio participant: ${id}`);
      
    } catch (error) {
      console.error(`Failed to add spatial audio participant ${id}:`, error);
    }
  }
  
  removeParticipant(id: string): void {
    const participant = this.participants.get(id);
    if (participant) {
      // Disconnect audio nodes
      participant.gainNode.disconnect();
      participant.pannerNode.disconnect();
      
      this.participants.delete(id);
      
      // Re-arrange remaining participants
      if (this.autoArrangementEnabled) {
        this.autoArrangeParticipants();
      }
      
      console.log(`Removed spatial audio participant: ${id}`);
    }
  }
  
  setParticipantPosition(id: string, position: SpatialPosition): void {
    const participant = this.participants.get(id);
    if (!participant || !this.settings.enabled) return;
    
    participant.position = { ...position };
    
    // Apply spatial intensity scaling
    const scaledPosition = {
      x: position.x * this.settings.spatialIntensity,
      y: position.y * this.settings.spatialIntensity,
      z: position.z * this.settings.spatialIntensity
    };
    
    // Update panner position
    participant.pannerNode.positionX.value = scaledPosition.x;
    participant.pannerNode.positionY.value = scaledPosition.y;
    participant.pannerNode.positionZ.value = scaledPosition.z;
    
    // Calculate distance-based volume
    const distance = this.calculateDistance(this.settings.listenerPosition, scaledPosition);
    const volumeMultiplier = this.calculateVolumeFromDistance(distance);
    participant.gainNode.gain.value = participant.volume * volumeMultiplier;
  }
  
  setListenerPosition(position: SpatialPosition): void {
    this.settings.listenerPosition = { ...position };
    this.updateListener();
    
    // Update all participant volumes based on new listener position
    this.participants.forEach((participant, id) => {
      this.setParticipantPosition(id, participant.position);
    });
  }
  
  setListenerOrientation(forward: SpatialPosition, up: SpatialPosition): void {
    this.settings.listenerOrientation = { forward: { ...forward }, up: { ...up } };
    this.updateListener();
  }
  
  private updateListener(): void {
    if (!this.audioContext || !this.audioContext.listener) return;
    
    const listener = this.audioContext.listener;
    const pos = this.settings.listenerPosition;
    const orient = this.settings.listenerOrientation;
    
    // Set listener position
    if (listener.positionX) {
      listener.positionX.value = pos.x;
      listener.positionY.value = pos.y;
      listener.positionZ.value = pos.z;
    }
    
    // Set listener orientation
    if (listener.forwardX) {
      listener.forwardX.value = orient.forward.x;
      listener.forwardY.value = orient.forward.y;
      listener.forwardZ.value = orient.forward.z;
      listener.upX.value = orient.up.x;
      listener.upY.value = orient.up.y;
      listener.upZ.value = orient.up.z;
    }
  }
  
  private calculateDistance(pos1: SpatialPosition, pos2: SpatialPosition): number {
    const dx = pos2.x - pos1.x;
    const dy = pos2.y - pos1.y;
    const dz = pos2.z - pos1.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  }
  
  private calculateVolumeFromDistance(distance: number): number {
    if (distance <= 1) return 1;
    if (distance >= this.settings.maxDistance) return 0;
    
    // Inverse distance law with rolloff factor
    return Math.pow(1 / distance, this.settings.rolloffFactor);
  }
  
  autoArrangeParticipants(): void {
    const participantIds = Array.from(this.participants.keys());
    const count = participantIds.length;
    
    if (count === 0) return;
    
    switch (this.arrangementPattern) {
      case 'circle':
        this.arrangeInCircle(participantIds);
        break;
      case 'grid':
        this.arrangeInGrid(participantIds);
        break;
      case 'line':
        this.arrangeInLine(participantIds);
        break;
      case 'cluster':
        this.arrangeInClusters(participantIds);
        break;
    }
  }
  
  private arrangeInCircle(participantIds: string[]): void {
    const radius = Math.min(3, Math.max(1, participantIds.length * 0.5));
    
    participantIds.forEach((id, index) => {
      const angle = (index / participantIds.length) * 2 * Math.PI;
      const position: SpatialPosition = {
        x: Math.cos(angle) * radius,
        y: 0,
        z: Math.sin(angle) * radius
      };
      this.setParticipantPosition(id, position);
    });
  }
  
  private arrangeInGrid(participantIds: string[]): void {
    const gridSize = Math.ceil(Math.sqrt(participantIds.length));
    const spacing = 2;
    
    participantIds.forEach((id, index) => {
      const row = Math.floor(index / gridSize);
      const col = index % gridSize;
      
      const position: SpatialPosition = {
        x: (col - gridSize / 2) * spacing,
        y: 0,
        z: (row - gridSize / 2) * spacing
      };
      this.setParticipantPosition(id, position);
    });
  }
  
  private arrangeInLine(participantIds: string[]): void {
    const spacing = 1.5;
    const totalWidth = (participantIds.length - 1) * spacing;
    
    participantIds.forEach((id, index) => {
      const position: SpatialPosition = {
        x: index * spacing - totalWidth / 2,
        y: 0,
        z: 2 // In front of listener
      };
      this.setParticipantPosition(id, position);
    });
  }
  
  private arrangeInClusters(participantIds: string[]): void {
    // Group participants into small clusters
    const clusterSize = 3;
    const numClusters = Math.ceil(participantIds.length / clusterSize);
    
    participantIds.forEach((id, index) => {
      const clusterIndex = Math.floor(index / clusterSize);
      const positionInCluster = index % clusterSize;
      
      // Position clusters in a circle
      const clusterAngle = (clusterIndex / numClusters) * 2 * Math.PI;
      const clusterRadius = 4;
      const clusterX = Math.cos(clusterAngle) * clusterRadius;
      const clusterZ = Math.sin(clusterAngle) * clusterRadius;
      
      // Position within cluster
      const localAngle = (positionInCluster / clusterSize) * 2 * Math.PI;
      const localRadius = 0.8;
      
      const position: SpatialPosition = {
        x: clusterX + Math.cos(localAngle) * localRadius,
        y: 0,
        z: clusterZ + Math.sin(localAngle) * localRadius
      };
      this.setParticipantPosition(id, position);
    });
  }
  
  // Public control methods
  setEnabled(enabled: boolean): void {
    this.settings.enabled = enabled;
    
    if (!enabled) {
      // Reset all participants to center position
      this.participants.forEach((participant, id) => {
        this.setParticipantPosition(id, { x: 0, y: 0, z: 0 });
      });
    } else {
      // Re-apply spatial positioning
      this.autoArrangeParticipants();
    }
  }
  
  setSpatialIntensity(intensity: number): void {
    this.settings.spatialIntensity = Math.max(0, Math.min(1, intensity));
    
    // Re-apply all positions with new intensity
    this.participants.forEach((participant, id) => {
      this.setParticipantPosition(id, participant.position);
    });
  }
  
  setArrangementPattern(pattern: 'circle' | 'grid' | 'line' | 'cluster'): void {
    this.arrangementPattern = pattern;
    if (this.autoArrangementEnabled) {
      this.autoArrangeParticipants();
    }
  }
  
  setAutoArrangement(enabled: boolean): void {
    this.autoArrangementEnabled = enabled;
    if (enabled) {
      this.autoArrangeParticipants();
    }
  }
  
  updateSettings(newSettings: Partial<SpatialAudioSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    
    // Re-initialize reverb if room settings changed
    if (newSettings.virtualRoom || newSettings.enableReverb !== undefined) {
      this.initializeReverb();
    }
    
    // Update listener
    this.updateListener();
    
    // Re-apply spatial positioning
    this.participants.forEach((participant, id) => {
      this.setParticipantPosition(id, participant.position);
    });
  }
  
  getSettings(): SpatialAudioSettings {
    return { ...this.settings };
  }
  
  getParticipants(): SpatialParticipant[] {
    return Array.from(this.participants.values());
  }
  
  // Cleanup
  dispose(): void {
    // Disconnect all participants
    this.participants.forEach((participant) => {
      participant.gainNode.disconnect();
      participant.pannerNode.disconnect();
    });
    
    this.participants.clear();
    
    // Disconnect reverb
    if (this.convolver) {
      this.convolver.disconnect();
      this.convolver = null;
    }
    
    if (this.reverbGain) {
      this.reverbGain.disconnect();
      this.reverbGain = null;
    }
    
    if (this.masterGain) {
      this.masterGain.disconnect();
      this.masterGain = null;
    }
    
    // Close audio context
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
      this.audioContext = null;
    }
    
    console.log('Spatial Audio Engine disposed');
  }
}
