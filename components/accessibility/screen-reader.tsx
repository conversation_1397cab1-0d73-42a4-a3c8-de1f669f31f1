"use client";

import React, { useEffect, useRef } from "react";

interface ScreenReaderAnnouncementProps {
  message: string;
  priority?: "polite" | "assertive";
  clearOnUnmount?: boolean;
}

export function ScreenReaderAnnouncement({
  message,
  priority = "polite",
  clearOnUnmount = true,
}: ScreenReaderAnnouncementProps) {
  const announcementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (announcementRef.current && message) {
      // Clear previous message
      announcementRef.current.textContent = "";
      
      // Set new message after a brief delay to ensure screen readers pick it up
      setTimeout(() => {
        if (announcementRef.current) {
          announcementRef.current.textContent = message;
        }
      }, 100);
    }

    return () => {
      if (clearOnUnmount && announcementRef.current) {
        announcementRef.current.textContent = "";
      }
    };
  }, [message, clearOnUnmount]);

  return (
    <div
      ref={announcementRef}
      aria-live={priority}
      aria-atomic="true"
      className="sr-only"
      role="status"
    />
  );
}

// Hook for programmatic announcements
export function useScreenReaderAnnouncement() {
  const announce = (message: string, priority: "polite" | "assertive" = "polite") => {
    const announcement = document.createElement("div");
    announcement.setAttribute("aria-live", priority);
    announcement.setAttribute("aria-atomic", "true");
    announcement.setAttribute("role", "status");
    announcement.className = "sr-only";
    
    document.body.appendChild(announcement);
    
    // Set message after a brief delay
    setTimeout(() => {
      announcement.textContent = message;
    }, 100);
    
    // Clean up after announcement
    setTimeout(() => {
      if (document.body.contains(announcement)) {
        document.body.removeChild(announcement);
      }
    }, 1000);
  };

  return { announce };
}

// Screen reader only text component
interface ScreenReaderOnlyProps {
  children: React.ReactNode;
  as?: keyof JSX.IntrinsicElements;
}

export function ScreenReaderOnly({ 
  children, 
  as: Component = "span" 
}: ScreenReaderOnlyProps) {
  return (
    <Component className="sr-only">
      {children}
    </Component>
  );
}
