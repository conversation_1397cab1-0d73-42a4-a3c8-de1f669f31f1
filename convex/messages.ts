import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Helper function to parse mentions from message content
const parseMentions = (content: string, roomParticipants: any[]) => {
  const mentions = [];
  const mentionRegex = /@(\w+)/g;
  let match;

  while ((match = mentionRegex.exec(content)) !== null) {
    const mentionText = match[0]; // @username
    const username = match[1]; // username
    const startIndex = match.index;
    const endIndex = match.index + mentionText.length;

    // Find participant by alias (case-insensitive)
    const mentionedParticipant = roomParticipants.find(p =>
      p.anonymousAlias.toLowerCase() === username.toLowerCase()
    );

    if (mentionedParticipant) {
      mentions.push({
        participantSessionId: mentionedParticipant.sessionId,
        anonymousAlias: mentionedParticipant.anonymousAlias,
        startIndex,
        endIndex,
        mentionText,
      });
    }
  }

  return mentions;
};

// Send a message with enhanced reply and mention support
export const sendMessage = mutation({
  args: {
    roomId: v.id("rooms"),
    participantSessionId: v.string(),
    content: v.string(),
    messageType: v.optional(v.union(
      v.literal("text"),
      v.literal("voice"),
      v.literal("system"),
      v.literal("ai_generated")
    )),
    replyToMessageId: v.optional(v.id("messages")),
  },
  handler: async (ctx, args) => {
    // Verify room exists and is active
    const room = await ctx.db.get(args.roomId);
    if (!room || room.status !== "active") {
      throw new Error("Room is not active");
    }

    // Verify participant exists and is active
    const participant = await ctx.db
      .query("participants")
      .withIndex("by_session", (q) => q.eq("sessionId", args.participantSessionId))
      .filter((q) => q.eq(q.field("roomId"), args.roomId))
      .first();

    if (!participant || !participant.isActive) {
      throw new Error("Participant not found or inactive");
    }

    if (participant.isBlocked || participant.isMuted) {
      throw new Error("Participant is blocked or muted");
    }

    // Get all room participants for mention parsing
    const roomParticipants = await ctx.db
      .query("participants")
      .withIndex("by_room_active", (q) => q.eq("roomId", args.roomId).eq("isActive", true))
      .collect();

    // Parse mentions from content
    const mentions = parseMentions(args.content, roomParticipants);

    // Handle reply logic
    let threadRootId = undefined;
    let threadDepth = 0;
    let replyToMessage = null;

    if (args.replyToMessageId) {
      replyToMessage = await ctx.db.get(args.replyToMessageId);
      if (!replyToMessage || replyToMessage.isDeleted) {
        throw new Error("Reply target message not found");
      }
      if (replyToMessage.roomId !== args.roomId) {
        throw new Error("Cannot reply to message from different room");
      }

      // Determine thread root and depth
      threadRootId = replyToMessage.threadRootId || args.replyToMessageId;
      threadDepth = (replyToMessage.threadDepth || 0) + 1;
    }

    // Create message
    const now = Date.now();
    const messageId = await ctx.db.insert("messages", {
      roomId: args.roomId,
      participantSessionId: args.participantSessionId,
      anonymousAlias: participant.anonymousAlias,
      content: args.content.trim(),
      messageType: args.messageType ?? "text",
      createdAt: now,
      editedAt: now,
      isEdited: false,
      isDeleted: false,
      isImportant: false,
      reactions: [],
      replyToMessageId: args.replyToMessageId,
      threadRootId,
      threadDepth: threadDepth,
      threadReplyCount: 0,
      threadParticipantCount: 0,
      threadLastReplyAt: undefined,
      mentions: mentions,
      hasMentions: mentions.length > 0,
      engagementMetrics: {
        totalReactions: 0,
        uniqueReactors: 0,
        replyCount: 0,
        mentionCount: mentions.length,
        viewCount: 0,
        lastEngagementAt: undefined,
      },
      attachments: [],
      sentiment: undefined,
      keyTopics: undefined,
      moderationScore: undefined,
      moderationReason: undefined,
      metadata: {},
    });

    // Update participant message count
    await ctx.db.patch(participant._id, {
      messageCount: participant.messageCount + 1,
      lastActiveAt: now,
    });

    // Update room total messages
    await ctx.db.patch(args.roomId, {
      totalMessages: (room.totalMessages ?? 0) + 1,
    });

    // Handle thread updates if this is a reply
    if (args.replyToMessageId && replyToMessage) {
      // Update reply count on the parent message
      await ctx.db.patch(args.replyToMessageId, {
        threadReplyCount: (replyToMessage.threadReplyCount || 0) + 1,
        threadLastReplyAt: now,
      });

      // Update thread root if it exists
      if (threadRootId && threadRootId !== args.replyToMessageId) {
        const rootMessage = await ctx.db.get(threadRootId);
        if (rootMessage) {
          await ctx.db.patch(threadRootId, {
            threadReplyCount: (rootMessage.threadReplyCount || 0) + 1,
            threadLastReplyAt: now,
          });
        }
      }

      // Update or create thread metadata
      const existingThread = await ctx.db
        .query("messageThreads")
        .withIndex("by_root_message", (q) => q.eq("rootMessageId", threadRootId!))
        .first();

      if (existingThread) {
        // Update existing thread
        const participantExists = existingThread.participants.some(p => p.sessionId === args.participantSessionId);
        const updatedParticipants = participantExists
          ? existingThread.participants.map(p =>
              p.sessionId === args.participantSessionId
                ? { ...p, messageCount: p.messageCount + 1, lastParticipatedAt: now }
                : p
            )
          : [...existingThread.participants, {
              sessionId: args.participantSessionId,
              anonymousAlias: participant.anonymousAlias,
              messageCount: 1,
              firstParticipatedAt: now,
              lastParticipatedAt: now,
            }];

        await ctx.db.patch(existingThread._id, {
          participantCount: updatedParticipants.length,
          messageCount: existingThread.messageCount + 1,
          lastActivityAt: now,
          participants: updatedParticipants,
          updatedAt: now,
        });
      } else {
        // Create new thread
        await ctx.db.insert("messageThreads", {
          rootMessageId: threadRootId!,
          roomId: args.roomId,
          participantCount: 1,
          messageCount: 1,
          lastActivityAt: now,
          participants: [{
            sessionId: args.participantSessionId,
            anonymousAlias: participant.anonymousAlias,
            messageCount: 1,
            firstParticipatedAt: now,
            lastParticipatedAt: now,
          }],
          engagementScore: 1,
          isActive: true,
          createdAt: now,
          updatedAt: now,
        });
      }
    }

    // Create mention entries
    for (const mention of mentions) {
      await ctx.db.insert("messageMentions", {
        messageId,
        roomId: args.roomId,
        mentionerSessionId: args.participantSessionId,
        mentionedSessionId: mention.participantSessionId,
        mentionedAlias: mention.anonymousAlias,
        mentionText: mention.mentionText,
        startIndex: mention.startIndex,
        endIndex: mention.endIndex,
        createdAt: now,
        isRead: false,
      });
    }

    return messageId;
  },
});

// Get room messages
export const getRoomMessages = query({
  args: { 
    roomId: v.id("rooms"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 50;
    
    const messages = await ctx.db
      .query("messages")
      .withIndex("by_room_time", (q) => q.eq("roomId", args.roomId))
      .filter((q) => q.eq(q.field("isDeleted"), false))
      .order("desc")
      .take(limit);

    // Return in chronological order (oldest first)
    return messages.reverse().map(msg => ({
      id: msg._id,
      roomId: msg.roomId,
      participantSessionId: msg.participantSessionId,
      anonymousAlias: msg.anonymousAlias,
      content: msg.content,
      messageType: msg.messageType,
      timestamp: msg.createdAt,
      isEdited: msg.isEdited,
      editedAt: msg.editedAt,
      reactions: msg.reactions,
      replyToMessageId: msg.replyToMessageId,
      threadRootId: msg.threadRootId,
      threadDepth: msg.threadDepth || 0,
      threadReplyCount: msg.threadReplyCount || 0,
      mentions: msg.mentions || [],
      hasMentions: msg.hasMentions || false,
      engagementMetrics: msg.engagementMetrics || {
        totalReactions: 0,
        uniqueReactors: 0,
        replyCount: 0,
        mentionCount: 0,
        viewCount: 0,
        lastEngagementAt: undefined,
      },
    }));
  },
});

// Subscribe to room messages (real-time)
export const subscribeToRoomMessages = query({
  args: { roomId: v.id("rooms") },
  handler: async (ctx, args) => {
    const messages = await ctx.db
      .query("messages")
      .withIndex("by_room_time", (q) => q.eq("roomId", args.roomId))
      .filter((q) => q.eq(q.field("isDeleted"), false))
      .order("desc")
      .take(100);

    return messages.reverse().map(msg => ({
      id: msg._id,
      roomId: msg.roomId,
      participantSessionId: msg.participantSessionId,
      anonymousAlias: msg.anonymousAlias,
      content: msg.content,
      messageType: msg.messageType,
      timestamp: msg.createdAt,
      isEdited: msg.isEdited,
      editedAt: msg.editedAt,
      reactions: msg.reactions,
      replyToMessageId: msg.replyToMessageId,
      threadRootId: msg.threadRootId,
      threadDepth: msg.threadDepth || 0,
      threadReplyCount: msg.threadReplyCount || 0,
      mentions: msg.mentions || [],
      hasMentions: msg.hasMentions || false,
      engagementMetrics: msg.engagementMetrics || {
        totalReactions: 0,
        uniqueReactors: 0,
        replyCount: 0,
        mentionCount: 0,
        viewCount: 0,
        lastEngagementAt: undefined,
      },
    }));
  },
});

// Enhanced reaction system with detailed tracking
export const addReaction = mutation({
  args: {
    messageId: v.id("messages"),
    participantSessionId: v.string(),
    emoji: v.string(),
  },
  handler: async (ctx, args) => {
    const message = await ctx.db.get(args.messageId);
    if (!message || message.isDeleted) {
      throw new Error("Message not found");
    }

    // Verify participant exists and is active
    const participant = await ctx.db
      .query("participants")
      .withIndex("by_session", (q) => q.eq("sessionId", args.participantSessionId))
      .filter((q) => q.eq(q.field("roomId"), message.roomId))
      .first();

    if (!participant || !participant.isActive) {
      throw new Error("Participant not found or inactive");
    }

    const now = Date.now();

    // Check if reaction already exists in messageReactions table
    const existingReaction = await ctx.db
      .query("messageReactions")
      .withIndex("by_message_emoji", (q) =>
        q.eq("messageId", args.messageId).eq("emoji", args.emoji)
      )
      .filter((q) => q.eq(q.field("participantSessionId"), args.participantSessionId))
      .first();

    if (existingReaction) {
      // Remove reaction
      await ctx.db.delete(existingReaction._id);
    } else {
      // Add new reaction
      await ctx.db.insert("messageReactions", {
        messageId: args.messageId,
        roomId: message.roomId,
        participantSessionId: args.participantSessionId,
        anonymousAlias: participant.anonymousAlias,
        emoji: args.emoji,
        createdAt: now,
        reactionContext: {
          deviceType: "desktop", // Could be passed from client
          reactionSpeed: 0, // Could calculate from message timestamp
        },
      });
    }

    // Update message reactions array for quick access
    const allReactions = await ctx.db
      .query("messageReactions")
      .withIndex("by_message", (q) => q.eq("messageId", args.messageId))
      .collect();

    // Group reactions by emoji
    const reactionGroups = allReactions.reduce((groups, reaction) => {
      if (!groups[reaction.emoji]) {
        groups[reaction.emoji] = {
          emoji: reaction.emoji,
          count: 0,
          participantSessionIds: [],
          firstReactedAt: reaction.createdAt,
          lastReactedAt: reaction.createdAt,
          participantDetails: [],
        };
      }

      groups[reaction.emoji].count++;
      groups[reaction.emoji].participantSessionIds.push(reaction.participantSessionId);
      groups[reaction.emoji].participantDetails.push({
        sessionId: reaction.participantSessionId,
        anonymousAlias: reaction.anonymousAlias,
        reactedAt: reaction.createdAt,
      });

      if (reaction.createdAt < groups[reaction.emoji].firstReactedAt) {
        groups[reaction.emoji].firstReactedAt = reaction.createdAt;
      }
      if (reaction.createdAt > groups[reaction.emoji].lastReactedAt) {
        groups[reaction.emoji].lastReactedAt = reaction.createdAt;
      }

      return groups;
    }, {} as Record<string, any>);

    const updatedReactions = Object.values(reactionGroups);

    // Update message with new reactions and engagement metrics
    const totalReactions = updatedReactions.reduce((sum, r) => sum + r.count, 0);
    const uniqueReactors = new Set(allReactions.map(r => r.participantSessionId)).size;

    await ctx.db.patch(args.messageId, {
      reactions: updatedReactions,
      engagementMetrics: {
        totalReactions,
        uniqueReactors,
        replyCount: message.engagementMetrics?.replyCount || 0,
        mentionCount: message.engagementMetrics?.mentionCount || 0,
        viewCount: message.engagementMetrics?.viewCount || 0,
        lastEngagementAt: now,
      },
    });

    return updatedReactions;
  },
});

// Delete message (soft delete)
export const deleteMessage = mutation({
  args: {
    messageId: v.id("messages"),
    participantSessionId: v.string(),
  },
  handler: async (ctx, args) => {
    const message = await ctx.db.get(args.messageId);
    if (!message || message.isDeleted) {
      throw new Error("Message not found");
    }

    // Only allow the message sender to delete their own message
    if (message.participantSessionId !== args.participantSessionId) {
      throw new Error("Not authorized to delete this message");
    }

    await ctx.db.patch(args.messageId, {
      isDeleted: true,
      content: "[Message deleted]",
    });
  },
});

// Edit message
export const editMessage = mutation({
  args: {
    messageId: v.id("messages"),
    participantSessionId: v.string(),
    newContent: v.string(),
  },
  handler: async (ctx, args) => {
    const message = await ctx.db.get(args.messageId);
    if (!message || message.isDeleted) {
      throw new Error("Message not found");
    }

    // Only allow the message sender to edit their own message
    if (message.participantSessionId !== args.participantSessionId) {
      throw new Error("Not authorized to edit this message");
    }

    // Don't allow editing messages older than 5 minutes
    const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
    if (message.createdAt < fiveMinutesAgo) {
      throw new Error("Message is too old to edit");
    }

    await ctx.db.patch(args.messageId, {
      content: args.newContent.trim(),
      isEdited: true,
      editedAt: Date.now(),
    });
  },
});

// Send system message
export const sendSystemMessage = mutation({
  args: {
    roomId: v.id("rooms"),
    content: v.string(),
    messageType: v.optional(v.union(v.literal("system"), v.literal("ai_generated"))),
  },
  handler: async (ctx, args) => {
    const room = await ctx.db.get(args.roomId);
    if (!room) {
      throw new Error("Room not found");
    }

    const now = Date.now();
    const messageId = await ctx.db.insert("messages", {
      roomId: args.roomId,
      participantSessionId: "system",
      anonymousAlias: "System",
      content: args.content,
      messageType: args.messageType ?? "system",
      createdAt: now,
      editedAt: now,
      isEdited: false,
      isDeleted: false,
      isImportant: false,
      reactions: [],
      replyToMessageId: undefined,
      threadRootId: undefined,
      threadDepth: 0,
      threadReplyCount: 0,
      threadParticipantCount: 0,
      threadLastReplyAt: undefined,
      mentions: [],
      hasMentions: false,
      engagementMetrics: {
        totalReactions: 0,
        uniqueReactors: 0,
        replyCount: 0,
        mentionCount: 0,
        viewCount: 0,
        lastEngagementAt: undefined,
      },
      attachments: [],
      sentiment: undefined,
      keyTopics: undefined,
      moderationScore: undefined,
      moderationReason: undefined,
      metadata: {},
    });

    return messageId;
  },
});

// Get thread messages
export const getThreadMessages = query({
  args: {
    threadRootId: v.id("messages"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 50;

    // Get root message
    const rootMessage = await ctx.db.get(args.threadRootId);
    if (!rootMessage || rootMessage.isDeleted) {
      throw new Error("Thread root message not found");
    }

    // Get all messages in the thread
    const threadMessages = await ctx.db
      .query("messages")
      .withIndex("by_thread_root", (q) => q.eq("threadRootId", args.threadRootId))
      .filter((q) => q.eq(q.field("isDeleted"), false))
      .order("asc")
      .take(limit);

    // Include root message and sort by creation time
    const allMessages = [rootMessage, ...threadMessages]
      .filter(msg => !msg.isDeleted)
      .sort((a, b) => a.createdAt - b.createdAt);

    return allMessages.map(msg => ({
      id: msg._id,
      roomId: msg.roomId,
      participantSessionId: msg.participantSessionId,
      anonymousAlias: msg.anonymousAlias,
      content: msg.content,
      messageType: msg.messageType,
      timestamp: msg.createdAt,
      isEdited: msg.isEdited,
      editedAt: msg.editedAt,
      reactions: msg.reactions,
      replyToMessageId: msg.replyToMessageId,
      threadRootId: msg.threadRootId,
      threadDepth: msg.threadDepth || 0,
      threadReplyCount: msg.threadReplyCount || 0,
      mentions: msg.mentions || [],
      hasMentions: msg.hasMentions || false,
      engagementMetrics: msg.engagementMetrics || {
        totalReactions: 0,
        uniqueReactors: 0,
        replyCount: 0,
        mentionCount: 0,
        viewCount: 0,
        lastEngagementAt: undefined,
      },
    }));
  },
});

// Get thread metadata
export const getThreadMetadata = query({
  args: {
    threadRootId: v.id("messages"),
  },
  handler: async (ctx, args) => {
    const thread = await ctx.db
      .query("messageThreads")
      .withIndex("by_root_message", (q) => q.eq("rootMessageId", args.threadRootId))
      .first();

    return thread;
  },
});

// Get participant mentions
export const getParticipantMentions = query({
  args: {
    participantSessionId: v.string(),
    roomId: v.optional(v.id("rooms")),
    unreadOnly: v.optional(v.boolean()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 50;

    let query = ctx.db
      .query("messageMentions")
      .withIndex("by_mentioned", (q) => q.eq("mentionedSessionId", args.participantSessionId));

    if (args.unreadOnly) {
      query = query.filter((q) => q.eq(q.field("isRead"), false));
    }

    if (args.roomId) {
      query = query.filter((q) => q.eq(q.field("roomId"), args.roomId));
    }

    const mentions = await query
      .order("desc")
      .take(limit);

    // Get associated messages
    const mentionData = await Promise.all(
      mentions.map(async (mention) => {
        const message = await ctx.db.get(mention.messageId);
        return {
          ...mention,
          message: message ? {
            id: message._id,
            content: message.content,
            timestamp: message.createdAt,
            anonymousAlias: message.anonymousAlias,
          } : null,
        };
      })
    );

    return mentionData.filter(m => m.message !== null);
  },
});

// Mark mentions as read
export const markMentionsAsRead = mutation({
  args: {
    participantSessionId: v.string(),
    mentionIds: v.optional(v.array(v.id("messageMentions"))),
    roomId: v.optional(v.id("rooms")),
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    if (args.mentionIds) {
      // Mark specific mentions as read
      for (const mentionId of args.mentionIds) {
        const mention = await ctx.db.get(mentionId);
        if (mention && mention.mentionedSessionId === args.participantSessionId) {
          await ctx.db.patch(mentionId, {
            isRead: true,
            readAt: now,
          });
        }
      }
    } else if (args.roomId) {
      // Mark all mentions in room as read
      if (args.roomId) {
        const mentions = await ctx.db
          .query("messageMentions")
          .withIndex("by_room_mentioned", (q) =>
            q.eq("roomId", args.roomId!).eq("mentionedSessionId", args.participantSessionId)
          )
          .filter((q) => q.eq(q.field("isRead"), false))
          .collect();

        for (const mention of mentions) {
          await ctx.db.patch(mention._id, {
            isRead: true,
            readAt: now,
          });
        }
      }
    }
  },
});
