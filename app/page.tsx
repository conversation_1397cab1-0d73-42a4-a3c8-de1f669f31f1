"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import {
  Shield,
  Brain,
  Users,
  ArrowRight,
  Zap,
  Eye,
  UserX,
  Lock,
  Sparkles,
  Clock,
  Menu,
  X,
  Github,
  Twitter,
  Linkedin,
  ExternalLink,
  Heart,
  Mail,
  CheckCircle,
  Trash2,
  Signal,
  Sun,
  Moon,
  LucideIcon
} from "lucide-react";

// Animation variants with reduced motion support
const prefersReducedMotion = () => {
  if (typeof window === "undefined") return false;
  return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
};

const createAnimationVariants = (reducedMotion = false) => ({
  fadeIn: {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: reducedMotion ? 0.1 : 0.6,
        ease: "easeOut",
      },
    },
  },
  fadeInUp: {
    hidden: { opacity: 0, y: reducedMotion ? 0 : 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: reducedMotion ? 0.1 : 0.6,
        ease: "easeOut",
      },
    },
  },
  staggerContainer: {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: reducedMotion ? 0 : 0.1,
        delayChildren: reducedMotion ? 0 : 0.1,
      },
    },
  },
  heroContainer: {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: reducedMotion ? 0 : 0.2,
        delayChildren: reducedMotion ? 0 : 0.1,
      },
    },
  },
  heroItem: {
    hidden: { opacity: 0, y: reducedMotion ? 0 : 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: reducedMotion ? 0.1 : 0.8,
        ease: "easeOut",
      },
    },
  },
});

const animationVariants = createAnimationVariants(prefersReducedMotion());
