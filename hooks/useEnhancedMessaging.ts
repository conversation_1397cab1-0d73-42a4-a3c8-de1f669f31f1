'use client';

import { useState, useCallback } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { useAnonymousIdentity } from './useAnonymousIdentity';
import { toast } from 'sonner';

export function useEnhancedMessaging(roomId?: Id<'rooms'>) {
  const { identity } = useAnonymousIdentity();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reply state
  const [replyingTo, setReplyingTo] = useState<{
    messageId: Id<'messages'>;
    content: string;
    author: string;
  } | null>(null);

  // Mutations
  const sendMessageMutation = useMutation(api.messages.sendMessage);
  const addReactionMutation = useMutation(api.messages.addReaction);
  const editMessageMutation = useMutation(api.messages.editMessage);
  const deleteMessageMutation = useMutation(api.messages.deleteMessage);
  const markMentionsAsReadMutation = useMutation(api.messages.markMentionsAsRead);

  // Queries
  const mentions = useQuery(
    api.messages.getParticipantMentions,
    roomId && identity ? {
      participantSessionId: identity.sessionId,
      roomId,
      unreadOnly: true,
    } : 'skip'
  );

  // Send a message with reply and mention support
  const sendMessage = useCallback(async (
    content: string,
    replyToMessageId?: Id<'messages'>
  ) => {
    if (!roomId || !identity || !content.trim()) {
      throw new Error('Missing required parameters');
    }

    try {
      setIsLoading(true);
      setError(null);

      const messageId = await sendMessageMutation({
        roomId,
        participantSessionId: identity.sessionId,
        content: content.trim(),
        messageType: 'text',
        replyToMessageId,
      });

      return messageId;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [roomId, identity, sendMessageMutation]);

  // Toggle reaction on a message
  const toggleReaction = useCallback(async (messageId: string, emoji: string) => {
    if (!identity) {
      throw new Error('No identity available');
    }

    try {
      setIsLoading(true);
      setError(null);

      await addReactionMutation({
        messageId: messageId as Id<'messages'>,
        participantSessionId: identity.sessionId,
        emoji,
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to react to message';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [identity, addReactionMutation]);

  // Edit a message
  const editMessage = useCallback(async (messageId: string, newContent: string) => {
    if (!identity || !newContent.trim()) {
      throw new Error('Missing required parameters');
    }

    try {
      setIsLoading(true);
      setError(null);

      await editMessageMutation({
        messageId: messageId as Id<'messages'>,
        participantSessionId: identity.sessionId,
        newContent: newContent.trim(),
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to edit message';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [identity, editMessageMutation]);

  // Delete a message
  const deleteMessage = useCallback(async (messageId: string) => {
    if (!identity) {
      throw new Error('No identity available');
    }

    try {
      setIsLoading(true);
      setError(null);

      await deleteMessageMutation({
        messageId: messageId as Id<'messages'>,
        participantSessionId: identity.sessionId,
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete message';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [identity, deleteMessageMutation]);

  // Start replying to a message
  const startReply = useCallback((messageId: Id<'messages'>, content: string, author: string) => {
    setReplyingTo({ messageId, content, author });
  }, []);

  // Cancel reply
  const cancelReply = useCallback(() => {
    setReplyingTo(null);
  }, []);

  // Mark mentions as read
  const markMentionsAsRead = useCallback(async (mentionIds?: string[]) => {
    if (!identity || !roomId) return;

    try {
      await markMentionsAsReadMutation({
        participantSessionId: identity.sessionId,
        mentionIds: mentionIds?.map(id => id as Id<'messageMentions'>),
        roomId,
      });
    } catch (err) {
      // Handle error silently
    }
  }, [identity, roomId, markMentionsAsReadMutation]);

  // Get unread mention count
  const unreadMentionCount = mentions?.length || 0;

  return {
    // Message operations
    sendMessage,
    toggleReaction,
    editMessage,
    deleteMessage,
    
    // Reply functionality
    replyingTo,
    startReply,
    cancelReply,
    
    // Mention functionality
    mentions,
    unreadMentionCount,
    markMentionsAsRead,
    
    // State
    isLoading,
    error,
  };
}

// Hook for thread management
export function useMessageThreads(threadRootId?: Id<'messages'>) {
  const threadMessages = useQuery(
    api.messages.getThreadMessages,
    threadRootId ? { threadRootId } : 'skip'
  );

  const threadMetadata = useQuery(
    api.messages.getThreadMetadata,
    threadRootId ? { threadRootId } : 'skip'
  );

  return {
    threadMessages,
    threadMetadata,
    isLoading: threadMessages === undefined || threadMetadata === undefined,
  };
}

// Hook for mention notifications
export function useMentionNotifications(participantSessionId?: string) {
  const [lastChecked, setLastChecked] = useState<number>(Date.now());

  const allMentions = useQuery(
    api.messages.getParticipantMentions,
    participantSessionId ? {
      participantSessionId,
      unreadOnly: false,
      limit: 100,
    } : 'skip'
  );

  const unreadMentions = allMentions?.filter(m => !m.isRead) || [];
  const newMentions = unreadMentions.filter(m => m.createdAt > lastChecked);

  const markAsChecked = useCallback(() => {
    setLastChecked(Date.now());
  }, []);

  return {
    allMentions,
    unreadMentions,
    newMentions,
    unreadCount: unreadMentions.length,
    newCount: newMentions.length,
    markAsChecked,
  };
}
