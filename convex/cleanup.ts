import { internalMutation, internalQuery } from "./_generated/server";
import { v } from "convex/values";

// Internal function to clean up expired rooms and data
export const cleanupExpiredRooms = internalMutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    
    // Find expired rooms
    const expiredRooms = await ctx.db
      .query("rooms")
      .withIndex("by_expires_at", (q) => q.lt("expiresAt", now))
      .filter((q) => q.neq(q.field("status"), "ended"))
      .collect();

    let cleanedRooms = 0;
    let cleanedParticipants = 0;
    let cleanedMessages = 0;

    for (const room of expiredRooms) {
      // Mark room as ended
      await ctx.db.patch(room._id, {
        status: "ended",
      });

      // Clean up participants
      const participants = await ctx.db
        .query("participants")
        .withIndex("by_room", (q) => q.eq("roomId", room._id))
        .collect();

      for (const participant of participants) {
        await ctx.db.patch(participant._id, {
          isActive: false,
          leftAt: now,
        });
      }

      // For ephemeral rooms, delete messages after 1 hour of expiry
      if (room.ephemeralMode && room.expiresAt < (now - 60 * 60 * 1000)) {
        const messages = await ctx.db
          .query("messages")
          .withIndex("by_room", (q) => q.eq("roomId", room._id))
          .collect();

        for (const message of messages) {
          await ctx.db.delete(message._id);
          cleanedMessages++;
        }
      }

      cleanedRooms++;
      cleanedParticipants += participants.length;
    }

    // Clean up inactive rooms (no participants for 5 minutes)
    const inactiveThreshold = now - (5 * 60 * 1000);
    const activeRooms = await ctx.db
      .query("rooms")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .collect();

    for (const room of activeRooms) {
      const activeParticipants = await ctx.db
        .query("participants")
        .withIndex("by_room_active", (q) => 
          q.eq("roomId", room._id).eq("isActive", true)
        )
        .collect();

      if (activeParticipants.length === 0) {
        // Check if any participant was active recently
        const recentParticipants = await ctx.db
          .query("participants")
          .withIndex("by_room", (q) => q.eq("roomId", room._id))
          .filter((q) => q.gt(q.field("lastActiveAt"), inactiveThreshold))
          .collect();

        if (recentParticipants.length === 0) {
          // Mark room as ended due to inactivity
          await ctx.db.patch(room._id, {
            status: "ended",
            expiresAt: now, // Expire immediately
          });
          cleanedRooms++;
        }
      }
    }

    return {
      cleanedRooms,
      cleanedParticipants,
      cleanedMessages,
      timestamp: now,
    };
  },
});

// Get room statistics
export const getRoomStatistics = internalQuery({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    
    // Count active rooms
    const activeRooms = await ctx.db
      .query("rooms")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .filter((q) => q.gt(q.field("expiresAt"), now))
      .collect();

    // Count total participants across all active rooms
    let totalActiveParticipants = 0;
    for (const room of activeRooms) {
      const participants = await ctx.db
        .query("participants")
        .withIndex("by_room_active", (q) => 
          q.eq("roomId", room._id).eq("isActive", true)
        )
        .collect();
      totalActiveParticipants += participants.length;
    }

    // Count messages sent in the last hour
    const oneHourAgo = now - (60 * 60 * 1000);
    const recentMessages = await ctx.db
      .query("messages")
      .filter((q) => q.gt(q.field("createdAt"), oneHourAgo))
      .collect();

    // Count total rooms created today
    const startOfDay = new Date();
    startOfDay.setHours(0, 0, 0, 0);
    const roomsCreatedToday = await ctx.db
      .query("rooms")
      .filter((q) => q.gt(q.field("createdAt"), startOfDay.getTime()))
      .collect();

    return {
      activeRooms: activeRooms.length,
      totalActiveParticipants,
      recentMessages: recentMessages.length,
      roomsCreatedToday: roomsCreatedToday.length,
      timestamp: now,
    };
  },
});

// Moderate content (placeholder for AI moderation)
export const moderateContent = internalMutation({
  args: {
    messageId: v.id("messages"),
    moderationAction: v.union(
      v.literal("approve"),
      v.literal("flag"),
      v.literal("delete"),
      v.literal("block_user")
    ),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Message not found");
    }

    switch (args.moderationAction) {
      case "delete":
        await ctx.db.patch(args.messageId, {
          isDeleted: true,
          content: "[Message removed by moderation]",
        });
        break;

      case "flag":
        await ctx.db.patch(args.messageId, {
          metadata: {
            ...(message.metadata || {}),
            flagged: true,
            flagReason: args.reason,
            flaggedAt: Date.now(),
          },
        });
        break;

      case "block_user":
        // Find and block the participant
        const participant = await ctx.db
          .query("participants")
          .withIndex("by_session", (q) => q.eq("sessionId", message.participantSessionId))
          .filter((q) => q.eq(q.field("roomId"), message.roomId))
          .first();

        if (participant) {
          await ctx.db.patch(participant._id, {
            isBlocked: true,
          });
        }

        // Also delete the message
        await ctx.db.patch(args.messageId, {
          isDeleted: true,
          content: "[Message removed by moderation]",
        });
        break;

      case "approve":
        await ctx.db.patch(args.messageId, {
          metadata: {
            ...(message.metadata || {}),
            approved: true,
            approvedAt: Date.now(),
          },
        });
        break;
    }

    return { success: true, action: args.moderationAction };
  },
});

// Archive old room data
export const archiveOldRooms = internalMutation({
  args: {
    olderThanDays: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const daysOld = args.olderThanDays ?? 7; // Default to 7 days
    const cutoffTime = Date.now() - (daysOld * 24 * 60 * 60 * 1000);

    // Find old ended rooms
    const oldRooms = await ctx.db
      .query("rooms")
      .withIndex("by_status", (q) => q.eq("status", "ended"))
      .filter((q) => q.lt(q.field("expiresAt"), cutoffTime))
      .collect();

    let archivedRooms = 0;
    let deletedMessages = 0;
    let deletedParticipants = 0;

    for (const room of oldRooms) {
      // Delete all messages for this room
      const messages = await ctx.db
        .query("messages")
        .withIndex("by_room", (q) => q.eq("roomId", room._id))
        .collect();

      for (const message of messages) {
        await ctx.db.delete(message._id);
        deletedMessages++;
      }

      // Delete all participants for this room
      const participants = await ctx.db
        .query("participants")
        .withIndex("by_room", (q) => q.eq("roomId", room._id))
        .collect();

      for (const participant of participants) {
        await ctx.db.delete(participant._id);
        deletedParticipants++;
      }

      // Delete the room itself
      await ctx.db.delete(room._id);
      archivedRooms++;
    }

    return {
      archivedRooms,
      deletedMessages,
      deletedParticipants,
      cutoffTime,
      timestamp: Date.now(),
    };
  },
});
