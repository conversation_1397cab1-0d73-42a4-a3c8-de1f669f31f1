"use client";

import { motion } from "motion/react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowR<PERSON>, 
  Sparkles, 
  Shield, 
  Zap,
  Users,
  Clock,
  CheckCircle
} from "lucide-react";

const benefits = [
  "Create room in under 10 seconds",
  "Complete anonymity guaranteed", 
  "AI-powered conversation enhancement",
  "No registration or personal info required",
  "Military-grade encryption",
  "Auto-delete after session ends"
];

export function CTA() {
  return (
    <section className="py-16 sm:py-20 lg:py-24 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      <div className="max-w-7xl mx-auto">
        {/* Main CTA Container */}
        <motion.div
          className="relative bg-gradient-to-br from-primary/20 via-secondary/10 to-accent/20 rounded-2xl sm:rounded-3xl p-4 sm:p-6 lg:p-12 xl:p-16 border border-primary/30"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          {/* Background effects */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent/5 rounded-2xl sm:rounded-3xl" />
          <div className="absolute top-0 left-1/4 w-64 sm:w-96 h-64 sm:h-96 bg-primary/10 rounded-full blur-3xl" />
          <div className="absolute bottom-0 right-1/4 w-48 sm:w-72 h-48 sm:h-72 bg-cyan-500/10 rounded-full blur-3xl" />

          <div className="relative z-10">
            {/* Header */}
            <motion.div
              className="text-center mb-8 sm:mb-12"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Badge variant="outline" className="mb-4 sm:mb-6 border-primary/30 text-primary bg-background/50 text-xs sm:text-sm">
                <Sparkles className="w-3 h-3 mr-1" />
                Ready to Start?
              </Badge>

              <h2 className="text-2xl sm:text-3xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-4 sm:mb-6 leading-tight px-2">
                <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent block">Your Voice Matters.</span>
                <span className="text-foreground block">Your Identity Doesn't.</span>
              </h2>

              <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed px-2">
                Join the revolution of honest communication. Create your first anonymous room
                and experience the freedom of speaking truth without consequences.
              </p>
            </motion.div>

            {/* Benefits Grid */}
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-8 sm:mb-12"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              {benefits.map((benefit, index) => (
                <motion.div
                  key={benefit}
                  className="flex items-center gap-2 sm:gap-3 bg-background/30 backdrop-blur-sm rounded-lg sm:rounded-xl p-3 sm:p-4 border border-border/50"
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 flex-shrink-0" />
                  <span className="text-xs sm:text-sm font-medium">{benefit}</span>
                </motion.div>
              ))}
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center mb-8 sm:mb-12 px-2"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              viewport={{ once: true }}
            >
              <Button
                size="lg"
                className="w-full sm:w-auto bg-primary hover:bg-primary/90 text-primary-foreground px-6 sm:px-8 lg:px-12 py-4 sm:py-6 lg:py-8 text-base sm:text-lg lg:text-xl font-bold transition-all duration-300 group shadow-2xl hover:shadow-3xl min-h-[48px]"
                onClick={() => window.location.href = '/create-room'}
              >
                <Zap className="mr-2 sm:mr-3 h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6" />
                <span className="whitespace-nowrap">Create Anonymous Room Now</span>
                <ArrowRight className="ml-2 sm:ml-3 h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 group-hover:translate-x-1 transition-transform" />
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="w-full sm:w-auto border-primary/30 text-primary hover:bg-primary/10 px-6 sm:px-8 lg:px-12 py-4 sm:py-6 lg:py-8 text-base sm:text-lg lg:text-xl backdrop-blur-sm min-h-[48px]"
                onClick={() => {
                  const heroSection = document.querySelector('section');
                  if (heroSection) {
                    heroSection.scrollIntoView({ behavior: 'smooth' });
                    // Focus on the room code input after scrolling
                    setTimeout(() => {
                      const input = document.querySelector('input[placeholder*="room code"]') as HTMLInputElement;
                      if (input) input.focus();
                    }, 500);
                  }
                }}
              >
                <Users className="mr-2 sm:mr-3 h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6" />
                <span className="whitespace-nowrap">Join Existing Room</span>
              </Button>
            </motion.div>

            {/* Trust indicators */}
            <motion.div
              className="flex flex-col sm:flex-row flex-wrap justify-center items-center gap-4 sm:gap-6 lg:gap-8 text-muted-foreground px-4"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 1 }}
              viewport={{ once: true }}
            >
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 flex-shrink-0" />
                <span className="font-medium text-sm sm:text-base whitespace-nowrap">100% Anonymous</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500 flex-shrink-0" />
                <span className="font-medium text-sm sm:text-base whitespace-nowrap">Ready in 10 Seconds</span>
              </div>
              <div className="flex items-center gap-2">
                <Sparkles className="w-4 h-4 sm:w-5 sm:h-5 text-primary flex-shrink-0" />
                <span className="font-medium text-sm sm:text-base whitespace-nowrap">AI-Enhanced</span>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Secondary CTA */}
        <motion.div
          className="text-center mt-12 sm:mt-16 px-4"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="bg-card/30 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 lg:p-8 border border-border/50 max-w-2xl mx-auto">
            <h3 className="text-lg sm:text-xl lg:text-2xl font-semibold mb-4">
              Not Ready to Create a Room?
            </h3>
            <p className="text-sm sm:text-base text-muted-foreground mb-6 leading-relaxed">
              Learn more about how VeilMeet protects your privacy and enhances conversations
              with cutting-edge AI technology.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
              <Button
                variant="outline"
                className="w-full sm:w-auto border-chart-2/30 text-chart-2 hover:bg-chart-2/10 text-sm sm:text-base min-h-[44px]"
              >
                Watch Demo Video
              </Button>
              <Button
                variant="outline"
                className="w-full sm:w-auto border-chart-3/30 text-chart-3 hover:bg-chart-3/10 text-sm sm:text-base min-h-[44px]"
              >
                Read Privacy Guide
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Final message */}
        <motion.div
          className="text-center mt-8 sm:mt-12 px-4"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <p className="text-base sm:text-lg text-muted-foreground italic leading-relaxed">
            "The most important conversations happen when people feel truly safe to speak."
          </p>
        </motion.div>
      </div>

      {/* Background decoration */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-1/4 left-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-0 w-72 h-72 bg-accent/5 rounded-full blur-3xl" />
      </div>
    </section>
  );
}
