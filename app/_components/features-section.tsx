"use client";

import React from "react";
import { motion } from "framer-motion";
import { SectionHeader } from "@/components/ui/section-header";
import { FeatureCard } from "@/components/ui/feature-card";
import { Badge } from "@/components/ui/badge";
import { cardVariants } from "@/lib/animation-variants";
import { Sparkles } from "lucide-react";
import { mainFeatures, additionalFeatures, trustBadges } from "@/lib/data/features";

export const FeaturesSection: React.FC = () => {

  return (
    <section id="features" className="section-padding bg-background" aria-labelledby="features-heading">
      <div className="container mx-auto container-padding">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <SectionHeader
            badgeText="Core Features"
            badgeIcon={Sparkles}
            title="Anonymous Conversations"
            highlightedTitle="Powerful Features for"
            description="Advanced technology meets simple design. Everything you need for secure, anonymous conversations without the complexity."
            size="lg"
          />

          {/* Main Features */}
          <motion.div
            variants={cardVariants.container}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 lg:grid-cols-3 gap-12 mb-16"
          >
            {mainFeatures.map((feature, index) => (
              <motion.div key={index} variants={cardVariants.item}>
                <FeatureCard
                  icon={feature.icon}
                  title={feature.title}
                  description={feature.description}
                  details={feature.details}
                  badge={feature.badge}
                  variant="floating"
                  iconColor={feature.color}
                />
              </motion.div>
            ))}
          </motion.div>

          {/* Additional Features Grid */}
          <motion.div
            variants={cardVariants.container}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="mb-12"
          >
            <h3 className="text-2xl font-bold text-center mb-8">
              Additional Security & Convenience Features
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              {additionalFeatures.map((feature, index) => (
                <motion.div key={index} variants={cardVariants.item}>
                  <FeatureCard
                    icon={feature.icon}
                    title={feature.title}
                    description={feature.description}
                    variant="minimal"
                  />
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Feature Highlight */}
          <motion.div
            variants={cardVariants.container}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="text-center relative overflow-hidden"
          >
            {/* Background gradient */}
            <div className="absolute inset-0 veil-glass rounded-3xl" />

            <div className="relative max-w-3xl mx-auto p-12">
              <h3 className="text-3xl font-bold mb-6 veil-gradient-text">
                Why Choose VeilMeet?
              </h3>
              <p className="text-muted-foreground mb-8 text-lg leading-relaxed">
                Unlike other platforms that collect your data or require complex setup,
                VeilMeet prioritizes your privacy while providing enterprise-grade features.
                Our AI doesn't just moderate—it enhances conversations while keeping you completely anonymous.
              </p>
              <div className="flex flex-wrap justify-center gap-3">
                {trustBadges.map((badge, index) => (
                  <motion.div
                    key={badge}
                    variants={cardVariants.item}
                    className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary font-medium text-sm border border-primary/20 hover:bg-primary/20 transition-colors"
                  >
                    {badge}
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};
