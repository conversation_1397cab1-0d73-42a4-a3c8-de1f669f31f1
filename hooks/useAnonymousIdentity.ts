'use client';

import { useState, useEffect, useCallback } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { 
  AnonymousIdentity, 
  storeIdentity, 
  getStoredIdentity, 
  clearStoredIdentity,
  generateSessionId 
} from '@/lib/identity';

export function useAnonymousIdentity() {
  const [identity, setIdentity] = useState<AnonymousIdentity | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const createIdentityMutation = useMutation(api.identity.createAnonymousIdentity);
  const getIdentityQuery = useQuery(
    api.identity.getIdentityBySession,
    identity ? { sessionId: identity.sessionId } : 'skip'
  );

  // Initialize identity on mount
  useEffect(() => {
    const initializeIdentity = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Try to get stored identity first
        const storedIdentity = getStoredIdentity();
        if (storedIdentity) {
          setIdentity(storedIdentity);
          setIsLoading(false);
          return;
        }

        // Create new identity if none exists
        await createNewIdentity();
      } catch (err) {
        console.error('Failed to initialize identity:', err);
        setError('Failed to create anonymous identity');
      } finally {
        setIsLoading(false);
      }
    };

    initializeIdentity();
  }, []);

  // Create a new anonymous identity
  const createNewIdentity = useCallback(async (roomCode?: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await createIdentityMutation({ roomCode });
      
      const newIdentity: AnonymousIdentity = {
        sessionId: result.sessionId,
        anonymousAlias: result.anonymousAlias,
        avatarSeed: result.avatarSeed,
        colorTheme: result.colorTheme,
        createdAt: Date.now(),
      };

      setIdentity(newIdentity);
      storeIdentity(newIdentity);
      
      return newIdentity;
    } catch (err) {
      console.error('Failed to create identity:', err);
      setError('Failed to create anonymous identity');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [createIdentityMutation]);

  // Refresh identity from server
  const refreshIdentity = useCallback(async () => {
    if (!identity) return;

    try {
      setError(null);
      // The query will automatically update when the server data changes
      // due to Convex's real-time nature
    } catch (err) {
      console.error('Failed to refresh identity:', err);
      setError('Failed to refresh identity');
    }
  }, [identity]);

  // Clear current identity and create a new one
  const resetIdentity = useCallback(async (roomCode?: string) => {
    try {
      clearStoredIdentity();
      setIdentity(null);
      await createNewIdentity(roomCode);
    } catch (err) {
      console.error('Failed to reset identity:', err);
      setError('Failed to reset identity');
      throw err;
    }
  }, [createNewIdentity]);

  // Validate current identity
  const validateIdentity = useCallback(() => {
    if (!identity) return false;

    // Check if identity is expired (24 hours)
    const isExpired = Date.now() - identity.createdAt > (24 * 60 * 60 * 1000);
    if (isExpired) {
      clearStoredIdentity();
      setIdentity(null);
      return false;
    }

    return true;
  }, [identity]);

  return {
    identity,
    isLoading,
    error,
    createNewIdentity,
    refreshIdentity,
    resetIdentity,
    validateIdentity,
    hasIdentity: !!identity,
  };
}

// Hook for managing room participation
export function useRoomParticipation(roomId?: string) {
  const { identity } = useAnonymousIdentity();
  const [isJoining, setIsJoining] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const joinRoomMutation = useMutation(api.rooms.joinRoom);
  const leaveRoomMutation = useMutation(api.rooms.leaveRoom);
  const updateActivityMutation = useMutation(api.identity.updateParticipantActivity);

  // Validate session access
  const sessionValidation = useQuery(
    api.identity.validateSessionAccess,
    roomId && identity ? { roomId, sessionId: identity.sessionId } : 'skip'
  );

  // Join a room
  const joinRoom = useCallback(async (roomCode: string) => {
    if (!identity) {
      throw new Error('No identity available');
    }

    try {
      setIsJoining(true);
      setError(null);

      const result = await joinRoomMutation({
        roomCode,
        sessionId: identity.sessionId,
        anonymousAlias: identity.anonymousAlias,
        avatarSeed: identity.avatarSeed,
      });

      return result;
    } catch (err) {
      console.error('Failed to join room:', err);
      setError(err instanceof Error ? err.message : 'Failed to join room');
      throw err;
    } finally {
      setIsJoining(false);
    }
  }, [identity, joinRoomMutation]);

  // Leave a room
  const leaveRoom = useCallback(async (roomId: string) => {
    if (!identity) return;

    try {
      setIsLeaving(true);
      setError(null);

      await leaveRoomMutation({
        roomId,
        sessionId: identity.sessionId,
      });
    } catch (err) {
      console.error('Failed to leave room:', err);
      setError('Failed to leave room');
      throw err;
    } finally {
      setIsLeaving(false);
    }
  }, [identity, leaveRoomMutation]);

  // Update participant activity (heartbeat)
  const updateActivity = useCallback(async (roomId: string, action?: string) => {
    if (!identity) return;

    try {
      await updateActivityMutation({
        roomId,
        sessionId: identity.sessionId,
        currentAction: action,
      });
    } catch (err) {
      console.error('Failed to update activity:', err);
      // Don't throw here as this is a background operation
    }
  }, [identity, updateActivityMutation]);

  return {
    joinRoom,
    leaveRoom,
    updateActivity,
    isJoining,
    isLeaving,
    error,
    hasAccess: sessionValidation?.hasAccess ?? false,
    accessReason: sessionValidation?.reason,
    participant: sessionValidation?.participant,
    room: sessionValidation?.room,
  };
}

// Hook for typing indicators
export function useTypingIndicator(roomId?: string) {
  const { identity } = useAnonymousIdentity();
  const { updateActivity } = useRoomParticipation(roomId);
  const [isTyping, setIsTyping] = useState(false);

  const typingIndicators = useQuery(
    api.identity.getTypingIndicators,
    roomId ? { roomId } : 'skip'
  );

  // Start typing
  const startTyping = useCallback(async () => {
    if (!roomId || !identity || isTyping) return;

    setIsTyping(true);
    await updateActivity(roomId, 'typing');
  }, [roomId, identity, isTyping, updateActivity]);

  // Stop typing
  const stopTyping = useCallback(async () => {
    if (!roomId || !identity || !isTyping) return;

    setIsTyping(false);
    await updateActivity(roomId, 'idle');
  }, [roomId, identity, isTyping, updateActivity]);

  // Get other participants who are typing (excluding current user)
  const otherTypingParticipants = typingIndicators?.filter(
    p => p.sessionId !== identity?.sessionId
  ) ?? [];

  return {
    startTyping,
    stopTyping,
    isTyping,
    otherTypingParticipants,
    typingCount: otherTypingParticipants.length,
  };
}
