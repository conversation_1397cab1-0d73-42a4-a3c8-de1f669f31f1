'use client';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Users,
  Share2,
  LogOut,
  MoreVertical,
  Copy,
  Settings,
  AlertTriangle,
  Loader2,
  Clock,
  Shield,
  Crown,
  Hash,
  Wifi,
  Reply
} from 'lucide-react';
import { formatRelativeTime } from '@/lib/identity';
import { cn } from '@/lib/utils';
import { useViewport } from '@/hooks/useViewport';

interface RoomHeaderProps {
  room: {
    _id: string;
    roomCode: string;
    topic?: string;
    status: string;
    createdAt: number;
    currentParticipants: number;
    maxParticipants: number;
  };
  participantCount: number;
  onShare: () => void;
  onLeave: () => void;
  onEnd?: () => void;
  isCreator?: boolean;
  isLoading?: boolean;
  mentionNotifications?: React.ReactNode;
  replyingTo?: {
    messageId: string;
    content: string;
    author: string;
  } | null;
}

export function RoomHeader({
  room,
  participantCount,
  onShare,
  onLeave,
  onEnd,
  isCreator = false,
  isLoading = false,
  mentionNotifications,
  replyingTo
}: RoomHeaderProps) {
  const { isMobile, isTablet } = useViewport();
  const isCompact = isMobile || isTablet;

  return (
    <header className="border-b border-border bg-card/50 backdrop-blur-sm sticky top-0 z-40">
      <div className={cn(
        "flex items-center justify-between",
        isCompact ? "px-3 py-2" : "px-6 py-4"
      )}>
        {/* Left: App Branding & Room Info */}
        <div className="flex items-center min-w-0 flex-1 gap-4">
          {/* VeilMeet Logo & Brand */}
          <div className={cn(
            "flex items-center flex-shrink-0",
            isCompact ? "gap-2.5" : "gap-3"
          )}>
            <div className={cn(
              "bg-gradient-to-br from-purple-500 via-purple-600 to-blue-600 rounded-xl flex items-center justify-center shadow-lg",
              isCompact ? "w-8 h-8" : "w-10 h-10"
            )}>
              <Shield className={cn(
                "text-white drop-shadow-sm",
                isCompact ? "h-5 w-5" : "h-6 w-6"
              )} />
            </div>
            <div className="flex flex-col">
              <span className={cn(
                "font-bold text-foreground bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent",
                isCompact ? "text-sm" : "text-base"
              )}>
                VeilMeet
              </span>
              {!isCompact && (
                <span className="text-xs text-muted-foreground font-medium">
                  Anonymous Room
                </span>
              )}
            </div>
          </div>

          {/* Vertical Separator */}
          <div className={cn(
            "w-px bg-border flex-shrink-0",
            isCompact ? "h-8" : "h-10"
          )} />

          {/* Room Information */}
          <div className="flex items-center min-w-0 flex-1 gap-3">

            <div className="min-w-0 flex-1">
              {/* Room Title & Badges */}
              <div className={cn(
                "flex items-center gap-2",
                isCompact ? "mb-0" : "mb-1"
              )}>
                <h1 className={cn(
                  "font-semibold text-foreground truncate",
                  isCompact ? "text-sm" : "text-base"
                )}>
                  {room.topic || 'Anonymous Discussion'}
                </h1>

                {/* Status Badge */}
                {!isCompact && (
                  <Badge
                    variant={room.status === 'active' ? 'default' : 'secondary'}
                    className="flex items-center gap-1.5 text-xs"
                  >
                    <Wifi className="h-3 w-3" />
                    {room.status === 'active' ? 'Live' : 'Offline'}
                  </Badge>
                )}

                {/* Creator Badge */}
                {isCreator && !isCompact && (
                  <Badge variant="outline" className="flex items-center gap-1 text-xs">
                    <Crown className="h-3 w-3" />
                    Host
                  </Badge>
                )}

                {/* Reply indicator - Always show but compact on mobile */}
                {replyingTo && (
                  <Badge variant="secondary" className={cn(
                    "flex items-center gap-1",
                    isCompact ? "text-xs px-2 py-0.5" : "text-xs"
                  )}>
                    <Reply className="h-3 w-3" />
                    {isCompact ? replyingTo.author : `Replying to ${replyingTo.author}`}
                  </Badge>
                )}
              </div>

              {/* Room Details - More compact on mobile */}
              {!isCompact ? (
                <div className="flex items-center gap-3 text-sm text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Hash className="h-3.5 w-3.5" />
                    <span className="font-mono">{room.roomCode}</span>
                  </span>
                  <span className="flex items-center gap-1">
                    <Users className="h-3.5 w-3.5" />
                    {participantCount}/{room.maxParticipants}
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="h-3.5 w-3.5" />
                    {formatRelativeTime(room.createdAt)}
                  </span>
                </div>
              ) : (
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    {participantCount}/{room.maxParticipants}
                  </span>
                  <span className="flex items-center gap-1">
                    <Hash className="h-3 w-3" />
                    <span className="font-mono text-xs">{room.roomCode}</span>
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right: Actions */}
        <div className={cn(
          "flex items-center flex-shrink-0",
          isCompact ? "gap-1" : "gap-3"
        )}>
          {/* Mention Notifications */}
          {mentionNotifications}

          {/* Privacy Badge - Hidden on mobile */}
          <Badge variant="secondary" className="hidden sm:flex items-center gap-1.5">
            <Shield className="h-3 w-3" />
            Anonymous
          </Badge>

          {/* Share Button - Hidden on mobile since it's in bottom nav */}
          {!isCompact && (
            <Button
              variant="outline"
              size="sm"
              onClick={onShare}
              className="flex items-center gap-2"
            >
              <Share2 className="h-4 w-4" />
              Share Room
            </Button>
          )}

          {/* More Actions */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size={isCompact ? "sm" : "sm"}
                disabled={isLoading}
                className={cn(
                  "flex-shrink-0",
                  isCompact ? "h-8 w-8 p-0" : ""
                )}
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <MoreVertical className="h-4 w-4" />
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {/* Quick Actions */}
              {isCompact && (
                <DropdownMenuItem onClick={onShare}>
                  <Share2 className="mr-2 h-4 w-4" />
                  Share Room
                </DropdownMenuItem>
              )}

              <DropdownMenuItem onClick={() => navigator.clipboard?.writeText(room.roomCode)}>
                <Copy className="mr-2 h-4 w-4" />
                Copy Room Code
              </DropdownMenuItem>

              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                Room Settings
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              {/* Mobile Room Info */}
              {isCompact && (
                <>
                  <div className="px-3 py-2 text-xs">
                    <div className="text-muted-foreground mb-2">Room Information</div>
                    <div className="space-y-1 text-foreground">
                      <div className="flex justify-between">
                        <span>Code:</span>
                        <span className="font-mono">#{room.roomCode}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Participants:</span>
                        <span>{participantCount}/{room.maxParticipants}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Created:</span>
                        <span>{formatRelativeTime(room.createdAt)}</span>
                      </div>
                    </div>
                  </div>
                  <DropdownMenuSeparator />
                </>
              )}

              {/* Host Actions */}
              {isCreator && onEnd && (
                <>
                  <DropdownMenuItem
                    onClick={onEnd}
                    className="text-destructive focus:text-destructive"
                  >
                    <AlertTriangle className="mr-2 h-4 w-4" />
                    End Room
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                </>
              )}

              {/* Leave Room */}
              <DropdownMenuItem
                onClick={onLeave}
                className="text-destructive focus:text-destructive"
              >
                <LogOut className="mr-2 h-4 w-4" />
                Leave Room
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
