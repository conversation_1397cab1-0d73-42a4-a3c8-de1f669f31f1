// Accessibility utilities for WCAG 2.1 AA compliance
// Production-ready accessibility helpers and functions

import { KeyboardEvent, RefObject } from 'react';

// ============================================================================
// KEYBOARD NAVIGATION UTILITIES
// ============================================================================

export const KEYBOARD_KEYS = {
  ENTER: 'Enter',
  SPACE: ' ',
  ESCAPE: 'Escape',
  TAB: 'Tab',
  ARROW_UP: 'ArrowUp',
  ARROW_DOWN: 'ArrowDown',
  ARROW_LEFT: 'ArrowLeft',
  ARROW_RIGHT: 'ArrowRight',
  HOME: 'Home',
  END: 'End',
  PAGE_UP: 'PageUp',
  PAGE_DOWN: 'PageDown',
} as const;

export type KeyboardKey = typeof KEYBOARD_KEYS[keyof typeof KEYBOARD_KEYS];

/**
 * Check if a key event matches specific keys
 */
export function isKeyPressed(event: KeyboardEvent, keys: KeyboardKey | KeyboardKey[]): boolean {
  const targetKeys = Array.isArray(keys) ? keys : [keys];
  return targetKeys.includes(event.key as KeyboardKey);
}

/**
 * Handle activation keys (Enter and Space) for interactive elements
 */
export function handleActivationKeys(
  event: KeyboardEvent,
  callback: () => void,
  preventDefault = true
): void {
  if (isKeyPressed(event, [KEYBOARD_KEYS.ENTER, KEYBOARD_KEYS.SPACE])) {
    if (preventDefault) {
      event.preventDefault();
    }
    callback();
  }
}

/**
 * Create keyboard navigation handler for lists
 */
export function createListNavigationHandler(
  items: HTMLElement[],
  currentIndex: number,
  onIndexChange: (index: number) => void,
  options: {
    wrap?: boolean;
    orientation?: 'horizontal' | 'vertical';
  } = {}
) {
  const { wrap = true, orientation = 'vertical' } = options;
  
  return (event: KeyboardEvent) => {
    const isVertical = orientation === 'vertical';
    const nextKey = isVertical ? KEYBOARD_KEYS.ARROW_DOWN : KEYBOARD_KEYS.ARROW_RIGHT;
    const prevKey = isVertical ? KEYBOARD_KEYS.ARROW_UP : KEYBOARD_KEYS.ARROW_LEFT;
    
    let newIndex = currentIndex;
    
    if (isKeyPressed(event, nextKey)) {
      newIndex = currentIndex + 1;
      if (newIndex >= items.length) {
        newIndex = wrap ? 0 : items.length - 1;
      }
    } else if (isKeyPressed(event, prevKey)) {
      newIndex = currentIndex - 1;
      if (newIndex < 0) {
        newIndex = wrap ? items.length - 1 : 0;
      }
    } else if (isKeyPressed(event, KEYBOARD_KEYS.HOME)) {
      newIndex = 0;
    } else if (isKeyPressed(event, KEYBOARD_KEYS.END)) {
      newIndex = items.length - 1;
    } else {
      return; // No navigation key pressed
    }
    
    event.preventDefault();
    onIndexChange(newIndex);
    items[newIndex]?.focus();
  };
}

// ============================================================================
// ARIA UTILITIES
// ============================================================================

/**
 * Generate unique IDs for ARIA relationships
 */
export function generateAriaId(prefix: string): string {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Create ARIA attributes for expandable content
 */
export function createExpandableAriaProps(
  isExpanded: boolean,
  contentId: string,
  triggerId?: string
) {
  return {
    'aria-expanded': isExpanded,
    'aria-controls': contentId,
    ...(triggerId && { id: triggerId }),
  };
}

/**
 * Create ARIA attributes for modal dialogs
 */
export function createModalAriaProps(
  titleId: string,
  descriptionId?: string,
  isOpen = true
) {
  return {
    role: 'dialog',
    'aria-modal': isOpen,
    'aria-labelledby': titleId,
    ...(descriptionId && { 'aria-describedby': descriptionId }),
  };
}

/**
 * Create ARIA attributes for live regions
 */
export function createLiveRegionProps(
  politeness: 'polite' | 'assertive' = 'polite',
  atomic = false
) {
  return {
    'aria-live': politeness,
    'aria-atomic': atomic,
  };
}

// ============================================================================
// FOCUS MANAGEMENT
// ============================================================================

/**
 * Trap focus within a container element
 */
export function trapFocus(
  containerRef: RefObject<HTMLElement>,
  isActive: boolean
): () => void {
  if (!isActive || !containerRef.current) {
    return () => {};
  }

  const container = containerRef.current;
  const focusableElements = container.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  ) as NodeListOf<HTMLElement>;

  const firstElement = focusableElements[0];
  const lastElement = focusableElements[focusableElements.length - 1];

  const handleTabKey = (event: KeyboardEvent) => {
    if (event.key !== KEYBOARD_KEYS.TAB) return;

    if (event.shiftKey) {
      if (document.activeElement === firstElement) {
        event.preventDefault();
        lastElement?.focus();
      }
    } else {
      if (document.activeElement === lastElement) {
        event.preventDefault();
        firstElement?.focus();
      }
    }
  };

  container.addEventListener('keydown', handleTabKey as any);
  firstElement?.focus();

  return () => {
    container.removeEventListener('keydown', handleTabKey as any);
  };
}

/**
 * Restore focus to a previously focused element
 */
export function createFocusRestorer(): {
  save: () => void;
  restore: () => void;
} {
  let previouslyFocusedElement: HTMLElement | null = null;

  return {
    save: () => {
      previouslyFocusedElement = document.activeElement as HTMLElement;
    },
    restore: () => {
      if (previouslyFocusedElement && document.contains(previouslyFocusedElement)) {
        previouslyFocusedElement.focus();
      }
    },
  };
}

// ============================================================================
// SCREEN READER UTILITIES
// ============================================================================

/**
 * Announce content to screen readers
 */
export function announceToScreenReader(
  message: string,
  politeness: 'polite' | 'assertive' = 'polite'
): void {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', politeness);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;

  document.body.appendChild(announcement);

  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
}

/**
 * Create screen reader only text
 */
export function createScreenReaderText(text: string): string {
  return `<span class="sr-only">${text}</span>`;
}

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Check if an element is focusable
 */
export function isFocusable(element: HTMLElement): boolean {
  const focusableSelectors = [
    'button:not([disabled])',
    '[href]',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]',
  ];

  return focusableSelectors.some(selector => element.matches(selector));
}

/**
 * Check if an element is visible to screen readers
 */
export function isVisibleToScreenReaders(element: HTMLElement): boolean {
  const style = window.getComputedStyle(element);
  
  return !(
    style.display === 'none' ||
    style.visibility === 'hidden' ||
    element.hasAttribute('aria-hidden') ||
    element.hidden
  );
}

// ============================================================================
// COLOR CONTRAST UTILITIES
// ============================================================================

/**
 * Calculate relative luminance of a color
 */
export function getRelativeLuminance(hex: string): number {
  const rgb = hexToRgb(hex);
  if (!rgb) return 0;

  const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });

  return 0.2126 * r + 0.7152 * g + 0.0722 * b;
}

/**
 * Calculate contrast ratio between two colors
 */
export function getContrastRatio(color1: string, color2: string): number {
  const l1 = getRelativeLuminance(color1);
  const l2 = getRelativeLuminance(color2);
  const lighter = Math.max(l1, l2);
  const darker = Math.min(l1, l2);
  
  return (lighter + 0.05) / (darker + 0.05);
}

/**
 * Check if color combination meets WCAG contrast requirements
 */
export function meetsContrastRequirement(
  foreground: string,
  background: string,
  level: 'AA' | 'AAA' = 'AA',
  size: 'normal' | 'large' = 'normal'
): boolean {
  const ratio = getContrastRatio(foreground, background);
  const threshold = level === 'AAA' 
    ? (size === 'large' ? 4.5 : 7) 
    : (size === 'large' ? 3 : 4.5);
  
  return ratio >= threshold;
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}
