// Enhanced error handling hook
// Production-ready error management with recovery strategies

'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { AppError, LoadingState } from '@/lib/types';
import { toast } from 'sonner';

// ============================================================================
// ERROR HANDLER HOOK
// ============================================================================

interface UseErrorHandlerOptions {
  onError?: (error: AppError) => void;
  enableRetry?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  enableToast?: boolean;
  toastDuration?: number;
}

interface ErrorState {
  error: AppError | null;
  isRetrying: boolean;
  retryCount: number;
  lastRetryAt: number | null;
}

export function useErrorHandler(options: UseErrorHandlerOptions = {}) {
  const {
    onError,
    enableRetry = true,
    maxRetries = 3,
    retryDelay = 1000,
    enableToast = true,
    toastDuration = 5000,
  } = options;

  const [errorState, setErrorState] = useState<ErrorState>({
    error: null,
    isRetrying: false,
    retryCount: 0,
    lastRetryAt: null,
  });

  const retryTimeoutRef = useRef<NodeJS.Timeout>();

  // Clear error
  const clearError = useCallback(() => {
    setErrorState({
      error: null,
      isRetrying: false,
      retryCount: 0,
      lastRetryAt: null,
    });
    
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }
  }, []);

  // Handle error
  const handleError = useCallback((error: unknown, context?: string) => {
    const appError: AppError = error instanceof Error 
      ? {
          code: error.name || 'UNKNOWN_ERROR',
          message: error.message,
          details: { context, stack: error.stack },
          timestamp: Date.now(),
          recoverable: true,
        }
      : {
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred',
          details: { error, context },
          timestamp: Date.now(),
          recoverable: false,
        };

    setErrorState(prev => ({
      ...prev,
      error: appError,
      isRetrying: false,
    }));

    // Show toast notification
    if (enableToast) {
      toast.error(appError.message, {
        duration: toastDuration,
        action: appError.recoverable && enableRetry ? {
          label: 'Retry',
          onClick: () => retry(),
        } : undefined,
      });
    }

    // Call custom error handler
    onError?.(appError);

    return appError;
  }, [enableToast, enableRetry, maxRetries, onError, retryDelay, toastDuration]);

  // Retry function
  const retry = useCallback(async (retryFn?: () => Promise<void>) => {
    if (!errorState.error || !errorState.error.recoverable) {
      return;
    }

    if (errorState.retryCount >= maxRetries) {
      toast.error('Maximum retry attempts reached');
      return;
    }

    setErrorState(prev => ({
      ...prev,
      isRetrying: true,
    }));

    const delay = retryDelay * Math.pow(2, errorState.retryCount); // Exponential backoff

    retryTimeoutRef.current = setTimeout(async () => {
      try {
        if (retryFn) {
          await retryFn();
        }
        
        // Success - clear error
        clearError();
        toast.success('Operation completed successfully');
      } catch (error) {
        // Retry failed - increment count
        setErrorState(prev => ({
          ...prev,
          isRetrying: false,
          retryCount: prev.retryCount + 1,
          lastRetryAt: Date.now(),
        }));

        if (errorState.retryCount + 1 >= maxRetries) {
          toast.error('All retry attempts failed');
        }
      }
    }, delay);
  }, [errorState.error, errorState.retryCount, maxRetries, retryDelay, clearError]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  return {
    error: errorState.error,
    isRetrying: errorState.isRetrying,
    retryCount: errorState.retryCount,
    canRetry: errorState.error?.recoverable && errorState.retryCount < maxRetries,
    handleError,
    clearError,
    retry,
  };
}

// ============================================================================
// ASYNC OPERATION HOOK
// ============================================================================

interface UseAsyncOperationOptions<T> extends UseErrorHandlerOptions {
  onSuccess?: (data: T) => void;
  initialData?: T;
}

export function useAsyncOperation<T = any>(
  options: UseAsyncOperationOptions<T> = {}
) {
  const { onSuccess, initialData, ...errorOptions } = options;
  
  const [state, setState] = useState<LoadingState>('idle');
  const [data, setData] = useState<T | null>(initialData || null);
  
  const { handleError, clearError, retry, ...errorState } = useErrorHandler(errorOptions);

  // Execute async operation
  const execute = useCallback(async (
    operation: () => Promise<T>,
    options: { silent?: boolean } = {}
  ) => {
    try {
      setState('loading');
      clearError();
      
      const result = await operation();
      
      setData(result);
      setState('success');
      
      if (!options.silent) {
        onSuccess?.(result);
      }
      
      return result;
    } catch (error) {
      setState('error');
      const appError = handleError(error);
      throw appError;
    }
  }, [handleError, clearError, onSuccess]);

  // Reset state
  const reset = useCallback(() => {
    setState('idle');
    setData(initialData || null);
    clearError();
  }, [initialData, clearError]);

  // Retry with the last operation
  const retryOperation = useCallback(async (operation?: () => Promise<T>) => {
    if (operation) {
      return execute(operation);
    }
    
    // If no operation provided, use the retry from error handler
    return retry();
  }, [execute, retry]);

  return {
    state,
    data,
    isLoading: state === 'loading',
    isSuccess: state === 'success',
    isError: state === 'error',
    isIdle: state === 'idle',
    execute,
    reset,
    retry: retryOperation,
    ...errorState,
  };
}

// ============================================================================
// FORM ERROR HOOK
// ============================================================================

interface FieldError {
  message: string;
  type: string;
}

interface FormErrors {
  [field: string]: FieldError | undefined;
}

export function useFormErrors() {
  const [errors, setErrors] = useState<FormErrors>({});

  const setFieldError = useCallback((field: string, error: FieldError) => {
    setErrors(prev => ({ ...prev, [field]: error }));
  }, []);

  const clearFieldError = useCallback((field: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  const hasErrors = Object.keys(errors).length > 0;
  const getFieldError = useCallback((field: string) => errors[field], [errors]);

  return {
    errors,
    hasErrors,
    setFieldError,
    clearFieldError,
    clearAllErrors,
    getFieldError,
  };
}

// ============================================================================
// NETWORK ERROR HOOK
// ============================================================================

export function useNetworkError() {
  const { handleError, ...errorState } = useErrorHandler({
    enableRetry: true,
    maxRetries: 3,
    retryDelay: 2000,
  });

  const handleNetworkError = useCallback((error: unknown, operation?: string) => {
    let networkError: AppError;

    if (error instanceof TypeError && error.message.includes('fetch')) {
      networkError = {
        code: 'NETWORK_ERROR',
        message: 'Network connection failed. Please check your internet connection.',
        details: { operation, originalError: error.message },
        timestamp: Date.now(),
        recoverable: true,
      };
    } else if (error instanceof Error && error.message.includes('timeout')) {
      networkError = {
        code: 'TIMEOUT_ERROR',
        message: 'Request timed out. Please try again.',
        details: { operation, originalError: error.message },
        timestamp: Date.now(),
        recoverable: true,
      };
    } else {
      networkError = {
        code: 'REQUEST_ERROR',
        message: 'Request failed. Please try again.',
        details: { operation, error },
        timestamp: Date.now(),
        recoverable: true,
      };
    }

    return handleError(networkError);
  }, [handleError]);

  return {
    handleNetworkError,
    ...errorState,
  };
}

// ============================================================================
// ERROR BOUNDARY HOOK
// ============================================================================

export function useErrorBoundary() {
  const [hasError, setHasError] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const resetError = useCallback(() => {
    setHasError(false);
    setError(null);
  }, []);

  const captureError = useCallback((error: Error) => {
    setHasError(true);
    setError(error);
    
    // Log error for monitoring
    console.error('Error boundary captured:', error);
    
    // You could send to error reporting service here
    // errorReportingService.captureException(error);
  }, []);

  return {
    hasError,
    error,
    resetError,
    captureError,
  };
}
