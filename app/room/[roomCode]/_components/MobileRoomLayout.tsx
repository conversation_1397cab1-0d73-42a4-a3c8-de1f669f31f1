'use client';

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from 'next-themes';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import {
  Users,
  Phone,
  Settings,
  Share2,
  MoreVertical,
  X,
  Hash,
  Shield,
  Wifi,
  Crown,
  LogOut,
  Sun,
  Moon,
  Volume2,
  Signal
} from 'lucide-react';

// Import existing components
import { ChatInterface } from '@/components/room/ChatInterface';
import { MessageInput } from '@/components/room/MessageInput';
import { ParticipantsList } from '@/components/room/ParticipantsList';
import { TypingIndicator } from '@/components/room/TypingIndicator';
import { CompactModernVoiceControls } from './ModernVoiceControls';
import { useVoiceRoom } from '@/hooks/useVoiceRoom';

// Types
import type { Room, ParticipantSummary, Message, TypingParticipant as TypingIndicatorType } from '@/lib/types';
import type { Id } from '@/convex/_generated/dataModel';

interface MobileRoomLayoutProps {
  room: Room;
  participants: ParticipantSummary[];
  messages: Message[];
  typingIndicators: TypingIndicatorType[];
  currentParticipant?: ParticipantSummary;
  isCreator?: boolean;
  onReaction: (messageId: string, emoji: string) => Promise<void>;
  onReply: (messageId: string, content: string, author: string) => void;
  onEdit: (messageId: string, newContent: string) => Promise<void>;
  onDelete: (messageId: string) => Promise<void>;
  onMentionClick: (participantSessionId: string) => void;
  onLeave: () => void;
  onShare: () => void;
  onEndRoom?: () => void;
  replyingTo?: {
    messageId: Id<'messages'>;
    content: string;
    author: string;
  } | null;
  onCancelReply?: () => void;
  // Media controls removed - now using voice controls directly
}



export function MobileRoomLayout({
  room,
  participants,
  messages,
  typingIndicators,
  currentParticipant,
  isCreator = false,
  onReaction,
  onReply,
  onEdit,
  onDelete,
  onMentionClick,
  onLeave,
  onShare,
  onEndRoom,
  replyingTo,
  onCancelReply
}: MobileRoomLayoutProps) {
  const [showParticipants, setShowParticipants] = useState(false);
  const { theme, setTheme } = useTheme();

  // Voice room functionality
  const { participants: voiceParticipants } = useVoiceRoom(room._id);



  const handleReaction = useCallback(async (messageId: string, emoji: string) => {
    try {
      await onReaction(messageId, emoji);
    } catch (error) {
      // Handle error silently
    }
  }, [onReaction]);

  // Computed values
  const typingParticipants = typingIndicators.map(t => ({
    sessionId: t.sessionId,
    anonymousAlias: participants.find(p => p.sessionId === t.sessionId)?.anonymousAlias || 'Anonymous'
  }));

  const activeParticipants = participants.filter(p => p.isActive);

  // Transform messages for ChatInterface compatibility
  const transformedMessages = messages.map(msg => ({
    id: msg._id,
    roomId: msg.roomId,
    participantSessionId: msg.participantSessionId,
    anonymousAlias: msg.anonymousAlias,
    content: msg.content,
    messageType: msg.messageType,
    timestamp: msg.timestamp,
    isEdited: msg.isEdited,
    editedAt: msg.editedAt,
    reactions: msg.reactions,
    replyToMessageId: msg.replyToMessageId,
    threadRootId: msg.threadRootId,
    threadDepth: msg.threadDepth,
    threadReplyCount: msg.threadReplyCount,
    mentions: msg.mentions,
    hasMentions: msg.hasMentions,
    engagementMetrics: msg.engagementMetrics
  }));

  // Transform participants for ChatInterface compatibility
  const transformedParticipants = participants.map(p => ({
    sessionId: p.sessionId,
    anonymousAlias: p.anonymousAlias,
    avatarSeed: p.avatarSeed,
    joinedAt: p.joinedAt,
    messageCount: p.messageCount,
    isActive: p.isActive,
    isMuted: p.isMuted
  }));

  const transformedCurrentParticipant = currentParticipant ? {
    sessionId: currentParticipant.sessionId,
    anonymousAlias: currentParticipant.anonymousAlias,
    avatarSeed: currentParticipant.avatarSeed,
    joinedAt: currentParticipant.joinedAt,
    messageCount: currentParticipant.messageCount,
    isActive: currentParticipant.isActive,
    isMuted: currentParticipant.isMuted
  } : undefined;

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Mobile Header */}
      <header className="border-b border-border bg-card/60 backdrop-blur-md sticky top-0 z-40 safe-area-pt">
        <div className="flex items-center justify-between px-3 py-2.5">
          {/* App Branding & Room Info */}
          <div className="flex items-center gap-2.5 min-w-0 flex-1">
            {/* VeilMeet Logo & Brand */}
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="flex items-center gap-2 flex-shrink-0"
            >
              <div className="bg-gradient-to-br from-purple-500 via-purple-600 to-blue-600 rounded-lg p-1.5 shadow-lg">
                <Shield className="h-4 w-4 text-white drop-shadow-sm" />
              </div>
              <span className="text-sm font-bold text-foreground bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                VeilMeet
              </span>
            </motion.div>

            {/* Vertical Separator */}
            <div className="w-px h-8 bg-border flex-shrink-0" />

            {/* Room Details */}
            <div className="min-w-0 flex-1">
              <h1 className="font-semibold text-sm text-foreground truncate mb-0.5">
                {room.topic || 'Anonymous Discussion'}
              </h1>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <Hash className="h-3 w-3" />
                <span className="font-mono">{room.roomCode}</span>
                <Users className="h-3 w-3 ml-1" />
                <span>{participants.length}</span>
                {room.allowVoice && (
                  <>
                    <Volume2 className="h-3 w-3 ml-1 text-green-600 dark:text-green-400" />
                    <span className="text-green-600 dark:text-green-400">Voice</span>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Header Actions */}
          <div className="flex items-center gap-1.5">
            {/* Connection Status */}
            <Badge variant="secondary" className="text-xs flex items-center gap-1">
              <Signal className="h-3 w-3" />
              <span className="hidden xs:inline">Live</span>
            </Badge>

            {/* Theme Toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
              className="h-8 w-8 p-0"
            >
              {theme === 'dark' ? (
                <Sun className="h-4 w-4" />
              ) : (
                <Moon className="h-4 w-4" />
              )}
            </Button>

            {/* Participants Sheet */}
            <Sheet open={showParticipants} onOpenChange={setShowParticipants}>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 relative transition-all duration-200 hover:bg-accent"
                >
                  <Users className="h-4 w-4" />
                  {activeParticipants.length > 0 && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute -top-1 -right-1"
                    >
                      <Badge
                        variant="default"
                        className="h-4 w-4 p-0 text-xs flex items-center justify-center bg-primary text-primary-foreground"
                      >
                        {activeParticipants.length}
                      </Badge>
                    </motion.div>
                  )}
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-80 p-0 bg-background/95 backdrop-blur-lg">
                <div className="h-full flex flex-col">
                  <motion.div
                    initial={{ y: -10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    className="px-4 py-4 border-b border-border bg-card/50"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-base">Participants</h3>
                      <Badge variant="secondary" className="text-xs">
                        {participants.length}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                      <span>{activeParticipants.length} active</span>
                      {room.allowVoice && voiceParticipants.length > 0 && (
                        <>
                          <div className="w-px h-3 bg-border mx-1" />
                          <Volume2 className="h-3 w-3" />
                          <span>{voiceParticipants.length} in voice</span>
                        </>
                      )}
                    </div>
                  </motion.div>
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.1 }}
                    className="flex-1 overflow-hidden p-3"
                  >
                    <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent">
                      <ParticipantsList
                        participants={participants}
                        voiceParticipants={voiceParticipants}
                        currentSessionId={currentParticipant?.sessionId}
                        showDetails={true}
                      />
                    </div>
                  </motion.div>
                </div>
              </SheetContent>
            </Sheet>

            {/* More Actions */}
            <Sheet>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 transition-all duration-200 hover:bg-accent"
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </SheetTrigger>
              <SheetContent side="bottom" className="h-auto bg-background/95 backdrop-blur-lg">
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  className="space-y-6 pb-6 pt-2"
                >
                  <div className="flex items-center justify-center">
                    <div className="w-12 h-1 bg-muted rounded-full" />
                  </div>

                  <h3 className="font-semibold text-center text-lg">Room Actions</h3>

                  <div className="grid grid-cols-3 gap-4">
                    <motion.div
                      initial={{ y: 10, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.1 }}
                    >
                      <Button
                        variant="outline"
                        onClick={onShare}
                        className="h-16 w-full flex flex-col gap-2 hover:bg-accent transition-colors duration-200"
                      >
                        <Share2 className="h-5 w-5" />
                        <span className="text-xs font-medium">Share</span>
                      </Button>
                    </motion.div>

                    <motion.div
                      initial={{ y: 10, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.15 }}
                    >
                      <Button
                        variant="outline"
                        className="h-16 w-full flex flex-col gap-2 hover:bg-accent transition-colors duration-200"
                      >
                        <Settings className="h-5 w-5" />
                        <span className="text-xs font-medium">Settings</span>
                      </Button>
                    </motion.div>

                    <motion.div
                      initial={{ y: 10, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.2 }}
                    >
                      <Button
                        variant="outline"
                        className="h-16 w-full flex flex-col gap-2 hover:bg-accent transition-colors duration-200"
                      >
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                        <span className="text-xs font-medium">AI Assistant</span>
                      </Button>
                    </motion.div>
                  </div>

                  <div className="space-y-3">
                    {isCreator && onEndRoom && (
                      <motion.div
                        initial={{ y: 10, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ delay: 0.25 }}
                      >
                        <Button
                          variant="destructive"
                          onClick={onEndRoom}
                          className="w-full h-12 text-base font-medium"
                        >
                          <Crown className="h-4 w-4 mr-2" />
                          End Room
                        </Button>
                      </motion.div>
                    )}

                    <motion.div
                      initial={{ y: 10, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.3 }}
                    >
                      <Button
                        variant="outline"
                        onClick={onLeave}
                        className="w-full h-12 text-base font-medium hover:bg-destructive/10 hover:text-destructive hover:border-destructive/20 transition-colors duration-200"
                      >
                        <LogOut className="h-4 w-4 mr-2" />
                        Leave Room
                      </Button>
                    </motion.div>
                  </div>
                </motion.div>
              </SheetContent>
            </Sheet>
          </div>
        </div>

        {/* Reply indicator */}
        <AnimatePresence>
          {replyingTo && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="px-3 py-2.5 bg-muted/60 border-t border-border overflow-hidden"
            >
              <div className="flex items-center justify-between gap-2">
                <div className="flex items-center gap-2 text-sm min-w-0 flex-1">
                  <motion.div
                    initial={{ rotate: 0 }}
                    animate={{ rotate: 180 }}
                    className="text-muted-foreground flex-shrink-0"
                  >
                    <X className="h-4 w-4 rotate-45" />
                  </motion.div>
                  <span className="text-muted-foreground">Replying to</span>
                  <span className="font-medium truncate">{replyingTo.author}</span>
                  <span className="text-muted-foreground truncate max-w-[100px]">
                    {replyingTo.content}
                  </span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onCancelReply}
                  className="h-7 w-7 p-0 flex-shrink-0 hover:bg-destructive/10 hover:text-destructive transition-colors duration-200"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex flex-col min-h-0 bg-background/50">
        {/* Chat Messages */}
        <div className="flex-1 min-h-0 relative">
          <div className="absolute inset-0">
            <ChatInterface
              messages={transformedMessages}
              currentParticipant={transformedCurrentParticipant}
              participants={transformedParticipants}
              onReaction={handleReaction}
              onReply={onReply}
              onEdit={onEdit}
              onDelete={onDelete}
              onMentionClick={onMentionClick}
              replyingTo={replyingTo}
            />
          </div>
        </div>

        {/* Typing Indicator */}
        <AnimatePresence>
          {typingParticipants.length > 0 && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <TypingIndicator
                typingParticipants={typingParticipants}
                currentSessionId={currentParticipant?.sessionId}
                className="border-t border-border bg-card/40 backdrop-blur-sm"
              />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Message Input */}
        <div className="border-t border-border bg-card/60 backdrop-blur-md">
          <div className="px-3 py-2">
            <MessageInput
              roomId={room._id}
              currentParticipant={currentParticipant}
              participants={participants}
              onMessageSent={() => {}}
              replyingTo={replyingTo}
              onCancelReply={onCancelReply}
              placeholder="Type a message..."
            />
          </div>
        </div>
      </main>

      {/* Bottom Controls */}
      <motion.div
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="border-t border-border bg-card/70 backdrop-blur-lg safe-area-pb"
      >
        <div className="px-4 py-3">
          <div className="flex items-center justify-between gap-3">
            {/* Modern Voice Controls */}
            <div className="flex-1">
              <CompactModernVoiceControls
                roomId={room._id}
                participantCount={participants.length}
                disabled={false}
                className="bg-transparent border-0 p-0"
              />
            </div>

            {/* Status Indicators */}
            <div className="flex items-center gap-2">
              {/* Connection Quality */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2 }}
              >
                <Badge variant="outline" className="text-xs flex items-center gap-1 border-green-200 text-green-700 dark:border-green-800 dark:text-green-300">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  <span className="hidden xs:inline">Connected</span>
                </Badge>
              </motion.div>

              {/* Active Participants */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.3 }}
              >
                <Badge variant="secondary" className="text-xs flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  <span>{activeParticipants.length}</span>
                  <span className="hidden xs:inline">online</span>
                </Badge>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
