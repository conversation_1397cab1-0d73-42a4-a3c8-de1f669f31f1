// Main room interface components
export { RoomInterface } from './RoomInterface';
export { MobileRoomLayout } from './MobileRoomLayout';

// Media and controls
export { MediaControls, CompactMediaControls } from './MediaControls';

// Enhanced messaging
export { EnhancedMessage } from './EnhancedMessage';

// Types for component props
export type {
  RoomInterfaceProps,
  MobileRoomLayoutProps,
  MediaControlsProps,
  EnhancedMessageProps,
  MediaControlsState,
  ConnectionQuality,
  ReplyContext,
  EnhancedMessageReaction,
  TypingParticipant,
  ConnectionStatus,
  ComponentUIState,
  MessageAction,
  MediaDevice,
  RoomStats,
  MessageEventHandlers,
  MediaEventHandlers,
  RoomEventHandlers,
  ComponentFeatures,
  AccessibilityConfig,
  PerformanceConfig
} from './types';


