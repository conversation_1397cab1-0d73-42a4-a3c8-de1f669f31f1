'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  AtSign,
  Bell,
  Check,
  MessageSquare,
  X
} from 'lucide-react';
import { useMentionNotifications, useEnhancedMessaging } from '@/hooks/useEnhancedMessaging';
import { formatRelativeTime } from '@/lib/identity';
import { cn } from '@/lib/utils';
import { Id } from '@/convex/_generated/dataModel';

interface MentionNotificationsProps {
  roomId: Id<'rooms'>;
  participantSessionId: string;
  className?: string;
}

export function MentionNotifications({
  roomId,
  participantSessionId,
  className
}: MentionNotificationsProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { 
    unreadMentions, 
    newMentions, 
    unreadCount, 
    newCount,
    markAsChecked 
  } = useMentionNotifications(participantSessionId);
  
  const { markMentionsAsRead } = useEnhancedMessaging(roomId);

  // Show notification for new mentions
  useEffect(() => {
    if (newCount > 0 && !isOpen) {
      // Could trigger a toast notification here
      console.log(`${newCount} new mention(s)`);
    }
  }, [newCount, isOpen]);

  // Mark as checked when popover opens
  useEffect(() => {
    if (isOpen) {
      markAsChecked();
    }
  }, [isOpen, markAsChecked]);

  const handleMarkAllAsRead = async () => {
    if (unreadMentions.length > 0) {
      await markMentionsAsRead(unreadMentions.map(m => m._id));
    }
  };

  const handleMarkAsRead = async (mentionId: string) => {
    await markMentionsAsRead([mentionId]);
  };

  if (unreadCount === 0) {
    return null;
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "relative h-8 w-8 p-0 hover:bg-muted",
            className
          )}
        >
          <Bell className="h-4 w-4" />
          {unreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs flex items-center justify-center"
            >
              {unreadCount > 9 ? '9+' : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-80 p-0" align="end">
        <div className="border-b border-border p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AtSign className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium text-sm">Mentions</span>
              {unreadCount > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {unreadCount}
                </Badge>
              )}
            </div>
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 text-xs"
                onClick={handleMarkAllAsRead}
              >
                <Check className="h-3 w-3 mr-1" />
                Mark all read
              </Button>
            )}
          </div>
        </div>

        <div className="max-h-80 overflow-y-auto">
          {unreadMentions.length === 0 ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              No unread mentions
            </div>
          ) : (
            <div className="space-y-1 p-1">
              {unreadMentions.map((mention) => (
                <div
                  key={mention._id}
                  className={cn(
                    "p-3 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer group",
                    !mention.isRead && "bg-blue-50 dark:bg-blue-950/20"
                  )}
                >
                  <div className="flex items-start justify-between gap-2">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <MessageSquare className="h-3 w-3 text-muted-foreground shrink-0" />
                        <span className="text-xs font-medium text-muted-foreground">
                          Mentioned by {mention.message?.anonymousAlias}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {formatRelativeTime(mention.createdAt)}
                        </span>
                      </div>
                      
                      {mention.message && (
                        <div className="text-sm text-foreground line-clamp-2">
                          {mention.message.content}
                        </div>
                      )}
                      
                      <div className="mt-1 text-xs text-blue-600 dark:text-blue-400">
                        {mention.mentionText}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMarkAsRead(mention._id);
                        }}
                        title="Mark as read"
                      >
                        <Check className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {unreadMentions.length > 0 && (
          <div className="border-t border-border p-2">
            <Button
              variant="ghost"
              size="sm"
              className="w-full text-xs"
              onClick={() => setIsOpen(false)}
            >
              Close
            </Button>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}

// Compact mention indicator for mobile
export function MentionIndicator({
  unreadCount,
  onClick,
  className
}: {
  unreadCount: number;
  onClick?: () => void;
  className?: string;
}) {
  if (unreadCount === 0) return null;

  return (
    <Button
      variant="ghost"
      size="sm"
      className={cn(
        "h-6 px-2 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-900/50",
        className
      )}
      onClick={onClick}
    >
      <AtSign className="h-3 w-3 mr-1" />
      {unreadCount} mention{unreadCount !== 1 ? 's' : ''}
    </Button>
  );
}

// Toast notification component for new mentions
export function MentionToast({
  mention,
  onDismiss,
  onMarkAsRead
}: {
  mention: any;
  onDismiss: () => void;
  onMarkAsRead: () => void;
}) {
  return (
    <div className="bg-background border border-border rounded-lg shadow-lg p-3 max-w-sm">
      <div className="flex items-start justify-between gap-2">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <AtSign className="h-3 w-3 text-blue-500" />
            <span className="text-xs font-medium">
              New mention from {mention.message?.anonymousAlias}
            </span>
          </div>
          
          {mention.message && (
            <div className="text-sm text-muted-foreground line-clamp-2">
              {mention.message.content}
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={onMarkAsRead}
            title="Mark as read"
          >
            <Check className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={onDismiss}
            title="Dismiss"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  );
}
