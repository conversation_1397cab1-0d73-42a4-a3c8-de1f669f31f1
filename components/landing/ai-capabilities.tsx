"use client";

import { motion } from "motion/react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Brain, 
  Shield, 
  MessageSquare, 
  TrendingUp,
  Zap,
  Eye,
  Users,
  BarChart3,
  <PERSON>rkles,
  ArrowRight
} from "lucide-react";

const aiFeatures = [
  {
    icon: Brain,
    title: "Intelligent Moderation",
    description: "Advanced AI monitors conversations in real-time, preventing harassment while preserving authentic dialogue.",
    capabilities: ["Toxicity detection", "Bias prevention", "Context awareness", "Escalation management"],
    color: "primary"
  },
  {
    icon: MessageSquare,
    title: "Smart Facilitation",
    description: "AI suggests conversation starters, manages speaking turns, and keeps discussions productive and engaging.",
    capabilities: ["Topic suggestions", "Turn management", "Engagement tracking", "Flow optimization"],
    color: "cyan-500"
  },
  {
    icon: TrendingUp,
    title: "Sentiment Analysis",
    description: "Real-time emotional intelligence helps maintain positive conversation dynamics without compromising anonymity.",
    capabilities: ["Mood detection", "Tension alerts", "Positivity boosting", "Emotional support"],
    color: "green-500"
  },
  {
    icon: BarChart3,
    title: "Anonymous Insights",
    description: "Generate valuable conversation analytics and summaries while maintaining complete participant privacy.",
    capabilities: ["Conversation summaries", "Key insights", "Participation metrics", "Trend analysis"],
    color: "orange-500"
  }
];

export function AiCapabilities() {
  return (
    <section className="py-16 sm:py-20 lg:py-24 px-4 sm:px-6 lg:px-8 relative bg-gradient-to-b from-primary/5 to-background">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          className="text-center mb-12 sm:mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="mb-4 border-primary/30 text-primary text-xs sm:text-sm">
            <Sparkles className="w-3 h-3 mr-1" />
            AI-Powered Intelligence
          </Badge>
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 px-2">
            <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent block">Next-Generation AI</span>
            <span className="text-foreground block">Meets Privacy</span>
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed px-2">
            Our advanced AI doesn't just moderate—it enhances every aspect of anonymous conversations,
            making them more productive, insightful, and meaningful while protecting your identity.
          </p>
        </motion.div>

        {/* AI Features Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 mb-12 sm:mb-16">
          {aiFeatures.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02 }}
              className="group"
            >
              <Card className="h-full bg-card/50 backdrop-blur-sm border-border/50 hover:border-primary/30 transition-all duration-300 group-hover:shadow-lg">
                <CardContent className="p-4 sm:p-6 lg:p-8">
                  {/* Header */}
                  <div className="flex flex-col sm:flex-row items-start gap-3 sm:gap-4 mb-4 sm:mb-6">
                    <div className={`p-3 sm:p-4 rounded-xl sm:rounded-2xl bg-${feature.color}/10 group-hover:bg-${feature.color}/20 transition-colors flex-shrink-0`}>
                      <feature.icon className={`w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-${feature.color}`} />
                    </div>
                    <h3 className="text-lg sm:text-xl lg:text-2xl font-semibold group-hover:text-primary transition-colors leading-tight">
                      {feature.title}
                    </h3>
                  </div>

                  {/* Description */}
                  <p className="text-sm sm:text-base lg:text-lg text-muted-foreground mb-4 sm:mb-6 leading-relaxed">
                    {feature.description}
                  </p>

                  {/* Capabilities */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                    {feature.capabilities.map((capability, capIndex) => (
                      <div key={capIndex} className="flex items-center gap-2 text-xs sm:text-sm">
                        <div className={`w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full bg-${feature.color} flex-shrink-0`} />
                        <span className="text-muted-foreground">{capability}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* AI Demo Section */}
        <motion.div
          className="bg-gradient-to-br from-primary/20 to-primary/10 rounded-3xl p-8 lg:p-12 border border-primary/20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Content */}
            <div>
              <Badge variant="secondary" className="mb-4">
                <Zap className="w-3 h-3 mr-1" />
                Live AI Demo
              </Badge>
              
              <h3 className="text-3xl font-bold mb-6">
                See AI in Action
              </h3>
              
              <p className="text-muted-foreground mb-8 text-lg leading-relaxed">
                Experience how our AI seamlessly enhances conversations without intruding. 
                Watch real-time moderation, smart suggestions, and intelligent facilitation 
                work together to create the perfect anonymous discussion environment.
              </p>

              <div className="space-y-4 mb-8">
                <div className="flex items-center gap-3">
                  <Shield className="w-5 h-5 text-green-500" />
                  <span>Real-time toxicity prevention</span>
                </div>
                <div className="flex items-center gap-3">
                  <Eye className="w-5 h-5 text-blue-500" />
                  <span>Context-aware conversation guidance</span>
                </div>
                <div className="flex items-center gap-3">
                  <Users className="w-5 h-5 text-primary" />
                  <span>Intelligent participant management</span>
                </div>
              </div>

              <Button
                size="lg"
                className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-6 text-lg font-semibold transition-all duration-300 group shadow-lg hover:shadow-xl"
              >
                Try AI-Enhanced Room
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>

            {/* Right side - Visual representation */}
            <div className="relative">
              <div className="bg-card/30 backdrop-blur-sm rounded-2xl p-6 border border-border/50">
                {/* Mock conversation interface */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
                    <div className="w-2 h-2 bg-chart-3 rounded-full animate-pulse" />
                    AI Moderator Active
                  </div>

                  {/* Mock messages */}
                  <div className="space-y-3">
                    <div className="bg-primary/10 rounded-lg p-3">
                      <div className="text-sm text-primary mb-1">Anonymous User 1</div>
                      <div className="text-sm">I think we should discuss the budget concerns...</div>
                    </div>

                    <div className="bg-chart-2/10 rounded-lg p-3 border-l-2 border-chart-2">
                      <div className="text-sm text-chart-2 mb-1">AI Facilitator</div>
                      <div className="text-sm">Great topic! Would anyone like to share specific concerns?</div>
                    </div>

                    <div className="bg-chart-3/10 rounded-lg p-3">
                      <div className="text-sm text-chart-3 mb-1">Anonymous User 2</div>
                      <div className="text-sm">The marketing spend seems excessive...</div>
                    </div>
                  </div>
                  
                  {/* AI insights panel */}
                  <div className="mt-6 p-4 bg-background/50 rounded-lg border border-border/30">
                    <div className="text-xs text-muted-foreground mb-2">AI Insights</div>
                    <div className="text-sm">
                      <div className="flex items-center gap-2 mb-1">
                        <div className="w-1.5 h-1.5 bg-chart-3 rounded-full" />
                        Positive sentiment: 85%
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-chart-1 rounded-full" />
                        Engagement level: High
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Background decoration */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-72 h-72 bg-accent/5 rounded-full blur-3xl" />
      </div>
    </section>
  );
}
