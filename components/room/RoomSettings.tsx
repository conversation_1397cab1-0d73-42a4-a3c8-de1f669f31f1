'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { 
  Settings, 
  Users, 
  Shield, 
  Mic, 
  Video, 
  Eye, 
  EyeOff,
  Crown,
  AlertTriangle,
  Save,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';

interface RoomSettingsProps {
  room: {
    _id: string;
    roomCode: string;
    topic?: string;
    status: string;
    isPublic: boolean;
    maxParticipants: number;
    currentParticipants: number;
    allowVoice: boolean;
    allowVideo: boolean;
    allowScreenShare: boolean;
    voiceModulationEnabled: boolean;
    recordingAllowed: boolean;
    ephemeralMode: boolean;
    aiModerationLevel: string;
    aiAgentsEnabled: string[];
  };
  isCreator: boolean;
  onUpdateRoom?: (updates: Partial<typeof room>) => Promise<void>;
  onEndRoom?: () => Promise<void>;
}

export function RoomSettings({ 
  room, 
  isCreator, 
  onUpdateRoom, 
  onEndRoom 
}: RoomSettingsProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [settings, setSettings] = useState({
    topic: room.topic || '',
    isPublic: room.isPublic,
    maxParticipants: room.maxParticipants,
    allowVoice: room.allowVoice,
    allowVideo: room.allowVideo,
    allowScreenShare: room.allowScreenShare,
    voiceModulationEnabled: room.voiceModulationEnabled,
    recordingAllowed: room.recordingAllowed,
    ephemeralMode: room.ephemeralMode,
    aiModerationLevel: room.aiModerationLevel,
  });

  const handleSaveSettings = async () => {
    if (!onUpdateRoom) return;

    try {
      setIsLoading(true);
      await onUpdateRoom(settings);
      setIsOpen(false);
      toast.success('Room settings updated successfully');
    } catch (error) {
      toast.error('Failed to update room settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEndRoom = async () => {
    if (!onEndRoom) return;

    try {
      setIsLoading(true);
      await onEndRoom();
      toast.success('Room ended successfully');
    } catch (error) {
      toast.error('Failed to end room');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className="flex items-center gap-2">
          <Settings className="h-4 w-4" />
          Room Settings
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Room Settings
            {isCreator && (
              <Badge variant="outline" className="ml-2">
                <Crown className="h-3 w-3 mr-1" />
                Creator
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            {isCreator 
              ? "Manage your room settings and preferences"
              : "View room settings (read-only)"
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="topic">Discussion Topic</Label>
                <Input
                  id="topic"
                  value={settings.topic}
                  onChange={(e) => setSettings(prev => ({ ...prev, topic: e.target.value }))}
                  placeholder="What's this room about?"
                  disabled={!isCreator}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Public Room</Label>
                  <p className="text-sm text-muted-foreground">
                    Allow others to discover this room
                  </p>
                </div>
                <Switch
                  checked={settings.isPublic}
                  onCheckedChange={(checked) => 
                    setSettings(prev => ({ ...prev, isPublic: checked }))
                  }
                  disabled={!isCreator}
                />
              </div>

              <div className="space-y-2">
                <Label>Maximum Participants: {settings.maxParticipants}</Label>
                <Slider
                  value={[settings.maxParticipants]}
                  onValueChange={([value]) => 
                    setSettings(prev => ({ ...prev, maxParticipants: value }))
                  }
                  max={50}
                  min={2}
                  step={1}
                  disabled={!isCreator}
                />
                <p className="text-xs text-muted-foreground">
                  Current participants: {room.currentParticipants}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Media Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <Mic className="h-4 w-4" />
                Media & Communication
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="flex items-center gap-2">
                      <Mic className="h-3 w-3" />
                      Voice Chat
                    </Label>
                  </div>
                  <Switch
                    checked={settings.allowVoice}
                    onCheckedChange={(checked) => 
                      setSettings(prev => ({ ...prev, allowVoice: checked }))
                    }
                    disabled={!isCreator}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="flex items-center gap-2">
                      <Video className="h-3 w-3" />
                      Video Chat
                    </Label>
                  </div>
                  <Switch
                    checked={settings.allowVideo}
                    onCheckedChange={(checked) => 
                      setSettings(prev => ({ ...prev, allowVideo: checked }))
                    }
                    disabled={!isCreator}
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Screen Sharing</Label>
                  <p className="text-sm text-muted-foreground">
                    Allow participants to share their screen
                  </p>
                </div>
                <Switch
                  checked={settings.allowScreenShare}
                  onCheckedChange={(checked) => 
                    setSettings(prev => ({ ...prev, allowScreenShare: checked }))
                  }
                  disabled={!isCreator}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Voice Modulation</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable voice changing for anonymity
                  </p>
                </div>
                <Switch
                  checked={settings.voiceModulationEnabled}
                  onCheckedChange={(checked) => 
                    setSettings(prev => ({ ...prev, voiceModulationEnabled: checked }))
                  }
                  disabled={!isCreator}
                />
              </div>
            </CardContent>
          </Card>

          {/* Privacy Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Privacy & Security
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Recording Allowed</Label>
                  <p className="text-sm text-muted-foreground">
                    Allow participants to record the session
                  </p>
                </div>
                <Switch
                  checked={settings.recordingAllowed}
                  onCheckedChange={(checked) => 
                    setSettings(prev => ({ ...prev, recordingAllowed: checked }))
                  }
                  disabled={!isCreator}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Ephemeral Mode</Label>
                  <p className="text-sm text-muted-foreground">
                    Messages are deleted after the room ends
                  </p>
                </div>
                <Switch
                  checked={settings.ephemeralMode}
                  onCheckedChange={(checked) => 
                    setSettings(prev => ({ ...prev, ephemeralMode: checked }))
                  }
                  disabled={!isCreator}
                />
              </div>

              <div className="space-y-2">
                <Label>AI Moderation Level</Label>
                <Select
                  value={settings.aiModerationLevel}
                  onValueChange={(value) =>
                    setSettings(prev => ({ ...prev, aiModerationLevel: value }))
                  }
                  disabled={!isCreator}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">Light - Minimal intervention</SelectItem>
                    <SelectItem value="moderate">Moderate - Balanced approach</SelectItem>
                    <SelectItem value="strict">Strict - Active moderation</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Danger Zone */}
          {isCreator && (
            <Card className="border-destructive/50">
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2 text-destructive">
                  <AlertTriangle className="h-4 w-4" />
                  Danger Zone
                </CardTitle>
                <CardDescription>
                  Irreversible actions that affect all participants
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" className="w-full">
                      <AlertTriangle className="mr-2 h-4 w-4" />
                      End Room Permanently
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>End Room Permanently?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This action cannot be undone. All participants will be removed and the room will be permanently closed.
                        {settings.ephemeralMode && " All messages will be deleted."}
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleEndRoom}
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Ending Room...
                          </>
                        ) : (
                          'End Room'
                        )}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          {isCreator && (
            <Button onClick={handleSaveSettings} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
