/**
 * VeilMind Noise Suppression AudioWorklet Processor
 * Real-time noise cancellation with spectral subtraction and Wiener filtering
 */

class NoiseSuppressionProcessor extends AudioWorkletProcessor {
  constructor() {
    super();
    
    // Settings
    this.enabled = true;
    this.spectralSubtraction = 0.6;
    this.wienerFiltering = 0.4;
    this.gateThreshold = -40; // dB
    this.preserveVoiceQuality = true;
    
    // Processing parameters
    this.frameSize = 1024;
    this.hopSize = 512;
    this.overlapFactor = 0.5;
    
    // Buffers
    this.inputBuffer = new Float32Array(this.frameSize);
    this.outputBuffer = new Float32Array(this.frameSize);
    this.overlapBuffer = new Float32Array(this.frameSize);
    this.bufferIndex = 0;
    
    // FFT buffers (simplified - in production use a proper FFT library)
    this.fftBuffer = new Float32Array(this.frameSize);
    this.ifftBuffer = new Float32Array(this.frameSize);
    
    // Noise estimation
    this.noiseSpectrum = new Float32Array(this.frameSize / 2);
    this.signalSpectrum = new Float32Array(this.frameSize / 2);
    this.noiseFloor = new Float32Array(this.frameSize / 2);
    this.smoothingFactor = 0.98;
    
    // Voice activity detection
    this.voiceActivityThreshold = 0.1;
    this.voiceActivitySmoothing = 0.9;
    this.currentVoiceActivity = 0;
    
    // Spectral subtraction parameters
    this.alphaConstant = 2.0; // Over-subtraction factor
    this.betaConstant = 0.01; // Spectral floor
    
    // Wiener filter
    this.wienerAlpha = 0.98; // Smoothing factor for noise estimation
    this.priorSNR = new Float32Array(this.frameSize / 2);
    this.posteriorSNR = new Float32Array(this.frameSize / 2);
    
    // Window function (Hann window)
    this.window = new Float32Array(this.frameSize);
    for (let i = 0; i < this.frameSize; i++) {
      this.window[i] = 0.5 * (1 - Math.cos(2 * Math.PI * i / (this.frameSize - 1)));
    }
    
    // Initialize noise floor
    this.noiseFloor.fill(0.001);
    
    // Listen for messages
    this.port.onmessage = (event) => {
      this.handleMessage(event.data);
    };
  }
  
  handleMessage(message) {
    const { type, data } = message;
    
    switch (type) {
      case 'updateSettings':
        this.updateSettings(data);
        break;
      case 'noiseAnalysis':
        this.updateNoiseAnalysis(data);
        break;
    }
  }
  
  updateSettings(settings) {
    this.enabled = settings.enabled ?? this.enabled;
    this.spectralSubtraction = settings.spectralSubtraction ?? this.spectralSubtraction;
    this.wienerFiltering = settings.wienerFiltering ?? this.wienerFiltering;
    this.gateThreshold = settings.gateThreshold ?? this.gateThreshold;
    this.preserveVoiceQuality = settings.preserveVoiceQuality ?? this.preserveVoiceQuality;
  }
  
  updateNoiseAnalysis(analysis) {
    // Update noise floor based on external analysis
    if (analysis.noiseSpectrum && analysis.voicePresence < 0.3) {
      // Only update during non-speech periods
      for (let i = 0; i < Math.min(this.noiseFloor.length, analysis.noiseSpectrum.length / 2); i++) {
        const noiseLevel = Math.pow(10, analysis.noiseSpectrum[i] / 20);
        this.noiseFloor[i] = this.noiseFloor[i] * this.smoothingFactor + 
                            noiseLevel * (1 - this.smoothingFactor);
      }
    }
  }
  
  process(inputs, outputs, parameters) {
    const input = inputs[0];
    const output = outputs[0];
    
    if (!input || !input[0] || !output || !output[0]) {
      return true;
    }
    
    const inputChannel = input[0];
    const outputChannel = output[0];
    const frameCount = inputChannel.length;
    
    if (!this.enabled) {
      // Pass through without processing
      for (let i = 0; i < frameCount; i++) {
        outputChannel[i] = inputChannel[i];
      }
      return true;
    }
    
    // Process frame by frame
    for (let i = 0; i < frameCount; i++) {
      this.inputBuffer[this.bufferIndex] = inputChannel[i];
      this.bufferIndex++;
      
      if (this.bufferIndex >= this.frameSize) {
        // Process full frame
        this.processFrame();
        this.bufferIndex = 0;
      }
      
      // Output processed sample
      outputChannel[i] = this.outputBuffer[i] || 0;
    }
    
    return true;
  }
  
  processFrame() {
    // Apply window
    for (let i = 0; i < this.frameSize; i++) {
      this.fftBuffer[i] = this.inputBuffer[i] * this.window[i];
    }
    
    // Forward FFT (simplified - using DFT for demonstration)
    const spectrum = this.computeSpectrum(this.fftBuffer);
    
    // Detect voice activity
    const voiceActivity = this.detectVoiceActivity(spectrum);
    this.currentVoiceActivity = this.currentVoiceActivity * this.voiceActivitySmoothing + 
                               voiceActivity * (1 - this.voiceActivitySmoothing);
    
    // Update noise estimation during non-speech periods
    if (this.currentVoiceActivity < this.voiceActivityThreshold) {
      this.updateNoiseEstimation(spectrum);
    }
    
    // Apply noise suppression
    const suppressedSpectrum = this.suppressNoise(spectrum);
    
    // Inverse FFT
    const processedFrame = this.computeInverseSpectrum(suppressedSpectrum);
    
    // Apply window and overlap-add
    for (let i = 0; i < this.frameSize; i++) {
      this.outputBuffer[i] = (processedFrame[i] * this.window[i] + this.overlapBuffer[i]);
      this.overlapBuffer[i] = processedFrame[i] * this.window[i];
    }
  }
  
  computeSpectrum(timeData) {
    const spectrum = [];
    const N = timeData.length;
    
    for (let k = 0; k < N / 2; k++) {
      let real = 0;
      let imag = 0;
      
      for (let n = 0; n < N; n++) {
        const angle = -2 * Math.PI * k * n / N;
        real += timeData[n] * Math.cos(angle);
        imag += timeData[n] * Math.sin(angle);
      }
      
      spectrum.push({
        real: real,
        imag: imag,
        magnitude: Math.sqrt(real * real + imag * imag),
        phase: Math.atan2(imag, real)
      });
    }
    
    return spectrum;
  }
  
  computeInverseSpectrum(spectrum) {
    const N = spectrum.length * 2;
    const timeData = new Float32Array(N);
    
    for (let n = 0; n < N; n++) {
      let real = 0;
      
      for (let k = 0; k < spectrum.length; k++) {
        const angle = 2 * Math.PI * k * n / N;
        real += spectrum[k].magnitude * Math.cos(spectrum[k].phase + angle);
      }
      
      timeData[n] = real / N;
    }
    
    return timeData;
  }
  
  detectVoiceActivity(spectrum) {
    // Voice activity detection based on spectral characteristics
    let voiceEnergy = 0;
    let totalEnergy = 0;
    
    // Voice frequency range (approximately 80Hz - 4kHz)
    const voiceStartBin = Math.floor(80 * spectrum.length / (sampleRate / 2));
    const voiceEndBin = Math.floor(4000 * spectrum.length / (sampleRate / 2));
    
    for (let i = 0; i < spectrum.length; i++) {
      const energy = spectrum[i].magnitude * spectrum[i].magnitude;
      totalEnergy += energy;
      
      if (i >= voiceStartBin && i <= voiceEndBin) {
        voiceEnergy += energy;
      }
    }
    
    const voiceRatio = totalEnergy > 0 ? voiceEnergy / totalEnergy : 0;
    
    // Check for harmonic structure (voice characteristic)
    let harmonicScore = 0;
    const fundamentalBin = Math.floor(150 * spectrum.length / (sampleRate / 2)); // ~150Hz
    
    for (let h = 1; h <= 5; h++) {
      const harmonicBin = fundamentalBin * h;
      if (harmonicBin < spectrum.length) {
        harmonicScore += spectrum[harmonicBin].magnitude;
      }
    }
    
    return Math.min(1, voiceRatio * 2 + harmonicScore * 0.1);
  }
  
  updateNoiseEstimation(spectrum) {
    for (let i = 0; i < spectrum.length; i++) {
      const magnitude = spectrum[i].magnitude;
      this.noiseSpectrum[i] = this.noiseSpectrum[i] * this.smoothingFactor + 
                             magnitude * (1 - this.smoothingFactor);
    }
  }
  
  suppressNoise(spectrum) {
    const suppressedSpectrum = [];
    
    for (let i = 0; i < spectrum.length; i++) {
      const signalMagnitude = spectrum[i].magnitude;
      const noiseMagnitude = Math.max(this.noiseSpectrum[i], this.noiseFloor[i]);
      
      let suppressedMagnitude = signalMagnitude;
      
      // Spectral Subtraction
      if (this.spectralSubtraction > 0) {
        const subtractedMagnitude = signalMagnitude - 
          this.alphaConstant * noiseMagnitude * this.spectralSubtraction;
        const flooredMagnitude = Math.max(subtractedMagnitude, 
          this.betaConstant * signalMagnitude);
        
        suppressedMagnitude = suppressedMagnitude * (1 - this.spectralSubtraction) + 
                             flooredMagnitude * this.spectralSubtraction;
      }
      
      // Wiener Filtering
      if (this.wienerFiltering > 0) {
        // Estimate SNR
        const posteriorSNR = Math.max(0.001, 
          (signalMagnitude * signalMagnitude) / (noiseMagnitude * noiseMagnitude));
        
        // Update prior SNR
        this.priorSNR[i] = this.wienerAlpha * Math.max(posteriorSNR - 1, 0.1 * this.priorSNR[i]) + 
                          (1 - this.wienerAlpha) * this.priorSNR[i];
        
        // Wiener gain
        const wienerGain = this.priorSNR[i] / (this.priorSNR[i] + 1);
        const wienerMagnitude = signalMagnitude * wienerGain;
        
        suppressedMagnitude = suppressedMagnitude * (1 - this.wienerFiltering) + 
                             wienerMagnitude * this.wienerFiltering;
      }
      
      // Preserve voice quality by limiting suppression in voice frequencies
      if (this.preserveVoiceQuality && this.currentVoiceActivity > 0.3) {
        const voiceStartBin = Math.floor(80 * spectrum.length / (sampleRate / 2));
        const voiceEndBin = Math.floor(4000 * spectrum.length / (sampleRate / 2));
        
        if (i >= voiceStartBin && i <= voiceEndBin) {
          const preservationFactor = 0.7; // Preserve 70% of original in voice range
          suppressedMagnitude = suppressedMagnitude * (1 - preservationFactor) + 
                               signalMagnitude * preservationFactor;
        }
      }
      
      // Apply noise gate
      const gateThresholdLinear = Math.pow(10, this.gateThreshold / 20);
      if (suppressedMagnitude < gateThresholdLinear) {
        suppressedMagnitude *= 0.1; // Reduce by 20dB
      }
      
      suppressedSpectrum.push({
        real: suppressedMagnitude * Math.cos(spectrum[i].phase),
        imag: suppressedMagnitude * Math.sin(spectrum[i].phase),
        magnitude: suppressedMagnitude,
        phase: spectrum[i].phase
      });
    }
    
    return suppressedSpectrum;
  }
}

registerProcessor('noise-suppression', NoiseSuppressionProcessor);
