"use client";

import React from "react";
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";

interface SectionHeaderProps {
  badgeText?: string;
  badgeIcon?: LucideIcon;
  title: string;
  highlightedTitle?: string;
  description?: string;
  className?: string;
  centered?: boolean;
  size?: "sm" | "md" | "lg";
}

const sizeClasses = {
  sm: {
    title: "text-2xl md:text-3xl",
    description: "text-base md:text-lg",
  },
  md: {
    title: "text-3xl md:text-4xl lg:text-5xl",
    description: "text-lg md:text-xl",
  },
  lg: {
    title: "text-4xl md:text-5xl lg:text-6xl",
    description: "text-xl md:text-2xl",
  },
};

export function SectionHeader({
  badgeText,
  badgeIcon: BadgeIcon,
  title,
  highlightedTitle,
  description,
  className,
  centered = true,
  size = "md",
}: SectionHeaderProps) {
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <motion.div
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      variants={containerVariants}
      className={cn(
        "mb-12 md:mb-16",
        centered && "text-center",
        className
      )}
    >
      {badgeText && (
        <div className={cn("mb-4", centered && "flex justify-center")}>
          <Badge 
            variant="outline" 
            className="border-primary/30 text-primary bg-primary/5 hover:bg-primary/10 transition-colors"
          >
            {BadgeIcon && <BadgeIcon className="w-4 h-4 mr-2" />}
            <span>{badgeText}</span>
          </Badge>
        </div>
      )}
      
      <h2 className={cn("font-bold mb-6", sizeClasses[size].title)}>
        {highlightedTitle && (
          <span className="veil-gradient-text block">
            {highlightedTitle}
          </span>
        )}
        <span className={highlightedTitle ? "block" : ""}>{title}</span>
      </h2>
      
      {description && (
        <p className={cn(
          "text-muted-foreground leading-relaxed",
          sizeClasses[size].description,
          centered && "max-w-3xl mx-auto"
        )}>
          {description}
        </p>
      )}
    </motion.div>
  );
}
