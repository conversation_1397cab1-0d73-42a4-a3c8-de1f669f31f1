'use client';

import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Mic, 
  MicOff, 
  Phone, 
  PhoneOff, 
  Users, 
  Volume2,
  Loader2,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useVoiceRoom } from '@/hooks/useVoiceRoom';
import { Id } from '@/convex/_generated/dataModel';
import { toast } from 'sonner';

interface VoiceRoomTestProps {
  roomId: Id<'rooms'>;
  className?: string;
}

export function VoiceRoomTest({ roomId, className }: VoiceRoomTestProps) {
  const {
    voiceState,
    participants,
    error,
    joinVoiceRoom,
    leaveVoiceRoom,
    toggleMute,
    enableVoiceForRoom,
    isVoiceEnabled,
    isVoiceActive,
    canJoinVoice,
    clearError,
  } = useVoiceRoom(roomId);

  const handleJoinVoice = async () => {
    try {
      await joinVoiceRoom();
      toast.success('Joined voice room successfully!');
    } catch (err) {
      console.error('Failed to join voice room:', err);
      toast.error('Failed to join voice room');
    }
  };

  const handleLeaveVoice = async () => {
    try {
      await leaveVoiceRoom();
      toast.success('Left voice room');
    } catch (err) {
      console.error('Failed to leave voice room:', err);
      toast.error('Failed to leave voice room');
    }
  };

  const handleToggleMute = async () => {
    try {
      await toggleMute();
      toast.success(voiceState.isMuted ? 'Unmuted' : 'Muted');
    } catch (err) {
      console.error('Failed to toggle mute:', err);
      toast.error('Failed to toggle mute');
    }
  };

  const handleEnableVoice = async () => {
    try {
      await enableVoiceForRoom();
      toast.success('Voice enabled for room');
    } catch (err) {
      console.error('Failed to enable voice:', err);
      toast.error('Failed to enable voice');
    }
  };

  return (
    <Card className={cn("border-border/50 bg-card/50 backdrop-blur-sm", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <Volume2 className="h-4 w-4" />
            Voice Room Test
            {isVoiceActive && (
              <Badge variant="secondary" className="text-xs">
                Active
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Users className="h-3 w-3" />
            <span className="text-xs">{voiceState.participantCount}</span>
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Error Display */}
        {error && (
          <div className="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
            <AlertCircle className="h-4 w-4 text-destructive" />
            <span className="text-sm text-destructive">{error}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="ml-auto h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
        )}

        {/* Status Display */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>Voice Enabled:</span>
            <div className="flex items-center gap-1">
              {isVoiceEnabled ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <AlertCircle className="h-4 w-4 text-yellow-500" />
              )}
              <span>{isVoiceEnabled ? 'Yes' : 'No'}</span>
            </div>
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <span>Connection:</span>
            <div className="flex items-center gap-1">
              {voiceState.isConnected ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : voiceState.isConnecting ? (
                <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
              ) : (
                <AlertCircle className="h-4 w-4 text-gray-500" />
              )}
              <span>
                {voiceState.isConnected ? 'Connected' : 
                 voiceState.isConnecting ? 'Connecting...' : 'Disconnected'}
              </span>
            </div>
          </div>

          {voiceState.connectionQuality && (
            <div className="flex items-center justify-between text-sm">
              <span>Quality:</span>
              <div className="flex items-center gap-1">
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  voiceState.connectionQuality === 'excellent' && "bg-green-500",
                  voiceState.connectionQuality === 'good' && "bg-yellow-500",
                  voiceState.connectionQuality === 'poor' && "bg-red-500"
                )} />
                <span className="capitalize">{voiceState.connectionQuality}</span>
              </div>
            </div>
          )}
        </div>

        {/* Participants List */}
        {participants.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Participants ({participants.length})</h4>
            <div className="space-y-1">
              {participants.map((participant) => (
                <div
                  key={participant.sessionId}
                  className="flex items-center justify-between p-2 bg-muted/50 rounded-md text-sm"
                >
                  <span className="font-medium">{participant.anonymousAlias}</span>
                  <div className="flex items-center gap-2">
                    {participant.isSpeaking && (
                      <Badge variant="secondary" className="text-xs">
                        Speaking
                      </Badge>
                    )}
                    {participant.isMuted ? (
                      <MicOff className="h-3 w-3 text-muted-foreground" />
                    ) : (
                      <Mic className="h-3 w-3 text-green-500" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Controls */}
        <div className="space-y-2">
          {!isVoiceEnabled ? (
            <Button
              onClick={handleEnableVoice}
              className="w-full"
              size="sm"
            >
              <Volume2 className="h-4 w-4 mr-2" />
              Enable Voice for Room
            </Button>
          ) : !voiceState.isConnected ? (
            <Button
              onClick={handleJoinVoice}
              disabled={!canJoinVoice || voiceState.isConnecting}
              className="w-full"
              size="sm"
            >
              {voiceState.isConnecting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Connecting...
                </>
              ) : (
                <>
                  <Phone className="h-4 w-4 mr-2" />
                  Join Voice Chat
                </>
              )}
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button
                onClick={handleToggleMute}
                variant={voiceState.isMuted ? "outline" : "default"}
                size="sm"
                className="flex-1"
              >
                {voiceState.isMuted ? (
                  <>
                    <MicOff className="h-4 w-4 mr-2" />
                    Unmute
                  </>
                ) : (
                  <>
                    <Mic className="h-4 w-4 mr-2" />
                    Mute
                  </>
                )}
              </Button>
              <Button
                onClick={handleLeaveVoice}
                variant="destructive"
                size="sm"
                className="flex-1"
              >
                <PhoneOff className="h-4 w-4 mr-2" />
                Leave
              </Button>
            </div>
          )}
        </div>

        {/* Debug Info */}
        <details className="text-xs">
          <summary className="cursor-pointer text-muted-foreground">Debug Info</summary>
          <pre className="mt-2 p-2 bg-muted/50 rounded text-xs overflow-auto">
            {JSON.stringify({
              isVoiceEnabled,
              isVoiceActive,
              canJoinVoice,
              voiceState,
              participantCount: participants.length,
            }, null, 2)}
          </pre>
        </details>
      </CardContent>
    </Card>
  );
}
