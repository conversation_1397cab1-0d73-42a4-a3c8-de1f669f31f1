// Viewport and responsive design hook
// Production-ready hook for responsive design and viewport management

'use client';

import { useState, useEffect, useCallback } from 'react';
import { ViewportState } from '@/lib/types';

// ============================================================================
// BREAKPOINT CONSTANTS
// ============================================================================

export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

export type Breakpoint = keyof typeof BREAKPOINTS;

// ============================================================================
// VIEWPORT HOOK
// ============================================================================

export function useViewport() {
  const [viewportState, setViewportState] = useState<ViewportState>(() => {
    // Safe initial state for SSR
    if (typeof window === 'undefined') {
      return {
        width: 1024,
        height: 768,
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        orientation: 'landscape',
      };
    }

    const width = window.innerWidth;
    const height = window.innerHeight;

    return {
      width,
      height,
      isMobile: width < BREAKPOINTS.md,
      isTablet: width >= BREAKPOINTS.md && width < BREAKPOINTS.lg,
      isDesktop: width >= BREAKPOINTS.lg,
      orientation: width > height ? 'landscape' : 'portrait',
    };
  });

  const updateViewport = useCallback(() => {
    const width = window.innerWidth;
    const height = window.innerHeight;

    setViewportState({
      width,
      height,
      isMobile: width < BREAKPOINTS.md,
      isTablet: width >= BREAKPOINTS.md && width < BREAKPOINTS.lg,
      isDesktop: width >= BREAKPOINTS.lg,
      orientation: width > height ? 'landscape' : 'portrait',
    });
  }, []);

  useEffect(() => {
    // Update on mount to get correct initial values
    updateViewport();

    // Debounced resize handler
    let timeoutId: NodeJS.Timeout;
    const debouncedUpdate = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(updateViewport, 100);
    };

    window.addEventListener('resize', debouncedUpdate);
    window.addEventListener('orientationchange', debouncedUpdate);

    return () => {
      window.removeEventListener('resize', debouncedUpdate);
      window.removeEventListener('orientationchange', debouncedUpdate);
      clearTimeout(timeoutId);
    };
  }, [updateViewport]);

  return viewportState;
}

// ============================================================================
// BREAKPOINT HOOKS
// ============================================================================

/**
 * Hook to check if viewport matches a specific breakpoint
 */
export function useBreakpoint(breakpoint: Breakpoint): boolean {
  const { width } = useViewport();
  return width >= BREAKPOINTS[breakpoint];
}

/**
 * Hook to get current breakpoint
 */
export function useCurrentBreakpoint(): Breakpoint | null {
  const { width } = useViewport();

  if (width >= BREAKPOINTS['2xl']) return '2xl';
  if (width >= BREAKPOINTS.xl) return 'xl';
  if (width >= BREAKPOINTS.lg) return 'lg';
  if (width >= BREAKPOINTS.md) return 'md';
  if (width >= BREAKPOINTS.sm) return 'sm';
  
  return null;
}

/**
 * Hook for responsive values based on breakpoints
 */
export function useResponsiveValue<T>(values: Partial<Record<Breakpoint | 'base', T>>): T {
  const currentBreakpoint = useCurrentBreakpoint();
  const { width } = useViewport();

  // Find the appropriate value based on current breakpoint
  const breakpointOrder: Array<Breakpoint | 'base'> = ['2xl', 'xl', 'lg', 'md', 'sm', 'base'];
  
  for (const bp of breakpointOrder) {
    if (bp === 'base') {
      return values.base as T;
    }
    
    if (width >= BREAKPOINTS[bp] && values[bp] !== undefined) {
      return values[bp] as T;
    }
  }

  // Fallback to base value or first available value
  return values.base ?? Object.values(values)[0] as T;
}

// ============================================================================
// ORIENTATION HOOKS
// ============================================================================

/**
 * Hook to detect orientation changes
 */
export function useOrientation() {
  const { orientation } = useViewport();
  const [previousOrientation, setPreviousOrientation] = useState(orientation);

  useEffect(() => {
    if (orientation !== previousOrientation) {
      setPreviousOrientation(orientation);
    }
  }, [orientation, previousOrientation]);

  return {
    orientation,
    isPortrait: orientation === 'portrait',
    isLandscape: orientation === 'landscape',
    hasChanged: orientation !== previousOrientation,
  };
}

// ============================================================================
// SAFE AREA HOOKS
// ============================================================================

/**
 * Hook to get safe area insets (for mobile devices with notches, etc.)
 */
export function useSafeArea() {
  const [safeArea, setSafeArea] = useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  });

  useEffect(() => {
    const updateSafeArea = () => {
      const computedStyle = getComputedStyle(document.documentElement);
      
      setSafeArea({
        top: parseInt(computedStyle.getPropertyValue('--safe-area-inset-top') || '0'),
        right: parseInt(computedStyle.getPropertyValue('--safe-area-inset-right') || '0'),
        bottom: parseInt(computedStyle.getPropertyValue('--safe-area-inset-bottom') || '0'),
        left: parseInt(computedStyle.getPropertyValue('--safe-area-inset-left') || '0'),
      });
    };

    updateSafeArea();
    window.addEventListener('resize', updateSafeArea);
    window.addEventListener('orientationchange', updateSafeArea);

    return () => {
      window.removeEventListener('resize', updateSafeArea);
      window.removeEventListener('orientationchange', updateSafeArea);
    };
  }, []);

  return safeArea;
}

// ============================================================================
// SCROLL HOOKS
// ============================================================================

/**
 * Hook to track scroll position and direction
 */
export function useScrollPosition() {
  const [scrollState, setScrollState] = useState({
    x: 0,
    y: 0,
    direction: 'up' as 'up' | 'down' | 'left' | 'right' | null,
  });

  useEffect(() => {
    let previousY = window.scrollY;
    let previousX = window.scrollX;

    const updateScrollPosition = () => {
      const currentY = window.scrollY;
      const currentX = window.scrollX;
      
      let direction: 'up' | 'down' | 'left' | 'right' | null = null;
      
      if (currentY > previousY) direction = 'down';
      else if (currentY < previousY) direction = 'up';
      else if (currentX > previousX) direction = 'right';
      else if (currentX < previousX) direction = 'left';

      setScrollState({
        x: currentX,
        y: currentY,
        direction,
      });

      previousY = currentY;
      previousX = currentX;
    };

    // Throttled scroll handler
    let ticking = false;
    const throttledUpdate = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          updateScrollPosition();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledUpdate, { passive: true });

    return () => {
      window.removeEventListener('scroll', throttledUpdate);
    };
  }, []);

  return scrollState;
}

// ============================================================================
// VISIBILITY HOOKS
// ============================================================================

/**
 * Hook to detect if page is visible (tab focus)
 */
export function usePageVisibility() {
  const [isVisible, setIsVisible] = useState(() => {
    if (typeof document === 'undefined') return true;
    return !document.hidden;
  });

  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsVisible(!document.hidden);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  return isVisible;
}

/**
 * Hook to detect if user is online/offline
 */
export function useOnlineStatus() {
  const [isOnline, setIsOnline] = useState(() => {
    if (typeof navigator === 'undefined') return true;
    return navigator.onLine;
  });

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return isOnline;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get responsive class names based on breakpoints
 */
export function getResponsiveClasses(
  classes: Partial<Record<Breakpoint | 'base', string>>,
  currentBreakpoint: Breakpoint | null
): string {
  const classNames: string[] = [];

  if (classes.base) {
    classNames.push(classes.base);
  }

  const breakpointOrder: Breakpoint[] = ['sm', 'md', 'lg', 'xl', '2xl'];
  
  for (const bp of breakpointOrder) {
    if (currentBreakpoint && BREAKPOINTS[bp] <= BREAKPOINTS[currentBreakpoint] && classes[bp]) {
      classNames.push(classes[bp]!);
    }
  }

  return classNames.join(' ');
}
