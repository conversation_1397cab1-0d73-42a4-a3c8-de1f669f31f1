"use client";

import React from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Shield, 
  Brain, 
  Zap, 
  Eye,
  UserX,
  Lock,
  Sparkles,
  Clock
} from "lucide-react";

interface Feature {
  icon: React.ElementType;
  title: string;
  description: string;
  details: string[];
  badge: string;
  color: string;
}

export const FeaturesSection: React.FC = () => {
  const features: Feature[] = [
    {
      icon: Shield,
      title: "Complete Anonymity",
      description: "No registration or tracking. No digital footprints left behind.",
      details: [
        "No registration or tracking",
        "No digital footprints",
        "AI-generated identities",
        "Zero personal data collection"
      ],
      badge: "Privacy First",
      color: "text-blue-600 dark:text-blue-400"
    },
    {
      icon: Brain,
      title: "AI-Powered Moderation",
      description: "Real-time conversation monitoring keeps discussions productive and respectful.",
      details: [
        "Real-time toxicity detection",
        "Bias prevention algorithms",
        "Context-aware responses",
        "Automatic escalation management"
      ],
      badge: "AI Enhanced",
      color: "text-purple-600 dark:text-purple-400"
    },
    {
      icon: Zap,
      title: "Instant Room Creation",
      description: "Create secure meeting rooms in seconds with no setup complexity.",
      details: [
        "One-click room generation",
        "No configuration needed",
        "Instant secure links",
        "Ready in under 10 seconds"
      ],
      badge: "One-Click",
      color: "text-yellow-600 dark:text-yellow-400"
    }
  ];

  const additionalFeatures = [
    {
      icon: Eye,
      title: "Voice Modulation",
      description: "Advanced voice processing for complete audio anonymity"
    },
    {
      icon: UserX,
      title: "Identity Protection",
      description: "AI-generated personas protect your real identity"
    },
    {
      icon: Lock,
      title: "End-to-End Encryption",
      description: "Military-grade security for all communications"
    },
    {
      icon: Sparkles,
      title: "Smart Facilitation",
      description: "AI suggests topics and manages conversation flow"
    },
    {
      icon: Clock,
      title: "Auto-Cleanup",
      description: "All data automatically deleted after sessions"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <section id="features" className="py-24 bg-background">
      <div className="container mx-auto px-4">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="max-w-6xl mx-auto"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
              Powerful Features for
              <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                {" "}Anonymous Conversations
              </span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Advanced technology meets simple design. Everything you need for secure, 
              anonymous conversations without the complexity.
            </p>
          </motion.div>

          {/* Main Features - Less boxy design */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 lg:grid-cols-3 gap-12 mb-16"
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="group relative"
                whileHover={{ y: -8 }}
              >
                <div className="relative p-8 rounded-3xl bg-gradient-to-br from-background/80 to-muted/20 backdrop-blur-sm border border-muted/30 hover:border-primary/30 transition-all duration-500 hover:shadow-2xl hover:shadow-primary/10">
                  {/* Floating icon */}
                  <div className="absolute -top-6 left-8">
                    <div className="p-4 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20 shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                      <feature.icon className={`h-8 w-8 ${feature.color}`} />
                    </div>
                  </div>

                  <div className="pt-8">
                    <div className="inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary text-xs font-medium mb-4">
                      {feature.badge}
                    </div>

                    <h3 className="text-2xl font-bold mb-4">{feature.title}</h3>
                    <p className="text-muted-foreground mb-6 leading-relaxed">
                      {feature.description}
                    </p>

                    <ul className="space-y-3">
                      {feature.details.map((detail, detailIndex) => (
                        <li key={detailIndex} className="flex items-start gap-3 text-sm">
                          <div className="w-2 h-2 bg-gradient-to-r from-primary to-primary/70 rounded-full mt-2 flex-shrink-0" />
                          <span className="leading-relaxed">{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Additional Features Grid - Cleaner design */}
          <motion.div variants={itemVariants} className="mb-12">
            <h3 className="text-2xl font-bold text-center mb-8">
              Additional Security & Convenience Features
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              {additionalFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  className="text-center group hover:scale-105 transition-transform duration-300"
                  whileHover={{ y: -4 }}
                >
                  <div className="p-4 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 w-fit mx-auto mb-4 group-hover:from-primary/20 group-hover:to-primary/10 transition-all duration-300 shadow-md group-hover:shadow-lg">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                  <h4 className="font-semibold mb-2 text-sm">{feature.title}</h4>
                  <p className="text-xs text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Feature Highlight - More organic design */}
          <motion.div
            variants={itemVariants}
            className="text-center relative overflow-hidden"
          >
            {/* Background gradient */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 rounded-3xl" />
            <div className="absolute inset-0 bg-gradient-to-br from-transparent via-background/50 to-transparent rounded-3xl" />

            <div className="relative max-w-3xl mx-auto p-12">
              <h3 className="text-3xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                Why Choose VeilMeet?
              </h3>
              <p className="text-muted-foreground mb-8 text-lg leading-relaxed">
                Unlike other platforms that collect your data or require complex setup,
                VeilMeet prioritizes your privacy while providing enterprise-grade features.
                Our AI doesn't just moderate—it enhances conversations while keeping you completely anonymous.
              </p>
              <div className="flex flex-wrap justify-center gap-3">
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 text-primary font-medium text-sm border border-primary/20">
                  Zero Data Collection
                </div>
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-secondary/10 to-secondary/5 text-secondary font-medium text-sm border border-secondary/20">
                  Enterprise Security
                </div>
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 text-primary font-medium text-sm border border-primary/20">
                  AI-Enhanced Experience
                </div>
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-secondary/10 to-secondary/5 text-secondary font-medium text-sm border border-secondary/20">
                  Instant Setup
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};
