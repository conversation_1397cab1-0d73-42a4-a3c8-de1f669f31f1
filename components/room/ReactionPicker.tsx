'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Plus, Smile } from 'lucide-react';
import { cn } from '@/lib/utils';

const REACTION_CATEGORIES = {
  'Frequently Used': ['👍', '❤️', '😊', '😮', '😢', '😡'],
  'Smileys & Emotion': [
    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
    '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
    '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
    '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
    '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬'
  ],
  'Gestures': [
    '👍', '👎', '👌', '🤌', '🤏', '✌️', '🤞', '🤟', '🤘', '🤙',
    '👈', '👉', '👆', '🖕', '👇', '☝️', '👏', '🙌', '👐', '🤲',
    '🤝', '🙏', '✊', '👊', '🤛', '🤜', '👋', '🤚', '🖐️', '✋'
  ],
  'Hearts': [
    '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
    '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟'
  ],
  'Objects & Symbols': [
    '💯', '💢', '💥', '💫', '💦', '💨', '🕳️', '💣', '💬', '👁️‍🗨️',
    '🗨️', '🗯️', '💭', '💤', '🔥', '⭐', '🌟', '✨', '⚡', '☄️'
  ]
};

interface ReactionPickerProps {
  onReactionSelect: (emoji: string) => void;
  trigger?: React.ReactNode;
  disabled?: boolean;
  className?: string;
}

export function ReactionPicker({
  onReactionSelect,
  trigger,
  disabled = false,
  className
}: ReactionPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('Frequently Used');

  const handleReactionSelect = (emoji: string) => {
    onReactionSelect(emoji);
    setIsOpen(false);
  };

  const defaultTrigger = (
    <Button
      variant="ghost"
      size="sm"
      className={cn(
        "h-6 w-6 p-0 rounded-full hover:bg-muted transition-colors",
        className
      )}
      disabled={disabled}
    >
      <Plus className="h-3 w-3" />
    </Button>
  );

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        {trigger || defaultTrigger}
      </PopoverTrigger>
      
      <PopoverContent className="w-80 p-0" align="start">
        <div className="border-b border-border">
          <div className="flex items-center gap-1 p-2 overflow-x-auto scrollbar-thin">
            {Object.keys(REACTION_CATEGORIES).map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "secondary" : "ghost"}
                size="sm"
                className="text-xs whitespace-nowrap shrink-0"
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </Button>
            ))}
          </div>
        </div>

        <div className="p-3 max-h-64 overflow-y-auto scrollbar-thin">
          <div className="grid grid-cols-8 gap-1">
            {REACTION_CATEGORIES[selectedCategory as keyof typeof REACTION_CATEGORIES]?.map((emoji) => (
              <TooltipProvider key={emoji}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 hover:bg-muted transition-colors text-lg"
                      onClick={() => handleReactionSelect(emoji)}
                    >
                      {emoji}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">React with {emoji}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ))}
          </div>
        </div>

        <div className="border-t border-border p-2">
          <div className="text-xs text-muted-foreground text-center">
            Click an emoji to react
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

// Quick reaction bar component
export function QuickReactionBar({
  onReactionSelect,
  className
}: {
  onReactionSelect: (emoji: string) => void;
  className?: string;
}) {
  const quickReactions = ['👍', '❤️', '😊', '😮', '😢', '😡'];

  return (
    <div className={cn(
      "flex items-center gap-0.5 bg-background border border-border rounded-full px-1 py-0.5 shadow-md",
      className
    )}>
      {quickReactions.map((emoji) => (
        <TooltipProvider key={emoji}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 rounded-full hover:bg-muted transition-colors text-xs"
                onClick={() => onReactionSelect(emoji)}
              >
                {emoji}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs">React with {emoji}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ))}
      
      <ReactionPicker
        onReactionSelect={onReactionSelect}
        trigger={
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 rounded-full hover:bg-muted transition-colors"
          >
            <Smile className="h-3 w-3" />
          </Button>
        }
      />
    </div>
  );
}

// Enhanced reaction display with participant details
export function ReactionDisplay({
  reactions,
  currentParticipantId,
  onReactionToggle,
  className
}: {
  reactions: Array<{
    emoji: string;
    count: number;
    participantSessionIds: string[];
    participantDetails?: Array<{
      sessionId: string;
      anonymousAlias: string;
      reactedAt: number;
    }>;
  }>;
  currentParticipantId?: string;
  onReactionToggle: (emoji: string) => void;
  className?: string;
}) {
  if (reactions.length === 0) return null;

  return (
    <div className={cn("flex flex-wrap gap-1", className)}>
      {reactions.map((reaction) => {
        const hasReacted = reaction.participantSessionIds.includes(currentParticipantId || '');
        const participantNames = reaction.participantDetails
          ?.slice(0, 5)
          .map(p => p.anonymousAlias)
          .join(', ');
        const extraCount = reaction.count > 5 ? reaction.count - 5 : 0;
        const tooltipText = participantNames + (extraCount > 0 ? ` and ${extraCount} others` : '');

        return (
          <TooltipProvider key={reaction.emoji}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs transition-colors",
                    hasReacted 
                      ? "bg-blue-100 dark:bg-blue-900/30 border border-blue-300 dark:border-blue-700 text-blue-700 dark:text-blue-300"
                      : "bg-muted/50 hover:bg-muted border border-border/50 text-muted-foreground hover:text-foreground"
                  )}
                  onClick={() => onReactionToggle(reaction.emoji)}
                >
                  <span>{reaction.emoji}</span>
                  <span className={hasReacted ? "font-medium" : ""}>{reaction.count}</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs max-w-48">{tooltipText}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      })}
    </div>
  );
}

// Mobile-optimized reaction picker
export function MobileReactionPicker({
  onReactionSelect,
  className
}: {
  onReactionSelect: (emoji: string) => void;
  className?: string;
}) {
  const mobileReactions = [
    '👍', '❤️', '😊', '😮', '😢', '😡', '🔥', '💯', '👏', '🙌',
    '😍', '🤔', '😴', '🎉', '💪', '🙏', '👀', '🤯', '😎', '🤗'
  ];

  return (
    <div className={cn(
      "grid grid-cols-6 gap-2 p-2 border-b border-border",
      className
    )}>
      {mobileReactions.map((emoji) => (
        <Button
          key={emoji}
          variant="ghost"
          size="sm"
          className="h-10 w-10 p-0 hover:bg-muted transition-colors text-lg touch-manipulation"
          onClick={() => onReactionSelect(emoji)}
        >
          {emoji}
        </Button>
      ))}
    </div>
  );
}
