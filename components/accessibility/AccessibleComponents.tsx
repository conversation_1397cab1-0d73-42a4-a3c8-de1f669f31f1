// Accessible UI components with WCAG 2.1 AA compliance
// Production-ready accessibility features and components

'use client';

import React, { useRef, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { 
  generateAriaId, 
  handleActivation<PERSON><PERSON>s, 
  trapFocus, 
  createFocusRestorer,
  announceToScreenReader,
  KEYBOARD_KEYS 
} from '@/lib/utils/accessibility';

// ============================================================================
// ACCESSIBLE BUTTON
// ============================================================================

interface AccessibleButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  isLoading?: boolean;
  loadingText?: string;
  describedBy?: string;
  announcement?: string;
}

export function AccessibleButton({
  children,
  isLoading = false,
  loadingText,
  describedBy,
  announcement,
  onClick,
  onKeyDown,
  className,
  disabled,
  ...props
}: AccessibleButtonProps) {
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (announcement) {
      announceToScreenReader(announcement);
    }
    onClick?.(event);
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLButtonElement>) => {
    handleActivationKeys(event, () => {
      if (announcement) {
        announceToScreenReader(announcement);
      }
    }, false);
    onKeyDown?.(event);
  };

  return (
    <Button
      {...props}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      disabled={disabled || isLoading}
      aria-disabled={disabled || isLoading}
      aria-describedby={describedBy}
      aria-busy={isLoading}
      className={className}
    >
      {isLoading ? (
        <>
          <span className="sr-only">{loadingText || 'Loading'}</span>
          {children}
        </>
      ) : (
        children
      )}
    </Button>
  );
}

// ============================================================================
// ACCESSIBLE MODAL
// ============================================================================

interface AccessibleModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title: string;
  description?: string;
  className?: string;
  closeOnEscape?: boolean;
  closeOnOverlayClick?: boolean;
  initialFocus?: React.RefObject<HTMLElement>;
}

export function AccessibleModal({
  isOpen,
  onClose,
  children,
  title,
  description,
  className,
  closeOnEscape = true,
  closeOnOverlayClick = true,
  initialFocus,
}: AccessibleModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);
  const titleId = generateAriaId('modal-title');
  const descriptionId = description ? generateAriaId('modal-description') : undefined;
  const focusRestorer = useRef(createFocusRestorer());

  useEffect(() => {
    if (isOpen) {
      focusRestorer.current.save();
      
      const cleanup = trapFocus(modalRef, true);
      
      // Focus initial element or first focusable element
      setTimeout(() => {
        if (initialFocus?.current) {
          initialFocus.current.focus();
        } else {
          const firstFocusable = modalRef.current?.querySelector(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
          ) as HTMLElement;
          firstFocusable?.focus();
        }
      }, 100);

      return cleanup;
    } else {
      focusRestorer.current.restore();
    }
  }, [isOpen, initialFocus]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === KEYBOARD_KEYS.ESCAPE && closeOnEscape) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = '';
    };
  }, [isOpen, closeOnEscape, onClose]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center"
      role="dialog"
      aria-modal="true"
      aria-labelledby={titleId}
      aria-describedby={descriptionId}
    >
      {/* Overlay */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={closeOnOverlayClick ? onClose : undefined}
        aria-hidden="true"
      />
      
      {/* Modal Content */}
      <div
        ref={modalRef}
        className={cn(
          'relative bg-background border rounded-lg shadow-lg max-w-lg w-full mx-4 max-h-[90vh] overflow-auto',
          className
        )}
      >
        <div className="p-6">
          <h2 id={titleId} className="text-lg font-semibold mb-2">
            {title}
          </h2>
          
          {description && (
            <p id={descriptionId} className="text-muted-foreground mb-4">
              {description}
            </p>
          )}
          
          {children}
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// ACCESSIBLE TABS
// ============================================================================

interface Tab {
  id: string;
  label: string;
  content: React.ReactNode;
  disabled?: boolean;
}

interface AccessibleTabsProps {
  tabs: Tab[];
  defaultTab?: string;
  onTabChange?: (tabId: string) => void;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
}

export function AccessibleTabs({
  tabs,
  defaultTab,
  onTabChange,
  className,
  orientation = 'horizontal',
}: AccessibleTabsProps) {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id);
  const tabListRef = useRef<HTMLDivElement>(null);
  const [focusedTab, setFocusedTab] = useState(activeTab);

  const handleTabClick = (tabId: string) => {
    if (tabs.find(tab => tab.id === tabId)?.disabled) return;
    
    setActiveTab(tabId);
    setFocusedTab(tabId);
    onTabChange?.(tabId);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    const enabledTabs = tabs.filter(tab => !tab.disabled);
    const currentIndex = enabledTabs.findIndex(tab => tab.id === focusedTab);
    
    let newIndex = currentIndex;
    
    if (orientation === 'horizontal') {
      if (event.key === KEYBOARD_KEYS.ARROW_LEFT) {
        newIndex = currentIndex > 0 ? currentIndex - 1 : enabledTabs.length - 1;
      } else if (event.key === KEYBOARD_KEYS.ARROW_RIGHT) {
        newIndex = currentIndex < enabledTabs.length - 1 ? currentIndex + 1 : 0;
      }
    } else {
      if (event.key === KEYBOARD_KEYS.ARROW_UP) {
        newIndex = currentIndex > 0 ? currentIndex - 1 : enabledTabs.length - 1;
      } else if (event.key === KEYBOARD_KEYS.ARROW_DOWN) {
        newIndex = currentIndex < enabledTabs.length - 1 ? currentIndex + 1 : 0;
      }
    }
    
    if (event.key === KEYBOARD_KEYS.HOME) {
      newIndex = 0;
    } else if (event.key === KEYBOARD_KEYS.END) {
      newIndex = enabledTabs.length - 1;
    }
    
    if (newIndex !== currentIndex) {
      event.preventDefault();
      const newTabId = enabledTabs[newIndex].id;
      setFocusedTab(newTabId);
      
      // Focus the tab button
      const tabButton = tabListRef.current?.querySelector(
        `[data-tab-id="${newTabId}"]`
      ) as HTMLButtonElement;
      tabButton?.focus();
    }
    
    if (event.key === KEYBOARD_KEYS.ENTER || event.key === KEYBOARD_KEYS.SPACE) {
      event.preventDefault();
      handleTabClick(focusedTab);
    }
  };

  const activeTabContent = tabs.find(tab => tab.id === activeTab)?.content;

  return (
    <div className={cn('w-full', className)}>
      {/* Tab List */}
      <div
        ref={tabListRef}
        role="tablist"
        aria-orientation={orientation}
        className={cn(
          'flex border-b',
          orientation === 'vertical' && 'flex-col border-b-0 border-r'
        )}
        onKeyDown={handleKeyDown}
      >
        {tabs.map((tab) => (
          <button
            key={tab.id}
            data-tab-id={tab.id}
            role="tab"
            aria-selected={activeTab === tab.id}
            aria-controls={`panel-${tab.id}`}
            aria-disabled={tab.disabled}
            tabIndex={focusedTab === tab.id ? 0 : -1}
            disabled={tab.disabled}
            onClick={() => handleTabClick(tab.id)}
            className={cn(
              'px-4 py-2 text-sm font-medium transition-colors',
              'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
              'disabled:opacity-50 disabled:cursor-not-allowed',
              activeTab === tab.id
                ? 'border-b-2 border-primary text-primary'
                : 'text-muted-foreground hover:text-foreground',
              orientation === 'vertical' && 'border-b-0 border-r-2 text-left'
            )}
          >
            {tab.label}
          </button>
        ))}
      </div>
      
      {/* Tab Panels */}
      <div className="mt-4">
        {tabs.map((tab) => (
          <div
            key={tab.id}
            id={`panel-${tab.id}`}
            role="tabpanel"
            aria-labelledby={tab.id}
            hidden={activeTab !== tab.id}
            tabIndex={0}
            className="focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded"
          >
            {activeTab === tab.id && tab.content}
          </div>
        ))}
      </div>
    </div>
  );
}

// ============================================================================
// ACCESSIBLE DROPDOWN
// ============================================================================

interface DropdownOption {
  id: string;
  label: string;
  value: string;
  disabled?: boolean;
}

interface AccessibleDropdownProps {
  options: DropdownOption[];
  value?: string;
  placeholder?: string;
  onSelect: (value: string) => void;
  className?: string;
  disabled?: boolean;
}

export function AccessibleDropdown({
  options,
  value,
  placeholder = 'Select an option',
  onSelect,
  className,
  disabled = false,
}: AccessibleDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const triggerRef = useRef<HTMLButtonElement>(null);
  const listRef = useRef<HTMLUListElement>(null);
  const listboxId = generateAriaId('listbox');

  const selectedOption = options.find(option => option.value === value);

  const handleToggle = () => {
    if (disabled) return;
    setIsOpen(!isOpen);
    setFocusedIndex(-1);
  };

  const handleSelect = (optionValue: string) => {
    onSelect(optionValue);
    setIsOpen(false);
    triggerRef.current?.focus();
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (disabled) return;

    switch (event.key) {
      case KEYBOARD_KEYS.ENTER:
      case KEYBOARD_KEYS.SPACE:
        event.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
          setFocusedIndex(0);
        } else if (focusedIndex >= 0) {
          const option = options[focusedIndex];
          if (!option.disabled) {
            handleSelect(option.value);
          }
        }
        break;
        
      case KEYBOARD_KEYS.ESCAPE:
        if (isOpen) {
          setIsOpen(false);
          triggerRef.current?.focus();
        }
        break;
        
      case KEYBOARD_KEYS.ARROW_DOWN:
        event.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
          setFocusedIndex(0);
        } else {
          const nextIndex = focusedIndex < options.length - 1 ? focusedIndex + 1 : 0;
          setFocusedIndex(nextIndex);
        }
        break;
        
      case KEYBOARD_KEYS.ARROW_UP:
        event.preventDefault();
        if (isOpen) {
          const prevIndex = focusedIndex > 0 ? focusedIndex - 1 : options.length - 1;
          setFocusedIndex(prevIndex);
        }
        break;
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node) &&
        listRef.current &&
        !listRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className={cn('relative', className)}>
      <button
        ref={triggerRef}
        type="button"
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        aria-labelledby={listboxId}
        disabled={disabled}
        onClick={handleToggle}
        onKeyDown={handleKeyDown}
        className={cn(
          'w-full px-3 py-2 text-left bg-background border border-input rounded-md',
          'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          isOpen && 'ring-2 ring-ring ring-offset-2'
        )}
      >
        {selectedOption ? selectedOption.label : placeholder}
      </button>
      
      {isOpen && (
        <ul
          ref={listRef}
          id={listboxId}
          role="listbox"
          aria-activedescendant={
            focusedIndex >= 0 ? `option-${options[focusedIndex].id}` : undefined
          }
          className="absolute z-10 w-full mt-1 bg-background border border-input rounded-md shadow-lg max-h-60 overflow-auto"
        >
          {options.map((option, index) => (
            <li
              key={option.id}
              id={`option-${option.id}`}
              role="option"
              aria-selected={value === option.value}
              aria-disabled={option.disabled}
              onClick={() => !option.disabled && handleSelect(option.value)}
              className={cn(
                'px-3 py-2 cursor-pointer',
                'hover:bg-accent hover:text-accent-foreground',
                focusedIndex === index && 'bg-accent text-accent-foreground',
                value === option.value && 'bg-primary text-primary-foreground',
                option.disabled && 'opacity-50 cursor-not-allowed'
              )}
            >
              {option.label}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}
