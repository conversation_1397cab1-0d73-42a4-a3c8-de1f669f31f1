'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  ArrowRight,
  Settings,
  Users,
  Shield,
  Mic,
  Video,
  ChevronDown,
  Sparkles,
  Copy,
  Share2,
  Loader2
} from 'lucide-react';
import { useRoomCreation } from '@/hooks/useRoom';
import { useAnonymousIdentity } from '@/hooks/useAnonymousIdentity';
import { RoomConfig } from '@/hooks/useRoom';
import { generateShareUrl, copyToClipboard } from '@/lib/identity';
import { toast } from 'sonner';

export default function CreateRoomPage() {
  const router = useRouter();
  const { createRoom, createdRoom, isCreating, error } = useRoomCreation();
  const { isLoading: identityLoading } = useAnonymousIdentity();
  
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [showShareOptions, setShowShareOptions] = useState(false);
  const [roomConfig, setRoomConfig] = useState<RoomConfig>({
    topic: '',
    isPublic: false,
    maxParticipants: 10,
    allowVoice: true,
    allowVideo: false,
    aiModerationLevel: 'moderate',
  });

  const handleCreateRoom = async () => {
    try {
      await createRoom(roomConfig);
      setShowShareOptions(true);
      toast.success('Room created successfully!');
    } catch (err) {
      toast.error('Failed to create room');
    }
  };

  const handleJoinRoom = () => {
    if (createdRoom) {
      router.push(`/room/${createdRoom.roomCode}`);
    }
  };

  const handleCopyLink = async () => {
    if (createdRoom) {
      const shareUrl = generateShareUrl(createdRoom.roomCode);
      const success = await copyToClipboard(shareUrl);
      if (success) {
        toast.success('Room link copied to clipboard!');
      } else {
        toast.error('Failed to copy link');
      }
    }
  };

  const handleShare = async () => {
    if (createdRoom && navigator.share) {
      try {
        await navigator.share({
          title: 'Join my anonymous room',
          text: `Join me in an anonymous discussion: ${roomConfig.topic || 'Open Discussion'}`,
          url: generateShareUrl(createdRoom.roomCode),
        });
      } catch (err) {
        // Fallback to copy
        handleCopyLink();
      }
    } else {
      handleCopyLink();
    }
  };

  if (identityLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 flex items-center justify-center">
        <div className="flex items-center gap-2 text-muted-foreground">
          <Loader2 className="h-5 w-5 animate-spin" />
          <span>Preparing your anonymous identity...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* App Branding & Page Header */}
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* VeilMeet Logo & Brand */}
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
              <Shield className="h-6 w-6 text-white" />
            </div>
            <div className="flex flex-col items-start">
              <span className="text-lg font-semibold text-foreground">VeilMeet</span>
              <span className="text-sm text-muted-foreground">Anonymous Discussions</span>
            </div>
          </div>

          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent mb-4">
            Create Anonymous Room
          </h1>
          <p className="text-muted-foreground text-lg">
            Start an instant anonymous discussion with complete privacy
          </p>
        </motion.div>

        {!showShareOptions ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-primary" />
                  Room Configuration
                </CardTitle>
                <CardDescription>
                  Customize your anonymous room settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Topic Input */}
                <div className="space-y-2">
                  <Label htmlFor="topic">Discussion Topic (Optional)</Label>
                  <Input
                    id="topic"
                    placeholder="What would you like to discuss?"
                    value={roomConfig.topic}
                    onChange={(e) => setRoomConfig(prev => ({ ...prev, topic: e.target.value }))}
                    className="bg-background/50"
                  />
                </div>

                {/* Quick Create Button */}
                <Button
                  onClick={handleCreateRoom}
                  disabled={isCreating}
                  size="lg"
                  className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-primary-foreground py-6 text-lg font-semibold shadow-lg"
                >
                  {isCreating ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Creating Room...
                    </>
                  ) : (
                    <>
                      <Sparkles className="mr-2 h-5 w-5" />
                      Create Room Instantly
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </>
                  )}
                </Button>

                {/* Advanced Settings */}
                <Collapsible open={showAdvanced} onOpenChange={setShowAdvanced}>
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" className="w-full justify-between">
                      <span className="flex items-center gap-2">
                        <Settings className="h-4 w-4" />
                        Advanced Settings
                      </span>
                      <ChevronDown className={`h-4 w-4 transition-transform ${showAdvanced ? 'rotate-180' : ''}`} />
                    </Button>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-4 pt-4">
                    {/* Public Room Toggle */}
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Public Room</Label>
                        <p className="text-sm text-muted-foreground">
                          Allow others to discover this room
                        </p>
                      </div>
                      <Switch
                        checked={roomConfig.isPublic}
                        onCheckedChange={(checked) => 
                          setRoomConfig(prev => ({ ...prev, isPublic: checked }))
                        }
                      />
                    </div>

                    {/* Max Participants */}
                    <div className="space-y-2">
                      <Label>Maximum Participants: {roomConfig.maxParticipants}</Label>
                      <Slider
                        value={[roomConfig.maxParticipants || 10]}
                        onValueChange={([value]) => 
                          setRoomConfig(prev => ({ ...prev, maxParticipants: value }))
                        }
                        max={50}
                        min={2}
                        step={1}
                        className="w-full"
                      />
                    </div>

                    {/* Voice/Video Settings */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label className="flex items-center gap-2">
                            <Mic className="h-4 w-4" />
                            Voice Chat
                          </Label>
                        </div>
                        <Switch
                          checked={roomConfig.allowVoice}
                          onCheckedChange={(checked) => 
                            setRoomConfig(prev => ({ ...prev, allowVoice: checked }))
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label className="flex items-center gap-2">
                            <Video className="h-4 w-4" />
                            Video Chat
                          </Label>
                        </div>
                        <Switch
                          checked={roomConfig.allowVideo}
                          onCheckedChange={(checked) => 
                            setRoomConfig(prev => ({ ...prev, allowVideo: checked }))
                          }
                        />
                      </div>
                    </div>

                    {/* AI Moderation Level */}
                    <div className="space-y-2">
                      <Label className="flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        AI Moderation Level
                      </Label>
                      <Select
                        value={roomConfig.aiModerationLevel}
                        onValueChange={(value: 'light' | 'moderate' | 'strict') =>
                          setRoomConfig(prev => ({ ...prev, aiModerationLevel: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="light">Light - Minimal intervention</SelectItem>
                          <SelectItem value="moderate">Moderate - Balanced approach</SelectItem>
                          <SelectItem value="strict">Strict - Active moderation</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CollapsibleContent>
                </Collapsible>

                {error && (
                  <div className="text-destructive text-sm bg-destructive/10 p-3 rounded-md">
                    {error}
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        ) : (
          /* Share Options */
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl text-green-500">Room Created Successfully!</CardTitle>
                <CardDescription>
                  Your anonymous room is ready. Share the link to invite others.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Room Info */}
                <div className="bg-background/50 p-4 rounded-lg space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Room Code:</span>
                    <span className="font-mono font-semibold">{createdRoom?.roomCode}</span>
                  </div>
                  {roomConfig.topic && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Topic:</span>
                      <span className="font-medium">{roomConfig.topic}</span>
                    </div>
                  )}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Max Participants:</span>
                    <span>{roomConfig.maxParticipants}</span>
                  </div>
                </div>

                {/* Share Actions */}
                <div className="grid grid-cols-2 gap-3">
                  <Button onClick={handleCopyLink} variant="outline" className="flex items-center gap-2">
                    <Copy className="h-4 w-4" />
                    Copy Link
                  </Button>
                  <Button onClick={handleShare} variant="outline" className="flex items-center gap-2">
                    <Share2 className="h-4 w-4" />
                    Share
                  </Button>
                </div>

                {/* Join Room Button */}
                <Button
                  onClick={handleJoinRoom}
                  size="lg"
                  className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-primary-foreground py-6 text-lg font-semibold shadow-lg"
                >
                  <Users className="mr-2 h-5 w-5" />
                  Enter Room
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  );
}
