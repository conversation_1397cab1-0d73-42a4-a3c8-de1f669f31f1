import { useState, useCallback, useEffect } from 'react';
import { useQuery, useMutation, useAction } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { useAnonymousIdentity } from './useAnonymousIdentity';
import { useLiveKitVoice } from './useLiveKitVoice';
import { toast } from 'sonner';

export interface VoiceRoomState {
  isConnected: boolean;
  isConnecting: boolean;
  isMuted: boolean;
  isSpeaking: boolean;
  audioEnabled: boolean;
  canJoin: boolean;
  participantCount: number;
  maxParticipants: number;
  connectionQuality: 'excellent' | 'good' | 'poor' | null;
}

export interface VoiceParticipant {
  sessionId: string;
  anonymousAlias: string;
  isMuted: boolean;
  isSpeaking: boolean;
  audioEnabled: boolean;
  joinedAt: number;
}

export function useVoiceRoom(roomId?: Id<'rooms'>) {
  const { identity } = useAnonymousIdentity();
  const [error, setError] = useState<string | null>(null);
  const [isReconnecting, setIsReconnecting] = useState(false);
  const [hasAttemptedReconnection, setHasAttemptedReconnection] = useState(false);

  // Use the LiveKit voice hook for actual voice functionality
  const liveKitVoice = useLiveKitVoice(roomId);

  const [voiceState, setVoiceState] = useState<VoiceRoomState>({
    isConnected: liveKitVoice.state.isConnected,
    isConnecting: liveKitVoice.state.isConnecting || isReconnecting,
    isMuted: liveKitVoice.state.isMuted,
    isSpeaking: liveKitVoice.state.isSpeaking,
    audioEnabled: liveKitVoice.state.audioEnabled,
    canJoin: false,
    participantCount: liveKitVoice.state.participants.length,
    maxParticipants: 10,
    connectionQuality: liveKitVoice.state.connectionQuality,
  });

  // Queries
  const voiceRoomStatus = useQuery(
    api.livekit.getVoiceRoomStatus,
    roomId ? { roomId } : 'skip'
  );

  const voiceParticipants = useQuery(
    api.livekit.getVoiceParticipants,
    roomId ? { roomId } : 'skip'
  );

  const voiceConnectionInfo = useQuery(
    api.voiceRoomManagement.getVoiceConnectionInfo,
    roomId && identity ? { 
      roomId, 
      participantSessionId: identity.sessionId 
    } : 'skip'
  );

  // Actions and Mutations
  const generateTokenAction = useAction(api.livekitActions.generateVoiceTokenForParticipant);
  const joinVoiceRoomMutation = useMutation(api.livekit.joinVoiceRoom);
  const leaveVoiceRoomMutation = useMutation(api.livekit.leaveVoiceRoom);
  const toggleMuteMutation = useMutation(api.livekit.toggleVoiceMute);
  const enableVoiceMutation = useMutation(api.voiceRoomManagement.enableVoiceForRoom);

  // Automatic reconnection logic
  useEffect(() => {
    if (!roomId || !identity || !voiceConnectionInfo || hasAttemptedReconnection) {
      return;
    }

    // Check if user was previously in voice but not currently connected to LiveKit
    const wasInVoice = !!voiceConnectionInfo.participant;
    const isCurrentlyConnected = liveKitVoice.state.isConnected;

    if (wasInVoice && !isCurrentlyConnected && !isReconnecting) {
      console.log('Attempting automatic voice reconnection...');
      setIsReconnecting(true);
      setHasAttemptedReconnection(true);

      // Add a small delay to allow for network stabilization
      setTimeout(() => {
        // Attempt to reconnect
        liveKitVoice.joinVoiceRoom()
          .then(() => {
            console.log('Automatic voice reconnection successful');
            setIsReconnecting(false);
            toast.success('Reconnected to voice chat');
          })
          .catch((err) => {
            console.error('Automatic voice reconnection failed:', err);
            setIsReconnecting(false);
            setError('Failed to reconnect to voice chat. Please try joining again.');
            toast.error('Voice reconnection failed');
          });
      }, 1000); // 1 second delay
    }
  }, [roomId, identity, voiceConnectionInfo, liveKitVoice.state.isConnected, hasAttemptedReconnection, isReconnecting, liveKitVoice]);

  // Update voice state based on queries
  useEffect(() => {
    if (voiceRoomStatus && voiceConnectionInfo) {
      setVoiceState(prev => ({
        ...prev,
        canJoin: voiceRoomStatus.canJoin && !voiceConnectionInfo.participant,
        isConnected: !!voiceConnectionInfo.participant && liveKitVoice.state.isConnected,
        participantCount: voiceRoomStatus.participantCount,
        maxParticipants: voiceRoomStatus.voiceRoom?.maxParticipants || 10,
        isMuted: voiceConnectionInfo.participant?.isMuted ?? true,
        isSpeaking: voiceConnectionInfo.participant?.isSpeaking ?? false,
        audioEnabled: voiceConnectionInfo.participant?.audioEnabled ?? false,
      }));
    }
  }, [voiceRoomStatus, voiceConnectionInfo, liveKitVoice.state.isConnected]);

  // Join voice room
  const joinVoiceRoom = useCallback(async () => {
    if (!roomId || !identity) {
      throw new Error('Room ID or identity not available');
    }

    try {
      setError(null);
      setIsReconnecting(false); // Clear any reconnection state

      // First, ensure we have a voice room
      let voiceRoomId = voiceRoomStatus?.voiceRoom?._id;
      if (!voiceRoomId) {
        // Create voice room if it doesn't exist
        voiceRoomId = await enableVoiceMutation({
          roomId,
          creatorSessionId: identity.sessionId,
        });
      }

      // Join the voice room in the backend
      await joinVoiceRoomMutation({
        roomId,
        participantSessionId: identity.sessionId,
      });

      // Use LiveKit to actually connect to voice
      await liveKitVoice.joinVoiceRoom();

    } catch (err) {
      console.error('Failed to join voice room:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to join voice room';
      setError(errorMessage);
      setIsReconnecting(false);
      throw err;
    }
  }, [roomId, identity, voiceRoomStatus, enableVoiceMutation, joinVoiceRoomMutation, liveKitVoice]);

  // Retry connection (for manual retry after failed reconnection)
  const retryConnection = useCallback(async () => {
    setHasAttemptedReconnection(false); // Reset reconnection attempt flag
    setError(null);
    await joinVoiceRoom();
  }, [joinVoiceRoom]);

  // Leave voice room
  const leaveVoiceRoom = useCallback(async () => {
    if (!roomId || !identity) {
      throw new Error('Room ID or identity not available');
    }

    try {
      setError(null);

      // Disconnect from LiveKit first
      await liveKitVoice.leaveVoiceRoom();

      // Then update backend
      await leaveVoiceRoomMutation({
        roomId,
        participantSessionId: identity.sessionId,
      });

    } catch (err) {
      console.error('Failed to leave voice room:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to leave voice room';
      setError(errorMessage);
      throw err;
    }
  }, [roomId, identity, liveKitVoice, leaveVoiceRoomMutation]);

  // Toggle mute
  const toggleMute = useCallback(async () => {
    if (!roomId || !identity) {
      throw new Error('Room ID or identity not available');
    }

    try {
      setError(null);

      // Get current mute state before toggling
      const currentlyMuted = liveKitVoice.state.isMuted;
      const newMutedState = !currentlyMuted;

      // Toggle mute in LiveKit first
      await liveKitVoice.toggleMute();

      // Then update backend with the new state
      await toggleMuteMutation({
        roomId,
        participantSessionId: identity.sessionId,
        muted: newMutedState,
      });

      // Return the new muted state for the UI
      return newMutedState;

    } catch (err) {
      console.error('Failed to toggle mute:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to toggle mute';
      setError(errorMessage);
      throw err;
    }
  }, [roomId, identity, liveKitVoice, toggleMuteMutation]);

  // Enable voice for room (if not already enabled)
  const enableVoiceForRoom = useCallback(async () => {
    if (!roomId || !identity) {
      throw new Error('Room ID or identity not available');
    }

    try {
      setError(null);

      const voiceRoomId = await enableVoiceMutation({
        roomId,
        creatorSessionId: identity.sessionId,
      });

      return voiceRoomId;
    } catch (err) {
      console.error('Failed to enable voice for room:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to enable voice for room';
      setError(errorMessage);
      throw err;
    }
  }, [roomId, identity, enableVoiceMutation]);

  // Sync LiveKit state with local voice state
  useEffect(() => {
    setVoiceState(prev => ({
      ...prev,
      isConnected: liveKitVoice.state.isConnected,
      isConnecting: liveKitVoice.state.isConnecting || isReconnecting,
      isMuted: liveKitVoice.state.isMuted,
      isSpeaking: liveKitVoice.state.isSpeaking,
      audioEnabled: liveKitVoice.state.audioEnabled,
      connectionQuality: liveKitVoice.state.connectionQuality,
      participantCount: liveKitVoice.state.participants.length,
    }));

    // Sync error state
    if (liveKitVoice.state.error && !error) {
      setError(liveKitVoice.state.error);
    }

    // Clear reconnecting state when successfully connected
    if (liveKitVoice.state.isConnected && isReconnecting) {
      setIsReconnecting(false);
    }
  }, [liveKitVoice.state, error, isReconnecting]);

  // Update voice state based on queries
  useEffect(() => {
    if (voiceRoomStatus) {
      setVoiceState(prev => ({
        ...prev,
        canJoin: voiceRoomStatus.canJoin && !liveKitVoice.state.isConnected,
        maxParticipants: voiceRoomStatus.voiceRoom?.maxParticipants || 10,
      }));
    }
  }, [voiceRoomStatus, liveKitVoice.state.isConnected]);

  // Get formatted voice participants - prioritize LiveKit participants when connected
  const formattedParticipants: VoiceParticipant[] = liveKitVoice.state.isConnected
    ? liveKitVoice.state.participants.map(p => ({
        sessionId: p.identity,
        anonymousAlias: p.name,
        isMuted: p.isMuted,
        isSpeaking: p.isSpeaking,
        audioEnabled: p.audioEnabled,
        joinedAt: Date.now(), // LiveKit doesn't provide join time
      }))
    : (voiceParticipants?.map(p => ({
        sessionId: p.participantSessionId,
        anonymousAlias: p.anonymousAlias,
        isMuted: p.isMuted,
        isSpeaking: p.isSpeaking,
        audioEnabled: p.audioEnabled,
        joinedAt: p.joinedVoiceAt,
      })) || []);

  return {
    // State
    voiceState,
    participants: formattedParticipants,
    error: error || liveKitVoice.state.error,

    // Room info
    voiceRoomInfo: voiceRoomStatus?.voiceRoom || null,
    connectionInfo: voiceConnectionInfo,

    // Actions
    joinVoiceRoom,
    leaveVoiceRoom,
    toggleMute,
    enableVoiceForRoom,
    retryConnection,

    // Computed properties
    isVoiceEnabled: !!voiceRoomStatus?.voiceRoom,
    isVoiceActive: voiceRoomStatus?.isActive ?? false,
    canJoinVoice: voiceState.canJoin && !voiceState.isConnected,

    // Clear error
    clearError: () => {
      setError(null);
      liveKitVoice.clearError();
    },
  };
}

// Hook for voice room analytics
export function useVoiceRoomAnalytics(roomId?: Id<'rooms'>) {
  const analytics = useQuery(
    api.voiceRoomManagement.getVoiceRoomAnalytics,
    roomId ? { roomId } : 'skip'
  );

  return {
    analytics,
    isLoading: analytics === undefined,
    hasData: !!analytics,
  };
}
