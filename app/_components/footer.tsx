"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
  Shield,
  Mail,
  Github,
  Twitter,
  Linkedin,
  ExternalLink,
  Heart
} from "lucide-react";

interface FooterLink {
  name: string;
  href: string;
  external?: boolean;
}

interface FooterSection {
  title: string;
  links: FooterLink[];
}

export const Footer: React.FC = () => {
  const footerSections: FooterSection[] = [
    {
      title: "Product",
      links: [
        { name: "Features", href: "#features" },
        { name: "How It Works", href: "#how-it-works" },
        { name: "Use Cases", href: "#use-cases" },
        { name: "AI Technology", href: "#ai-capabilities" },
        { name: "Pricing", href: "/pricing" },
        { name: "Enterprise", href: "/enterprise" }
      ]
    },
    {
      title: "Company",
      links: [
        { name: "About Us", href: "/about" },
        { name: "Contact", href: "/contact" },
        { name: "Careers", href: "/careers" },
        { name: "Blog", href: "/blog" },
        { name: "Press Kit", href: "/press" },
        { name: "Partners", href: "/partners" }
      ]
    },
    {
      title: "Legal",
      links: [
        { name: "Terms of Service", href: "/terms" },
        { name: "Privacy Policy", href: "/privacy" },
        { name: "Cookie Policy", href: "/cookies" },
        { name: "Security", href: "/security" },
        { name: "Compliance", href: "/compliance" },
        { name: "Data Protection", href: "/data-protection" }
      ]
    },
    {
      title: "Support",
      links: [
        { name: "Help Center", href: "/help" },
        { name: "Documentation", href: "/docs" },
        { name: "API Reference", href: "/api" },
        { name: "Status Page", href: "/status", external: true },
        { name: "Community", href: "/community" },
        { name: "Bug Reports", href: "/bugs" }
      ]
    }
  ];

  const socialLinks = [
    { name: "GitHub", href: "https://github.com/veilmeet", icon: Github },
    { name: "Twitter", href: "https://twitter.com/veilmeet", icon: Twitter },
    { name: "LinkedIn", href: "https://linkedin.com/company/veilmeet", icon: Linkedin }
  ];

  const contactInfo = [
    { label: "General", email: "<EMAIL>" },
    { label: "Security", email: "<EMAIL>" },
    { label: "Support", email: "<EMAIL>" },
    { label: "Press", email: "<EMAIL>" }
  ];

  return (
    <footer className="bg-background border-t border-border">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          {/* Main Footer Content */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8 mb-12">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <div className="flex items-center gap-2 mb-4">
                <div className="p-2 rounded-lg bg-primary/10">
                  <Shield className="h-6 w-6 text-primary" />
                </div>
                <span className="text-2xl font-bold">VeilMeet</span>
              </div>
              <p className="text-muted-foreground mb-6 leading-relaxed">
                Anonymous conversations, authentic connections. 
                Secure, AI-enhanced meeting rooms with complete privacy guaranteed.
              </p>
              
              {/* Newsletter Signup */}
              <div className="mb-6">
                <h4 className="font-semibold mb-3">Stay Updated</h4>
                <div className="flex gap-2">
                  <Input 
                    placeholder="Enter your email" 
                    className="flex-1"
                  />
                  <Button size="sm">
                    Subscribe
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Get security updates and feature announcements
                </p>
              </div>

              {/* Security Badges */}
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline" className="text-xs">
                  SOC 2 Certified
                </Badge>
                <Badge variant="outline" className="text-xs">
                  GDPR Compliant
                </Badge>
                <Badge variant="outline" className="text-xs">
                  HIPAA Ready
                </Badge>
              </div>
            </div>

            {/* Footer Links */}
            {footerSections.map((section, index) => (
              <div key={index}>
                <h4 className="font-semibold mb-4">{section.title}</h4>
                <ul className="space-y-3">
                  {section.links.map((link, linkIndex) => (
                    <li key={linkIndex}>
                      <a
                        href={link.href}
                        className="text-muted-foreground hover:text-foreground transition-colors text-sm flex items-center gap-1"
                        target={link.external ? "_blank" : undefined}
                        rel={link.external ? "noopener noreferrer" : undefined}
                      >
                        {link.name}
                        {link.external && (
                          <ExternalLink className="h-3 w-3" />
                        )}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          <Separator className="mb-8" />

          {/* Contact Information */}
          <div className="mb-8">
            <h4 className="font-semibold mb-4">Contact Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {contactInfo.map((contact, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">{contact.label}</div>
                    <a 
                      href={`mailto:${contact.email}`}
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                    >
                      {contact.email}
                    </a>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <Separator className="mb-8" />

          {/* Bottom Section */}
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            {/* Copyright */}
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <span>© 2024 VeilMeet. All rights reserved.</span>
              <span className="hidden md:inline">•</span>
              <span className="flex items-center gap-1">
                Made with <Heart className="h-3 w-3 text-red-500" /> for privacy
              </span>
            </div>

            {/* Social Links */}
            <div className="flex items-center gap-4">
              <span className="text-sm text-muted-foreground">Follow us:</span>
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 rounded-lg bg-muted/50 hover:bg-muted transition-colors"
                  aria-label={social.name}
                >
                  <social.icon className="h-4 w-4" />
                </a>
              ))}
            </div>
          </div>

          {/* Legal Notice */}
          <div className="mt-8 pt-8 border-t border-border">
            <div className="text-xs text-muted-foreground text-center max-w-4xl mx-auto leading-relaxed">
              <p className="mb-2">
                VeilMeet is committed to protecting your privacy and anonymity. 
                We use zero-knowledge architecture and do not collect, store, or process any personal information.
              </p>
              <p>
                All conversations are encrypted end-to-end and automatically deleted after sessions. 
                For security concerns or questions, contact our security <NAME_EMAIL>
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
