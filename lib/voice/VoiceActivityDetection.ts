/**
 * VeilMind Voice Activity Detection (VAD) System
 * Smart voice detection with automatic threshold adaptation
 */

export interface VADSettings {
  sensitivity: number; // 0 to 1 (higher = more sensitive)
  noiseGateThreshold: number; // dB
  adaptiveThreshold: boolean;
  whisperMode: boolean;
  backgroundNoiseFiltering: boolean;
  autoMuteTimeout: number; // ms
}

export interface VADState {
  isSpeaking: boolean;
  isListening: boolean;
  isMuted: boolean;
  isProcessing: boolean;
  audioLevel: number; // 0 to 1
  backgroundNoiseLevel: number; // 0 to 1
  adaptiveThreshold: number; // Current threshold
}

export type VADEventType = 'speaking' | 'stopped' | 'muted' | 'unmuted' | 'threshold_adapted';

export interface VADEvent {
  type: VADEventType;
  timestamp: number;
  data?: any;
}

export class VoiceActivityDetection {
  private audioContext: AudioContext | null = null;
  private analyserNode: AnalyserNode | null = null;
  private sourceNode: MediaStreamAudioSourceNode | null = null;
  private inputStream: MediaStream | null = null;
  
  private settings: VADSettings;
  private state: VADState;
  private eventListeners: Map<VADEventType, Set<(event: VADEvent) => void>> = new Map();
  
  // Analysis buffers
  private frequencyData: Uint8Array | null = null;
  private timeData: Uint8Array | null = null;
  
  // Adaptive threshold variables
  private backgroundNoiseSamples: number[] = [];
  private readonly BACKGROUND_SAMPLE_SIZE = 100;
  private lastSpeechTime = 0;
  private silenceStartTime = 0;
  
  // Auto-mute timer
  private autoMuteTimer: NodeJS.Timeout | null = null;
  
  // Processing loop
  private animationFrame: number | null = null;
  private isRunning = false;
  
  constructor(settings: Partial<VADSettings> = {}) {
    this.settings = {
      sensitivity: 0.5,
      noiseGateThreshold: -40,
      adaptiveThreshold: true,
      whisperMode: false,
      backgroundNoiseFiltering: true,
      autoMuteTimeout: 5000, // 5 seconds
      ...settings
    };
    
    this.state = {
      isSpeaking: false,
      isListening: true,
      isMuted: false,
      isProcessing: false,
      audioLevel: 0,
      backgroundNoiseLevel: 0,
      adaptiveThreshold: this.dbToLinear(this.settings.noiseGateThreshold)
    };
    
    // Initialize event listener maps
    ['speaking', 'stopped', 'muted', 'unmuted', 'threshold_adapted'].forEach(eventType => {
      this.eventListeners.set(eventType as VADEventType, new Set());
    });
  }
  
  async initialize(inputStream: MediaStream): Promise<void> {
    try {
      this.inputStream = inputStream;
      
      // Create audio context if not exists
      if (!this.audioContext) {
        this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      }
      
      // Create analyser node
      this.analyserNode = this.audioContext.createAnalyser();
      this.analyserNode.fftSize = 2048;
      this.analyserNode.smoothingTimeConstant = 0.8;
      
      // Create source node
      this.sourceNode = this.audioContext.createMediaStreamSource(inputStream);
      this.sourceNode.connect(this.analyserNode);
      
      // Initialize data arrays
      this.frequencyData = new Uint8Array(this.analyserNode.frequencyBinCount);
      this.timeData = new Uint8Array(this.analyserNode.fftSize);
      
      console.log('Voice Activity Detection initialized');
    } catch (error) {
      console.error('Failed to initialize VAD:', error);
      throw error;
    }
  }
  
  start(): void {
    if (this.isRunning || !this.analyserNode) return;
    
    this.isRunning = true;
    this.state.isProcessing = true;
    this.processAudio();
    
    console.log('Voice Activity Detection started');
  }
  
  stop(): void {
    if (!this.isRunning) return;
    
    this.isRunning = false;
    this.state.isProcessing = false;
    
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }
    
    if (this.autoMuteTimer) {
      clearTimeout(this.autoMuteTimer);
      this.autoMuteTimer = null;
    }
    
    console.log('Voice Activity Detection stopped');
  }
  
  private processAudio(): void {
    if (!this.isRunning || !this.analyserNode || !this.frequencyData || !this.timeData) {
      return;
    }
    
    // Get audio data
    this.analyserNode.getByteFrequencyData(this.frequencyData);
    this.analyserNode.getByteTimeDomainData(this.timeData);
    
    // Calculate audio level
    const audioLevel = this.calculateAudioLevel();
    this.state.audioLevel = audioLevel;
    
    // Update background noise level
    this.updateBackgroundNoise(audioLevel);
    
    // Adapt threshold if enabled
    if (this.settings.adaptiveThreshold) {
      this.adaptThreshold();
    }
    
    // Determine if speaking
    const wasSpeaking = this.state.isSpeaking;
    const isSpeaking = this.detectSpeech(audioLevel);
    
    if (isSpeaking !== wasSpeaking) {
      this.state.isSpeaking = isSpeaking;
      
      if (isSpeaking) {
        this.onSpeechStart();
      } else {
        this.onSpeechStop();
      }
    }
    
    // Schedule next frame
    this.animationFrame = requestAnimationFrame(() => this.processAudio());
  }
  
  private calculateAudioLevel(): number {
    if (!this.frequencyData) return 0;
    
    let sum = 0;
    let count = 0;
    
    // Focus on speech frequencies (85Hz - 8kHz)
    const minBin = Math.floor(85 * this.frequencyData.length / (this.audioContext!.sampleRate / 2));
    const maxBin = Math.floor(8000 * this.frequencyData.length / (this.audioContext!.sampleRate / 2));
    
    for (let i = minBin; i < Math.min(maxBin, this.frequencyData.length); i++) {
      sum += this.frequencyData[i];
      count++;
    }
    
    const average = count > 0 ? sum / count : 0;
    return average / 255; // Normalize to 0-1
  }
  
  private updateBackgroundNoise(audioLevel: number): void {
    const now = Date.now();
    
    // Only update background noise during silence
    if (!this.state.isSpeaking && (now - this.lastSpeechTime) > 1000) {
      this.backgroundNoiseSamples.push(audioLevel);
      
      if (this.backgroundNoiseSamples.length > this.BACKGROUND_SAMPLE_SIZE) {
        this.backgroundNoiseSamples.shift();
      }
      
      // Calculate background noise level
      if (this.backgroundNoiseSamples.length > 10) {
        const sorted = [...this.backgroundNoiseSamples].sort((a, b) => a - b);
        const percentile75 = sorted[Math.floor(sorted.length * 0.75)];
        this.state.backgroundNoiseLevel = percentile75;
      }
    }
  }
  
  private adaptThreshold(): void {
    if (this.backgroundNoiseSamples.length < 20) return;
    
    const baseThreshold = this.state.backgroundNoiseLevel * 2;
    const sensitivityMultiplier = 0.5 + (this.settings.sensitivity * 1.5);
    const whisperMultiplier = this.settings.whisperMode ? 0.5 : 1.0;
    
    const newThreshold = baseThreshold * sensitivityMultiplier * whisperMultiplier;
    
    // Smooth threshold changes
    const smoothingFactor = 0.1;
    this.state.adaptiveThreshold = 
      this.state.adaptiveThreshold * (1 - smoothingFactor) + 
      newThreshold * smoothingFactor;
    
    this.emitEvent('threshold_adapted', { threshold: this.state.adaptiveThreshold });
  }
  
  private detectSpeech(audioLevel: number): boolean {
    if (this.state.isMuted) return false;
    
    const threshold = this.settings.adaptiveThreshold 
      ? this.state.adaptiveThreshold 
      : this.dbToLinear(this.settings.noiseGateThreshold);
    
    // Apply background noise filtering
    let effectiveLevel = audioLevel;
    if (this.settings.backgroundNoiseFiltering && this.state.backgroundNoiseLevel > 0) {
      effectiveLevel = Math.max(0, audioLevel - this.state.backgroundNoiseLevel);
    }
    
    // Check for speech patterns in frequency domain
    const hasVoiceFrequencies = this.detectVoiceFrequencies();
    
    return effectiveLevel > threshold && hasVoiceFrequencies;
  }
  
  private detectVoiceFrequencies(): boolean {
    if (!this.frequencyData) return false;
    
    // Check for typical voice frequency patterns
    const sampleRate = this.audioContext!.sampleRate;
    const binSize = sampleRate / this.analyserNode!.fftSize;
    
    // Voice fundamental frequency range (80-400 Hz)
    const fundamentalStart = Math.floor(80 / binSize);
    const fundamentalEnd = Math.floor(400 / binSize);
    
    // Voice formant ranges (400-3000 Hz)
    const formantStart = Math.floor(400 / binSize);
    const formantEnd = Math.floor(3000 / binSize);
    
    let fundamentalEnergy = 0;
    let formantEnergy = 0;
    let totalEnergy = 0;
    
    for (let i = 0; i < this.frequencyData.length; i++) {
      const energy = this.frequencyData[i];
      totalEnergy += energy;
      
      if (i >= fundamentalStart && i <= fundamentalEnd) {
        fundamentalEnergy += energy;
      }
      
      if (i >= formantStart && i <= formantEnd) {
        formantEnergy += energy;
      }
    }
    
    // Voice typically has energy in both fundamental and formant ranges
    const fundamentalRatio = fundamentalEnergy / totalEnergy;
    const formantRatio = formantEnergy / totalEnergy;
    
    return fundamentalRatio > 0.1 && formantRatio > 0.3;
  }
  
  private onSpeechStart(): void {
    this.lastSpeechTime = Date.now();
    
    // Clear auto-mute timer
    if (this.autoMuteTimer) {
      clearTimeout(this.autoMuteTimer);
      this.autoMuteTimer = null;
    }
    
    this.emitEvent('speaking', { audioLevel: this.state.audioLevel });
  }
  
  private onSpeechStop(): void {
    this.silenceStartTime = Date.now();
    
    // Start auto-mute timer if enabled
    if (this.settings.autoMuteTimeout > 0) {
      this.autoMuteTimer = setTimeout(() => {
        if (!this.state.isSpeaking && !this.state.isMuted) {
          this.setMuted(true);
        }
      }, this.settings.autoMuteTimeout);
    }
    
    this.emitEvent('stopped', { silenceDuration: this.silenceStartTime - this.lastSpeechTime });
  }
  
  // Public methods
  setMuted(muted: boolean): void {
    if (this.state.isMuted === muted) return;
    
    this.state.isMuted = muted;
    
    if (muted) {
      this.state.isSpeaking = false;
      this.emitEvent('muted', {});
    } else {
      this.emitEvent('unmuted', {});
    }
  }
  
  updateSettings(newSettings: Partial<VADSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    
    // Reset adaptive threshold if sensitivity changed
    if (newSettings.sensitivity !== undefined || newSettings.noiseGateThreshold !== undefined) {
      this.state.adaptiveThreshold = this.dbToLinear(this.settings.noiseGateThreshold);
    }
  }
  
  getState(): VADState {
    return { ...this.state };
  }
  
  getSettings(): VADSettings {
    return { ...this.settings };
  }
  
  // Event handling
  addEventListener(eventType: VADEventType, listener: (event: VADEvent) => void): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.add(listener);
    }
  }
  
  removeEventListener(eventType: VADEventType, listener: (event: VADEvent) => void): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.delete(listener);
    }
  }
  
  private emitEvent(type: VADEventType, data?: any): void {
    const event: VADEvent = {
      type,
      timestamp: Date.now(),
      data
    };
    
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.forEach(listener => listener(event));
    }
  }
  
  // Utility methods
  private dbToLinear(db: number): number {
    return Math.pow(10, db / 20);
  }
  
  private linearToDb(linear: number): number {
    return 20 * Math.log10(Math.max(linear, 0.000001));
  }
  
  // Cleanup
  dispose(): void {
    this.stop();
    
    if (this.sourceNode) {
      this.sourceNode.disconnect();
      this.sourceNode = null;
    }
    
    if (this.analyserNode) {
      this.analyserNode.disconnect();
      this.analyserNode = null;
    }
    
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
      this.audioContext = null;
    }
    
    this.eventListeners.clear();
    this.inputStream = null;
    this.frequencyData = null;
    this.timeData = null;
    
    console.log('Voice Activity Detection disposed');
  }
}
