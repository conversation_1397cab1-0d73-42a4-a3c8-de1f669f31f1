"use client";

import React from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  Zap,
  MessageSquare
} from "lucide-react";

interface Step {
  number: string;
  title: string;
  description: string;
  icon: React.ElementType;
  color: string;
}

export const HowItWorksSection: React.FC = () => {
  const steps: Step[] = [
    {
      number: "01",
      title: "Create Room",
      description: "Click 'Create Room' for an instant secure space. No registration or personal information required.",
      icon: Zap,
      color: "from-blue-500/20 to-cyan-500/20"
    },
    {
      number: "02",
      title: "Join Anonymously",
      description: "Share the room link with participants. Everyone gets AI-generated anonymous identities for complete privacy.",
      icon: Users,
      color: "from-purple-500/20 to-pink-500/20"
    },
    {
      number: "03",
      title: "Start Talking",
      description: "Begin your conversation with AI moderation ensuring productive discussions. All data is automatically deleted after the session.",
      icon: MessageSquare,
      color: "from-green-500/20 to-emerald-500/20"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <section id="how-it-works" className="py-24 bg-muted/30">
      <div className="container mx-auto px-4">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="max-w-6xl mx-auto"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
              How It Works
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Three simple steps to secure, anonymous conversations. 
              No complexity, no compromises on privacy.
            </p>
          </motion.div>

          {/* Steps - Simplified */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="text-center group"
                whileHover={{ y: -8 }}
              >
                <div className={`relative p-8 rounded-3xl bg-gradient-to-br ${step.color} backdrop-blur-sm border border-muted/20 hover:border-primary/30 transition-all duration-500 hover:shadow-2xl hover:shadow-primary/10`}>
                  {/* Step Icon */}
                  <div className="mb-6 flex justify-center">
                    <div className="p-4 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20 shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                      <step.icon className="h-8 w-8 text-primary" />
                    </div>
                  </div>

                  {/* Step Number */}
                  <div className="text-4xl font-bold text-primary/30 mb-4">
                    {step.number}
                  </div>

                  {/* Step Content */}
                  <h3 className="text-xl font-bold mb-4">
                    {step.title}
                  </h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {step.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Simple Summary */}
          <motion.div
            variants={itemVariants}
            className="mt-16 text-center"
          >
            <h3 className="text-2xl font-bold mb-4">
              Start Anonymous Conversations in Seconds
            </h3>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              No registration, no personal data, no complexity. Just secure, anonymous conversations with AI-powered moderation.
            </p>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};
