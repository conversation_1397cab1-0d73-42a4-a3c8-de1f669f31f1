"use client";

import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { EnhancedButton } from "@/components/ui/enhanced-button";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { heroVariants, getTransitionProps } from "@/lib/animation-variants";
import {
  Shield,
  Brain,
  Users,
  ArrowRight
} from "lucide-react";

export const HeroSection: React.FC = () => {
  const [roomCode, setRoomCode] = useState("");
  const router = useRouter();

  const handleCreateRoom = () => {
    router.push('/create-room');
  };

  const handleJoinRoom = async () => {
    if (!roomCode.trim()) {
      router.push('/join');
      return;
    }

    setIsJoining(true);
    try {
      // Add a small delay to show loading state
      await new Promise(resolve => setTimeout(resolve, 500));
      router.push(`/join?code=${encodeURIComponent(roomCode.trim())}`);
    } catch (error) {
      console.error('Error joining room:', error);
    } finally {
      setIsJoining(false);
    }
  };

  const [isJoining, setIsJoining] = useState(false);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleJoinRoom();
    }
  };

  return (
    <main
      className="relative h-screen w-full flex items-center justify-center overflow-hidden"
      role="main"
      aria-label="VeilMeet hero section"
    >
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-primary/5" aria-hidden="true">
        <div className="absolute inset-0">
          {/* Floating orbs with Framer Motion */}
          <motion.div 
            className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/10 rounded-full blur-3xl"
            animate={{
              x: [0, 100, 50, 0],
              y: [0, -50, -25, 0],
              scale: [1, 1.2, 1.1, 1],
            }}
            transition={{
              duration: 8,
              ease: "easeInOut",
              repeat: Infinity,
            }}
          />
          <motion.div 
            className="absolute top-3/4 right-1/4 w-96 h-96 bg-secondary/10 rounded-full blur-3xl"
            animate={{
              x: [0, -80, -40, 0],
              y: [0, 60, 30, 0],
              scale: [1, 0.8, 0.9, 1],
            }}
            transition={{
              duration: 10,
              ease: "easeInOut",
              repeat: Infinity,
            }}
          />
          <motion.div 
            className="absolute top-1/2 right-1/3 w-48 h-48 bg-accent/10 rounded-full blur-3xl"
            animate={{
              x: [0, 60, 30, 0],
              y: [0, -80, -40, 0],
              scale: [1, 1.1, 1.05, 1],
            }}
            transition={{
              duration: 6,
              ease: "easeInOut",
              repeat: Infinity,
            }}
          />
        </div>

        {/* Grid pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]" />
      </div>

      <motion.div
        className="relative z-10 w-full max-w-7xl mx-auto container-padding text-center flex flex-col justify-center items-center"
        variants={heroVariants.container}
        initial="hidden"
        animate="visible"
      >
        <motion.h1
          className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl xl:text-8xl font-bold mb-6 sm:mb-8 leading-tight"
          variants={heroVariants.item}
        >
          <span className="veil-gradient-text block">Speak Your Truth</span>
          <span className="text-foreground block">Without Consequences</span>
        </motion.h1>

        <motion.p
          className="text-lg sm:text-xl md:text-2xl text-muted-foreground mb-8 sm:mb-12 max-w-4xl mx-auto leading-relaxed px-2"
          variants={heroVariants.item}
        >
          Create secure, anonymous meeting rooms with AI-powered moderation. Complete privacy, instant setup, no registration required.
        </motion.p>

        <motion.div
          className="flex flex-col gap-6 justify-center items-center mb-12 sm:mb-16 px-2 max-w-2xl mx-auto"
          variants={heroVariants.item}
        >
          {/* Create Room Button */}
          <EnhancedButton
            size="lg"
            icon={ArrowRight}
            iconPosition="right"
            onClick={handleCreateRoom}
            className="w-full sm:w-auto px-6 sm:px-8 py-4 sm:py-6 text-base sm:text-lg shadow-lg hover:shadow-xl"
            gradient
            aria-label="Create a new anonymous room"
          >
            <span className="whitespace-nowrap">Create Anonymous Room</span>
          </EnhancedButton>

          {/* Divider */}
          <div className="flex items-center gap-4 w-full max-w-xs" role="separator" aria-label="or join existing room">
            <hr className="flex-1 border-border" aria-hidden="true" />
            <span className="text-sm text-muted-foreground whitespace-nowrap">or join existing</span>
            <hr className="flex-1 border-border" aria-hidden="true" />
          </div>

          {/* Join Room */}
          <div className="w-full space-y-3">
            <h2 className="text-lg font-semibold">Join Existing Room</h2>
            <div className="flex flex-col sm:flex-row gap-3">
              <Input
                placeholder="Enter room code (e.g., sunset-wolf-1234)"
                value={roomCode}
                onChange={(e) => setRoomCode(e.target.value)}
                onKeyPress={handleKeyPress}
                className="flex-1 min-h-[48px] text-center sm:text-left focus-ring"
                disabled={isJoining}
                aria-label="Enter room code to join discussion"
              />
              <EnhancedButton
                variant="outline"
                size="lg"
                icon={Users}
                onClick={handleJoinRoom}
                disabled={!roomCode.trim() || isJoining}
                loading={isJoining}
                loadingText="Joining..."
                className="min-h-[48px] px-6 sm:px-8 whitespace-nowrap"
                aria-label="Join room with entered code"
              >
                Join Room
              </EnhancedButton>
            </div>
          </div>
        </motion.div>

        {/* Trust Indicators */}
        <motion.div
          className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-center px-2"
          variants={heroVariants.item}
          role="list"
          aria-label="Key features"
        >
          <motion.div
            className="flex items-center gap-2"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
            role="listitem"
          >
            <Shield className="w-4 h-4 sm:w-5 sm:h-5 text-primary flex-shrink-0" aria-hidden="true" />
            <span className="text-sm sm:text-base whitespace-nowrap">100% Anonymous</span>
          </motion.div>
          <motion.div
            className="flex items-center gap-2"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
            role="listitem"
          >
            <Users className="w-4 h-4 sm:w-5 sm:h-5 text-primary flex-shrink-0" aria-hidden="true" />
            <span className="text-sm sm:text-base whitespace-nowrap">No Registration Required</span>
          </motion.div>
          <motion.div
            className="flex items-center gap-2"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
            role="listitem"
          >
            <Brain className="w-4 h-4 sm:w-5 sm:h-5 text-primary flex-shrink-0" aria-hidden="true" />
            <span className="text-sm sm:text-base whitespace-nowrap">AI-Moderated Discussions</span>
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Scroll indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        variants={heroVariants.item}
        role="button"
        aria-label="Scroll down to see more content"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            window.scrollTo({ top: window.innerHeight, behavior: 'smooth' });
          }
        }}
        onClick={() => window.scrollTo({ top: window.innerHeight, behavior: 'smooth' })}
        className="cursor-pointer focus-ring"
      >
        <motion.div
          className="w-6 h-10 border-2 border-primary/30 rounded-full flex justify-center"
          animate={{ y: [0, 10, 0] }}
          transition={{
            duration: 2,
            ease: "easeInOut",
            repeat: Infinity,
          }}
        >
          <motion.div
            className="w-1 h-3 bg-primary rounded-full mt-2"
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          />
        </motion.div>
      </motion.div>
    </main>
  );
};