import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import type { Id } from "./_generated/dataModel";

// Enable voice for an existing room (if not already enabled)
export const enableVoiceForRoom = mutation({
  args: {
    roomId: v.id("rooms"),
    creatorSessionId: v.string(),
  },
  handler: async (ctx, args) => {
    // Get the room
    const room = await ctx.db.get(args.roomId);
    if (!room) {
      throw new Error("Room not found");
    }

    // Verify the creator
    const creator = await ctx.db
      .query("participants")
      .withIndex("by_session", (q) => q.eq("sessionId", args.creatorSessionId))
      .filter((q) => q.eq(q.field("roomId"), args.roomId))
      .first();

    if (!creator) {
      throw new Error("Only room participants can enable voice");
    }

    // Check if voice is already enabled
    if (room.voiceRoomEnabled && room.voiceRoomStatus === "active") {
      const existingVoiceRoom = await ctx.db
        .query("voiceRooms")
        .withIndex("by_room", (q) => q.eq("roomId", args.roomId))
        .filter((q) => q.eq(q.field("status"), "active"))
        .first();
      
      if (existingVoiceRoom) {
        return existingVoiceRoom._id;
      }
    }

    // Enable voice for the room
    await ctx.db.patch(args.roomId, {
      allowVoice: true,
      voiceRoomEnabled: true,
      voiceRoomStatus: "active",
    });

    // Create voice room
    const livekitRoomName = `voice_${room.roomCode}_${Date.now()}`;
    const now = Date.now();

    const voiceRoomId = await ctx.db.insert("voiceRooms", {
      roomId: args.roomId,
      livekitRoomName,
      maxParticipants: room.maxVoiceParticipants,
      currentParticipants: 0,
      isRecording: false,
      status: "active",
      createdAt: now,
      startedAt: now,
      totalSpeakingTime: 0,
      totalParticipants: 0,
      peakParticipants: 0,
      aiVoiceModerationEnabled: room.aiModerationLevel !== "light",
      moderationEvents: 0,
      expiresAt: room.expiresAt,
    });

    return voiceRoomId;
  },
});

// Disable voice for a room
export const disableVoiceForRoom = mutation({
  args: {
    roomId: v.id("rooms"),
    creatorSessionId: v.string(),
  },
  handler: async (ctx, args) => {
    // Get the room
    const room = await ctx.db.get(args.roomId);
    if (!room) {
      throw new Error("Room not found");
    }

    // Verify the creator
    const creator = await ctx.db
      .query("participants")
      .withIndex("by_session", (q) => q.eq("sessionId", args.creatorSessionId))
      .filter((q) => q.eq(q.field("roomId"), args.roomId))
      .first();

    if (!creator) {
      throw new Error("Only room participants can disable voice");
    }

    // Get active voice room
    const voiceRoom = await ctx.db
      .query("voiceRooms")
      .withIndex("by_room", (q) => q.eq("roomId", args.roomId))
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();

    if (voiceRoom) {
      const now = Date.now();

      // End voice room
      await ctx.db.patch(voiceRoom._id, {
        status: "ended",
        endedAt: now,
      });

      // Remove all participants from voice
      const activeParticipants = await ctx.db
        .query("voiceParticipants")
        .withIndex("by_voice_room", (q) => q.eq("voiceRoomId", voiceRoom._id))
        .filter((q) => q.eq(q.field("isCurrentlyInVoice"), true))
        .collect();

      for (const participant of activeParticipants) {
        await ctx.db.patch(participant._id, {
          isCurrentlyInVoice: false,
          leftVoiceAt: now,
        });
      }
    }

    // Disable voice for the room
    await ctx.db.patch(args.roomId, {
      voiceRoomStatus: "inactive",
      currentVoiceParticipants: 0,
    });
  },
});

// Get comprehensive voice room analytics
export const getVoiceRoomAnalytics = query({
  args: {
    roomId: v.id("rooms"),
  },
  handler: async (ctx, args) => {
    // Get voice room
    const voiceRoom = await ctx.db
      .query("voiceRooms")
      .withIndex("by_room", (q) => q.eq("roomId", args.roomId))
      .first();

    if (!voiceRoom) {
      return null;
    }

    // Get all voice participants (current and past)
    const allVoiceParticipants = await ctx.db
      .query("voiceParticipants")
      .withIndex("by_voice_room", (q) => q.eq("voiceRoomId", voiceRoom._id))
      .collect();

    // Get voice events
    const voiceEvents = await ctx.db
      .query("voiceEvents")
      .withIndex("by_voice_room", (q) => q.eq("voiceRoomId", voiceRoom._id))
      .collect();

    // Calculate analytics
    const currentParticipants = allVoiceParticipants.filter(p => p.isCurrentlyInVoice);
    const totalSpeakingTime = allVoiceParticipants.reduce((sum, p) => sum + p.totalSpeakingTime, 0);
    const averageSpeakingTime = allVoiceParticipants.length > 0 
      ? totalSpeakingTime / allVoiceParticipants.length 
      : 0;

    // Speaking distribution
    const speakingDistribution = allVoiceParticipants.map(p => ({
      participantSessionId: p.participantSessionId,
      anonymousAlias: p.anonymousAlias,
      speakingTime: p.totalSpeakingTime,
      speakingTurns: p.speakingTurns,
      joinedAt: p.joinedVoiceAt,
      leftAt: p.leftVoiceAt,
      isCurrentlyInVoice: p.isCurrentlyInVoice,
    }));

    // Event counts
    const eventCounts = voiceEvents.reduce((counts, event) => {
      counts[event.eventType] = (counts[event.eventType] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    return {
      voiceRoom,
      currentParticipants: currentParticipants.length,
      totalParticipants: allVoiceParticipants.length,
      peakParticipants: voiceRoom.peakParticipants,
      totalSpeakingTime,
      averageSpeakingTime,
      speakingDistribution,
      eventCounts,
      sessionDuration: voiceRoom.endedAt
        ? voiceRoom.endedAt - (voiceRoom.startedAt || voiceRoom.createdAt)
        : Date.now() - (voiceRoom.startedAt || voiceRoom.createdAt),
      isActive: voiceRoom.status === "active",
    };
  },
});

// Update voice room settings
export const updateVoiceRoomSettings = mutation({
  args: {
    roomId: v.id("rooms"),
    settings: v.object({
      muteOnJoin: v.optional(v.boolean()),
      pushToTalk: v.optional(v.boolean()),
      noiseSuppressionEnabled: v.optional(v.boolean()),
      echoCancellationEnabled: v.optional(v.boolean()),
      autoGainControlEnabled: v.optional(v.boolean()),
      spatialAudioEnabled: v.optional(v.boolean()),
      recordVoiceSession: v.optional(v.boolean()),
    }),
    updaterSessionId: v.string(),
  },
  handler: async (ctx, args) => {
    // Get the room
    const room = await ctx.db.get(args.roomId);
    if (!room) {
      throw new Error("Room not found");
    }

    // Verify the updater is a participant
    const participant = await ctx.db
      .query("participants")
      .withIndex("by_session", (q) => q.eq("sessionId", args.updaterSessionId))
      .filter((q) => q.eq(q.field("roomId"), args.roomId))
      .first();

    if (!participant) {
      throw new Error("Only room participants can update voice settings");
    }

    // Update room voice settings
    const currentSettings = room.voiceRoomSettings;
    const newSettings = {
      ...currentSettings,
      ...args.settings,
    };

    await ctx.db.patch(args.roomId, {
      voiceRoomSettings: newSettings,
    });

    return newSettings;
  },
});

// Get voice room connection info for a participant
export const getVoiceConnectionInfo = query({
  args: {
    roomId: v.id("rooms"),
    participantSessionId: v.string(),
  },
  handler: async (ctx, args) => {
    // Get voice room
    const voiceRoom = await ctx.db
      .query("voiceRooms")
      .withIndex("by_room", (q) => q.eq("roomId", args.roomId))
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();

    if (!voiceRoom) {
      return null;
    }

    // Get participant's voice token
    const voiceToken = await ctx.db
      .query("voiceTokens")
      .withIndex("by_participant", (q) => q.eq("participantSessionId", args.participantSessionId))
      .filter((q) => q.eq(q.field("roomId"), args.roomId))
      .filter((q) => q.eq(q.field("isRevoked"), false))
      .filter((q) => q.gt(q.field("expiresAt"), Date.now()))
      .first();

    // Get voice participant info
    const voiceParticipant = await ctx.db
      .query("voiceParticipants")
      .withIndex("by_session", (q) => q.eq("participantSessionId", args.participantSessionId))
      .filter((q) => q.eq(q.field("voiceRoomId"), voiceRoom._id))
      .filter((q) => q.eq(q.field("isCurrentlyInVoice"), true))
      .first();

    return {
      voiceRoom: {
        id: voiceRoom._id,
        name: voiceRoom.livekitRoomName,
        status: voiceRoom.status,
        currentParticipants: voiceRoom.currentParticipants,
        maxParticipants: voiceRoom.maxParticipants,
      },
      token: voiceToken ? {
        token: voiceToken.livekitToken,
        identity: voiceToken.livekitIdentity,
        expiresAt: voiceToken.expiresAt,
        permissions: voiceToken.permissions,
      } : null,
      participant: voiceParticipant ? {
        id: voiceParticipant._id,
        isMuted: voiceParticipant.isMuted,
        isSpeaking: voiceParticipant.isSpeaking,
        audioEnabled: voiceParticipant.audioEnabled,
        canSpeak: voiceParticipant.canSpeak,
        joinedAt: voiceParticipant.joinedVoiceAt,
      } : null,
      canJoin: !voiceParticipant && voiceRoom.currentParticipants < voiceRoom.maxParticipants,
    };
  },
});
