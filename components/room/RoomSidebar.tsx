'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Users, 
  Mic, 
  MicOff, 
  Video, 
  VideoOff, 
  Settings,
  Crown,
  X,
  Volume2,
  VolumeX,
  Shield
} from 'lucide-react';
import { ParticipantsList } from './ParticipantsList';
import { generateAvatarUrl, formatRelativeTime } from '@/lib/identity';

interface RoomSidebarProps {
  room: {
    _id: string;
    roomCode: string;
    topic?: string;
    status: string;
    createdAt: number;
    currentParticipants: number;
    maxParticipants: number;
    allowVoice: boolean;
    allowVideo: boolean;
    aiModerationLevel: string;
  };
  participants: Array<{
    sessionId: string;
    anonymousAlias: string;
    avatarSeed: string;
    joinedAt: number;
    messageCount: number;
    isActive: boolean;
    isMuted: boolean;
  }>;
  currentParticipant?: {
    sessionId: string;
    anonymousAlias: string;
    avatarSeed: string;
    joinedAt: number;
    messageCount: number;
    isActive: boolean;
    isMuted: boolean;
  };
  onAudioToggle: () => void;
  onVideoToggle: () => void;
  isAudioEnabled: boolean;
  isVideoEnabled: boolean;
  onClose?: () => void;
}

export function RoomSidebar({
  room,
  participants,
  currentParticipant,
  onAudioToggle,
  onVideoToggle,
  isAudioEnabled,
  isVideoEnabled,
  onClose
}: RoomSidebarProps) {
  return (
    <div className="flex flex-col h-full">
      {/* Mobile Header */}
      {onClose && (
        <div className="flex items-center justify-between p-4 border-b border-border">
          <h2 className="font-semibold">Room Info</h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}

      <ScrollArea className="flex-1 scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent">
        <div className="p-4 space-y-4 pb-safe-area-inset-bottom">
          {/* Room Info - With Border */}
          <div className="border border-border rounded-lg p-3 space-y-2">
            <div className="space-y-1">
              <div className="text-sm font-medium truncate">
                {room.topic || 'Anonymous Discussion'}
              </div>
              <div className="text-xs text-muted-foreground">
                {room.roomCode} • {room.currentParticipants}/{room.maxParticipants} people
              </div>
            </div>

            {/* Features with AI Status */}
            <div className="flex flex-wrap gap-1">
              {room.allowVoice && (
                <Badge variant="outline" className="text-xs h-5">
                  <Mic className="h-2 w-2 mr-1" />
                  Voice
                </Badge>
              )}
              {room.allowVideo && (
                <Badge variant="outline" className="text-xs h-5">
                  <Video className="h-2 w-2 mr-1" />
                  Video
                </Badge>
              )}
              <Badge variant="outline" className="text-xs h-5">
                <Shield className="h-2 w-2 mr-1" />
                AI {room.aiModerationLevel}
              </Badge>
            </div>
          </div>

          {/* Your Identity - With Border */}
          {currentParticipant && (
            <div className="border border-border rounded-lg p-3">
              <div className="flex items-center gap-3">
                <img
                  src={generateAvatarUrl(currentParticipant.avatarSeed)}
                  alt={currentParticipant.anonymousAlias}
                  className="w-10 h-10 rounded-full"
                />
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm truncate">
                    {currentParticipant.anonymousAlias}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {currentParticipant.messageCount} messages
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Voice/Video Controls - Modern Compact with Border */}
          {(room.allowVoice || room.allowVideo) && (
            <div className="border border-border rounded-lg p-3 space-y-2">
              <div className="text-sm font-medium mb-2">Media</div>
              <div className="grid grid-cols-2 gap-2">
                {room.allowVoice && (
                  <Button
                    variant={isAudioEnabled ? "default" : "outline"}
                    size="sm"
                    onClick={onAudioToggle}
                    className="h-8 text-xs"
                  >
                    {isAudioEnabled ? (
                      <Mic className="h-3 w-3" />
                    ) : (
                      <MicOff className="h-3 w-3" />
                    )}
                  </Button>
                )}

                {room.allowVideo && (
                  <Button
                    variant={isVideoEnabled ? "default" : "outline"}
                    size="sm"
                    onClick={onVideoToggle}
                    className="h-8 text-xs"
                  >
                    {isVideoEnabled ? (
                      <Video className="h-3 w-3" />
                    ) : (
                      <VideoOff className="h-3 w-3" />
                    )}
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* Participants List - With Border */}
          <div className="border border-border rounded-lg p-3">
            <div className="text-sm font-medium mb-3 flex items-center gap-2">
              <Users className="h-4 w-4" />
              Participants ({participants.length})
            </div>
            <div className="max-h-48 overflow-y-auto">
              <ParticipantsList
                participants={participants}
                currentSessionId={currentParticipant?.sessionId}
              />
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
