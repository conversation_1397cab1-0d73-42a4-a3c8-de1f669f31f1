'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  Mic, 
  MicOff, 
  Phone, 
  PhoneOff, 
  Users, 
  Volume2, 
  VolumeX,
  Settings,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useVoiceRoom, VoiceParticipant } from '@/hooks/useVoiceRoom';
import { Id } from '@/convex/_generated/dataModel';
import { toast } from 'sonner';

interface VoiceRoomControlsProps {
  roomId: Id<'rooms'>;
  className?: string;
  compact?: boolean;
}

export function VoiceRoomControls({ 
  roomId, 
  className,
  compact = false 
}: VoiceRoomControlsProps) {
  const {
    voiceState,
    participants,
    error,
    joinVoiceRoom,
    leaveVoiceRoom,
    toggleMute,
    enableVoiceForRoom,
    retryConnection,
    isVoiceEnabled,
    isVoiceActive,
    canJoinVoice,
    clearError,
  } = useVoiceRoom(roomId);

  const [isEnabling, setIsEnabling] = useState(false);

  const handleJoinVoice = async () => {
    try {
      await joinVoiceRoom();
      toast.success('Joined voice room');
    } catch (err) {
      toast.error('Failed to join voice room');
    }
  };

  const handleLeaveVoice = async () => {
    try {
      await leaveVoiceRoom();
      toast.success('Left voice room');
    } catch (err) {
      toast.error('Failed to leave voice room');
    }
  };

  const handleToggleMute = async () => {
    try {
      const newMutedState = await toggleMute();
      toast.success(newMutedState ? 'Microphone muted' : 'Microphone unmuted');
    } catch (err) {
      toast.error('Failed to toggle microphone');
    }
  };

  const handleEnableVoice = async () => {
    try {
      setIsEnabling(true);
      await enableVoiceForRoom();
      toast.success('Voice room enabled');
    } catch (err) {
      toast.error('Failed to enable voice room');
    } finally {
      setIsEnabling(false);
    }
  };

  const handleRetryConnection = async () => {
    try {
      await retryConnection();
      toast.success('Reconnected to voice chat');
    } catch (err) {
      toast.error('Failed to reconnect to voice chat');
    }
  };

  // If voice is not enabled for the room
  if (!isVoiceEnabled) {
    return (
      <Card className={cn("border-border/50 bg-card/50 backdrop-blur-sm", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Volume2 className="h-4 w-4" />
            Voice Chat
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-center space-y-3">
            <p className="text-sm text-muted-foreground">
              Voice chat is not enabled for this room
            </p>
            <Button 
              onClick={handleEnableVoice}
              disabled={isEnabling}
              size="sm"
              className="w-full"
            >
              {isEnabling && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Enable Voice Chat
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Compact version for mobile/small screens
  if (compact) {
    return (
      <div className={cn("flex items-center gap-2 p-2 bg-card/50 backdrop-blur-sm rounded-lg border border-border/50", className)}>
        {voiceState.isConnected ? (
          <>
            <Button
              variant={voiceState.isMuted ? "outline" : "default"}
              size="sm"
              onClick={handleToggleMute}
              className="h-8 w-8 p-0"
            >
              {voiceState.isMuted ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleLeaveVoice}
              className="h-8 w-8 p-0"
            >
              <PhoneOff className="h-4 w-4" />
            </Button>
            <Badge variant="secondary" className="text-xs">
              {voiceState.participantCount}
            </Badge>
          </>
        ) : (
          <Button
            onClick={handleJoinVoice}
            disabled={!canJoinVoice || voiceState.isConnecting}
            size="sm"
            className="flex-1"
          >
            {voiceState.isConnecting ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Phone className="h-4 w-4 mr-2" />
            )}
            {voiceState.isConnecting ? 'Reconnecting...' : 'Join Voice'}
          </Button>
        )}
      </div>
    );
  }

  // Full version
  return (
    <Card className={cn("border-border/50 bg-card/50 backdrop-blur-sm", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <Volume2 className="h-4 w-4" />
            Voice Chat
            {isVoiceActive && (
              <Badge variant="secondary" className="text-xs">
                Active
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Users className="h-3 w-3" />
            {voiceState.participantCount}/{voiceState.maxParticipants}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0 space-y-4">
        {/* Reconnection Status */}
        {voiceState.isConnecting && !voiceState.isConnected && (
          <div className="flex items-center gap-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
            <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />
            <span className="text-sm text-blue-700">Reconnecting to voice chat...</span>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="flex items-center gap-2 p-2 bg-destructive/10 border border-destructive/20 rounded-md">
            <AlertCircle className="h-4 w-4 text-destructive" />
            <div className="flex-1">
              <span className="text-sm text-destructive">{error}</span>
            </div>
            <div className="flex items-center gap-1">
              {error.includes('reconnect') && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRetryConnection}
                  className="h-6 px-2 text-xs text-destructive hover:text-destructive"
                >
                  Retry
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={clearError}
                className="h-6 w-6 p-0"
              >
                ×
              </Button>
            </div>
          </div>
        )}

        {/* Connection Status */}
        {voiceState.isConnected ? (
          <div className="space-y-3">
            {/* Voice Controls */}
            <div className="flex items-center gap-2">
              <Button
                variant={voiceState.isMuted ? "outline" : "default"}
                size="sm"
                onClick={handleToggleMute}
                className="flex-1"
              >
                {voiceState.isMuted ? (
                  <>
                    <MicOff className="h-4 w-4 mr-2" />
                    Unmute
                  </>
                ) : (
                  <>
                    <Mic className="h-4 w-4 mr-2" />
                    Mute
                  </>
                )}
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={handleLeaveVoice}
              >
                <PhoneOff className="h-4 w-4 mr-2" />
                Leave
              </Button>
            </div>

            {/* Connection Quality */}
            {voiceState.connectionQuality && (
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  voiceState.connectionQuality === 'excellent' && "bg-green-500",
                  voiceState.connectionQuality === 'good' && "bg-yellow-500",
                  voiceState.connectionQuality === 'poor' && "bg-red-500"
                )} />
                Connection: {voiceState.connectionQuality}
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-3">
            <Button
              onClick={handleJoinVoice}
              disabled={!canJoinVoice || voiceState.isConnecting}
              className="w-full"
            >
              {voiceState.isConnecting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Reconnecting to voice...
                </>
              ) : (
                <>
                  <Phone className="h-4 w-4 mr-2" />
                  Join Voice Chat
                </>
              )}
            </Button>
            
            {!canJoinVoice && voiceState.participantCount >= voiceState.maxParticipants && (
              <p className="text-xs text-muted-foreground text-center">
                Voice room is full
              </p>
            )}
          </div>
        )}

        {/* Voice Status Summary */}
        {participants.length > 0 && (
          <>
            <Separator />
            <div className="text-xs text-muted-foreground text-center">
              {participants.length} participant{participants.length !== 1 ? 's' : ''} in voice
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}


