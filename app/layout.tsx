import type { <PERSON>ada<PERSON> } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import { ThemeProvider } from "next-themes";
import { ConvexClientProvider } from "@/components/providers/convex-provider";
import { Toaster } from "sonner";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "VeilMind - AI-Powered Anonymous Conferencing",
  description: "Speak your truth without consequences. Join meaningful conversations with complete anonymity and AI-powered moderation. Create anonymous rooms instantly.",
  keywords: ["anonymous conferencing", "AI moderation", "private discussions", "secure meetings", "anonymous feedback"],
  authors: [{ name: "VeilMind" }],
  creator: "VeilMind",
  publisher: "VeilMind",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://veilmind.com"),
  openGraph: {
    title: "VeilMind - AI-Powered Anonymous Conferencing",
    description: "Speak your truth without consequences. Join meaningful conversations with complete anonymity and AI-powered moderation.",
    url: "https://veilmind.com",
    siteName: "VeilMind",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "VeilMind - Anonymous Conferencing Platform",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "VeilMind - AI-Powered Anonymous Conferencing",
    description: "Speak your truth without consequences. Join meaningful conversations with complete anonymity and AI-powered moderation.",
    images: ["/og-image.jpg"],
    creator: "@veilmind",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} font-sans antialiased`}
      >
        <ConvexClientProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="dark"
            enableSystem={false}
            disableTransitionOnChange
          >
            {children}
            <Toaster
              theme="dark"
              position="top-right"
              richColors
              closeButton
            />
          </ThemeProvider>
        </ConvexClientProvider>
      </body>
    </html>
  );
}
