import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";

// Environment variables for LiveKit
const LIVEKIT_WS_URL = process.env.LIVEKIT_WS_URL;

// Create a LiveKit voice room when a regular room enables voice
export const createVoiceRoom = mutation({
  args: {
    roomId: v.id("rooms"),
    maxParticipants: v.optional(v.number()),
    recordingEnabled: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Get the main room
    const room = await ctx.db.get(args.roomId);
    if (!room) {
      throw new Error("Room not found");
    }

    // Check if voice room already exists
    const existingVoiceRoom = await ctx.db
      .query("voiceRooms")
      .withIndex("by_room", (q) => q.eq("roomId", args.roomId))
      .filter((q) => q.neq(q.field("status"), "ended"))
      .first();

    if (existingVoiceRoom) {
      return existingVoiceRoom._id;
    }

    // Generate unique LiveKit room name
    const livekitRoomName = `voice_${room.roomCode}_${Date.now()}`;
    
    const now = Date.now();
    const expiresAt = room.expiresAt; // Same expiration as main room

    // Create voice room record
    const voiceRoomId = await ctx.db.insert("voiceRooms", {
      roomId: args.roomId,
      livekitRoomName,
      maxParticipants: args.maxParticipants ?? room.maxVoiceParticipants,
      currentParticipants: 0,
      isRecording: args.recordingEnabled ?? false,
      status: "active",
      createdAt: now,
      startedAt: now,
      totalSpeakingTime: 0,
      totalParticipants: 0,
      peakParticipants: 0,
      aiVoiceModerationEnabled: room.aiModerationLevel !== "light",
      moderationEvents: 0,
      expiresAt,
    });

    // Update main room voice status
    await ctx.db.patch(args.roomId, {
      voiceRoomStatus: "active",
    });

    return voiceRoomId;
  },
});

// Store voice token in database (helper mutation)
export const storeVoiceToken = mutation({
  args: {
    participantSessionId: v.string(),
    roomId: v.id("rooms"),
    voiceRoomId: v.id("voiceRooms"),
    livekitToken: v.string(),
    livekitIdentity: v.string(),
    permissions: v.object({
      canPublish: v.boolean(),
      canSubscribe: v.boolean(),
      canPublishData: v.boolean(),
      canModerate: v.boolean(),
    }),
    issuedAt: v.number(),
    expiresAt: v.number(),
  },
  handler: async (ctx, args) => {
    // Store token record
    const tokenId = await ctx.db.insert("voiceTokens", {
      participantSessionId: args.participantSessionId,
      roomId: args.roomId,
      voiceRoomId: args.voiceRoomId,
      livekitToken: args.livekitToken,
      livekitIdentity: args.livekitIdentity,
      permissions: args.permissions,
      issuedAt: args.issuedAt,
      expiresAt: args.expiresAt,
      isRevoked: false,
    });

    return tokenId;
  },
});

// Join voice room - creates participant record and updates counts
export const joinVoiceRoom = mutation({
  args: {
    roomId: v.id("rooms"),
    participantSessionId: v.string(),
  },
  handler: async (ctx, args) => {
    // Get the voice room
    const voiceRoom = await ctx.db
      .query("voiceRooms")
      .withIndex("by_room", (q) => q.eq("roomId", args.roomId))
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();

    if (!voiceRoom) {
      throw new Error("Voice room not found or not active");
    }

    // Get participant info
    const participant = await ctx.db
      .query("participants")
      .withIndex("by_session", (q) => q.eq("sessionId", args.participantSessionId))
      .filter((q) => q.eq(q.field("roomId"), args.roomId))
      .first();

    if (!participant) {
      throw new Error("Participant not found");
    }

    // Check if already in voice room
    const existingVoiceParticipant = await ctx.db
      .query("voiceParticipants")
      .withIndex("by_session", (q) => q.eq("participantSessionId", args.participantSessionId))
      .filter((q) => q.eq(q.field("voiceRoomId"), voiceRoom._id))
      .filter((q) => q.eq(q.field("isCurrentlyInVoice"), true))
      .first();

    if (existingVoiceParticipant) {
      return existingVoiceParticipant._id;
    }

    // Check room capacity
    if (voiceRoom.currentParticipants >= voiceRoom.maxParticipants) {
      throw new Error("Voice room is full");
    }

    const now = Date.now();
    const livekitIdentity = `${args.participantSessionId}_${now}`;

    // Create voice participant record
    const voiceParticipantId = await ctx.db.insert("voiceParticipants", {
      roomId: args.roomId,
      voiceRoomId: voiceRoom._id,
      participantSessionId: args.participantSessionId,
      anonymousAlias: participant.anonymousAlias,
      livekitIdentity,
      joinedVoiceAt: now,
      isCurrentlyInVoice: true,
      isMuted: voiceRoom.maxParticipants > 1, // Auto-mute in group calls
      isSpeaking: false,
      audioEnabled: true,
      videoEnabled: false,
      screenShareEnabled: false,
      totalSpeakingTime: 0,
      speakingTurns: 0,
      averageSpeakingTurnDuration: 0,
      longestSpeakingTurn: 0,
      canSpeak: true,
      canModerate: false,
      temporarilyMuted: false,
    });

    // Update voice room participant count
    await ctx.db.patch(voiceRoom._id, {
      currentParticipants: voiceRoom.currentParticipants + 1,
      totalParticipants: Math.max(voiceRoom.totalParticipants, voiceRoom.currentParticipants + 1),
      peakParticipants: Math.max(voiceRoom.peakParticipants, voiceRoom.currentParticipants + 1),
    });

    // Update main room voice participant count
    const room = await ctx.db.get(args.roomId);
    if (room) {
      await ctx.db.patch(args.roomId, {
        currentVoiceParticipants: room.currentVoiceParticipants + 1,
      });
    }

    // Update participant voice status
    await ctx.db.patch(participant._id, {
      isCurrentlyInVoice: true,
      joinedVoiceAt: now,
    });

    // Log voice event
    await ctx.db.insert("voiceEvents", {
      roomId: args.roomId,
      voiceRoomId: voiceRoom._id,
      participantSessionId: args.participantSessionId,
      eventType: "joined",
      createdAt: now,
      expiresAt: now + (7 * 24 * 60 * 60 * 1000), // 7 days
    });

    return voiceParticipantId;
  },
});

// Leave voice room
export const leaveVoiceRoom = mutation({
  args: {
    roomId: v.id("rooms"),
    participantSessionId: v.string(),
  },
  handler: async (ctx, args) => {
    // Find active voice participant
    const voiceParticipant = await ctx.db
      .query("voiceParticipants")
      .withIndex("by_session", (q) => q.eq("participantSessionId", args.participantSessionId))
      .filter((q) => q.eq(q.field("roomId"), args.roomId))
      .filter((q) => q.eq(q.field("isCurrentlyInVoice"), true))
      .first();

    if (!voiceParticipant) {
      return; // Already left or never joined
    }

    const now = Date.now();

    // Update voice participant record
    await ctx.db.patch(voiceParticipant._id, {
      isCurrentlyInVoice: false,
      leftVoiceAt: now,
    });

    // Update voice room participant count
    const voiceRoom = await ctx.db.get(voiceParticipant.voiceRoomId);
    if (voiceRoom) {
      await ctx.db.patch(voiceRoom._id, {
        currentParticipants: Math.max(0, voiceRoom.currentParticipants - 1),
      });
    }

    // Update main room voice participant count
    const room = await ctx.db.get(args.roomId);
    if (room) {
      await ctx.db.patch(args.roomId, {
        currentVoiceParticipants: Math.max(0, room.currentVoiceParticipants - 1),
      });
    }

    // Update participant voice status
    const participant = await ctx.db
      .query("participants")
      .withIndex("by_session", (q) => q.eq("sessionId", args.participantSessionId))
      .filter((q) => q.eq(q.field("roomId"), args.roomId))
      .first();

    if (participant) {
      const totalVoiceTime = participant.totalVoiceTime + 
        (voiceParticipant.joinedVoiceAt ? now - voiceParticipant.joinedVoiceAt : 0);
      
      await ctx.db.patch(participant._id, {
        isCurrentlyInVoice: false,
        leftVoiceAt: now,
        totalVoiceTime,
      });
    }

    // Log voice event
    await ctx.db.insert("voiceEvents", {
      roomId: args.roomId,
      voiceRoomId: voiceParticipant.voiceRoomId,
      participantSessionId: args.participantSessionId,
      eventType: "left",
      eventData: {
        duration: voiceParticipant.joinedVoiceAt ? now - voiceParticipant.joinedVoiceAt : 0,
      },
      createdAt: now,
      expiresAt: now + (7 * 24 * 60 * 60 * 1000), // 7 days
    });

    // Revoke any active tokens
    const activeTokens = await ctx.db
      .query("voiceTokens")
      .withIndex("by_participant", (q) => q.eq("participantSessionId", args.participantSessionId))
      .filter((q) => q.eq(q.field("roomId"), args.roomId))
      .filter((q) => q.eq(q.field("isRevoked"), false))
      .collect();

    for (const token of activeTokens) {
      await ctx.db.patch(token._id, {
        isRevoked: true,
        revokedAt: now,
        revokeReason: "participant_left",
      });
    }
  },
});

// Get voice room status and participants
export const getVoiceRoomStatus = query({
  args: {
    roomId: v.id("rooms"),
  },
  handler: async (ctx, args) => {
    // Get the voice room
    const voiceRoom = await ctx.db
      .query("voiceRooms")
      .withIndex("by_room", (q) => q.eq("roomId", args.roomId))
      .filter((q) => q.neq(q.field("status"), "ended"))
      .first();

    if (!voiceRoom) {
      return null;
    }

    // Get active voice participants
    const voiceParticipants = await ctx.db
      .query("voiceParticipants")
      .withIndex("by_voice_room", (q) => q.eq("voiceRoomId", voiceRoom._id))
      .filter((q) => q.eq(q.field("isCurrentlyInVoice"), true))
      .collect();

    return {
      voiceRoom,
      participants: voiceParticipants,
      participantCount: voiceParticipants.length,
      isActive: voiceRoom.status === "active",
      canJoin: voiceParticipants.length < voiceRoom.maxParticipants,
    };
  },
});

// Get voice participants for a room
export const getVoiceParticipants = query({
  args: {
    roomId: v.id("rooms"),
  },
  handler: async (ctx, args) => {
    // Get the voice room
    const voiceRoom = await ctx.db
      .query("voiceRooms")
      .withIndex("by_room", (q) => q.eq("roomId", args.roomId))
      .filter((q) => q.neq(q.field("status"), "ended"))
      .first();

    if (!voiceRoom) {
      return [];
    }

    // Get active voice participants
    const voiceParticipants = await ctx.db
      .query("voiceParticipants")
      .withIndex("by_voice_room", (q) => q.eq("voiceRoomId", voiceRoom._id))
      .filter((q) => q.eq(q.field("isCurrentlyInVoice"), true))
      .collect();

    return voiceParticipants;
  },
});

// Toggle mute status for a voice participant
export const toggleVoiceMute = mutation({
  args: {
    roomId: v.id("rooms"),
    participantSessionId: v.string(),
    muted: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Find active voice participant
    const voiceParticipant = await ctx.db
      .query("voiceParticipants")
      .withIndex("by_session", (q) => q.eq("participantSessionId", args.participantSessionId))
      .filter((q) => q.eq(q.field("roomId"), args.roomId))
      .filter((q) => q.eq(q.field("isCurrentlyInVoice"), true))
      .first();

    if (!voiceParticipant) {
      throw new Error("Voice participant not found");
    }

    const newMutedState = args.muted ?? !voiceParticipant.isMuted;
    const now = Date.now();

    // Update mute status
    await ctx.db.patch(voiceParticipant._id, {
      isMuted: newMutedState,
    });

    // Log voice event
    await ctx.db.insert("voiceEvents", {
      roomId: args.roomId,
      voiceRoomId: voiceParticipant.voiceRoomId,
      participantSessionId: args.participantSessionId,
      eventType: newMutedState ? "muted" : "unmuted",
      createdAt: now,
      expiresAt: now + (7 * 24 * 60 * 60 * 1000), // 7 days
    });

    return newMutedState;
  },
});

// Update speaking status (called by LiveKit events)
export const updateSpeakingStatus = mutation({
  args: {
    roomId: v.id("rooms"),
    participantSessionId: v.string(),
    isSpeaking: v.boolean(),
  },
  handler: async (ctx, args) => {
    // Find active voice participant
    const voiceParticipant = await ctx.db
      .query("voiceParticipants")
      .withIndex("by_session", (q) => q.eq("participantSessionId", args.participantSessionId))
      .filter((q) => q.eq(q.field("roomId"), args.roomId))
      .filter((q) => q.eq(q.field("isCurrentlyInVoice"), true))
      .first();

    if (!voiceParticipant) {
      return;
    }

    const now = Date.now();

    // Update speaking status
    await ctx.db.patch(voiceParticipant._id, {
      isSpeaking: args.isSpeaking,
    });

    // Log voice event
    await ctx.db.insert("voiceEvents", {
      roomId: args.roomId,
      voiceRoomId: voiceParticipant.voiceRoomId,
      participantSessionId: args.participantSessionId,
      eventType: args.isSpeaking ? "started_speaking" : "stopped_speaking",
      createdAt: now,
      expiresAt: now + (7 * 24 * 60 * 60 * 1000), // 7 days
    });
  },
});

// End voice room
export const endVoiceRoom = mutation({
  args: {
    roomId: v.id("rooms"),
  },
  handler: async (ctx, args) => {
    // Get the voice room
    const voiceRoom = await ctx.db
      .query("voiceRooms")
      .withIndex("by_room", (q) => q.eq("roomId", args.roomId))
      .filter((q) => q.neq(q.field("status"), "ended"))
      .first();

    if (!voiceRoom) {
      return;
    }

    const now = Date.now();

    // End voice room
    await ctx.db.patch(voiceRoom._id, {
      status: "ended",
      endedAt: now,
    });

    // Update main room voice status
    await ctx.db.patch(args.roomId, {
      voiceRoomStatus: "inactive",
      currentVoiceParticipants: 0,
    });

    // Mark all voice participants as left
    const activeParticipants = await ctx.db
      .query("voiceParticipants")
      .withIndex("by_voice_room", (q) => q.eq("voiceRoomId", voiceRoom._id))
      .filter((q) => q.eq(q.field("isCurrentlyInVoice"), true))
      .collect();

    for (const participant of activeParticipants) {
      await ctx.db.patch(participant._id, {
        isCurrentlyInVoice: false,
        leftVoiceAt: now,
      });

      // Log voice event
      await ctx.db.insert("voiceEvents", {
        roomId: args.roomId,
        voiceRoomId: voiceRoom._id,
        participantSessionId: participant.participantSessionId,
        eventType: "left",
        eventData: {
          reason: "room_ended",
          duration: participant.joinedVoiceAt ? now - participant.joinedVoiceAt : 0,
        },
        createdAt: now,
        expiresAt: now + (7 * 24 * 60 * 60 * 1000), // 7 days
      });
    }

    // Revoke all active tokens
    const activeTokens = await ctx.db
      .query("voiceTokens")
      .withIndex("by_room", (q) => q.eq("roomId", args.roomId))
      .filter((q) => q.eq(q.field("isRevoked"), false))
      .collect();

    for (const token of activeTokens) {
      await ctx.db.patch(token._id, {
        isRevoked: true,
        revokedAt: now,
        revokeReason: "room_ended",
      });
    }
  },
});
