import { Variants } from "framer-motion";

// Check if user prefers reduced motion
export const prefersReducedMotion = () => {
  if (typeof window === "undefined") return false;
  return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
};

// Base animation variants with reduced motion support
export const createAnimationVariants = (reducedMotion = false): Record<string, Variants> => ({
  // Fade animations
  fadeIn: {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: {
        duration: reducedMotion ? 0.1 : 0.6,
        ease: "easeOut",
      },
    },
  },

  fadeInUp: {
    hidden: { opacity: 0, y: reducedMotion ? 0 : 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: reducedMotion ? 0.1 : 0.6,
        ease: "easeOut",
      },
    },
  },

  fadeInDown: {
    hidden: { opacity: 0, y: reducedMotion ? 0 : -20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: reducedMotion ? 0.1 : 0.6,
        ease: "easeOut",
      },
    },
  },

  fadeInLeft: {
    hidden: { opacity: 0, x: reducedMotion ? 0 : -20 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: {
        duration: reducedMotion ? 0.1 : 0.6,
        ease: "easeOut",
      },
    },
  },

  fadeInRight: {
    hidden: { opacity: 0, x: reducedMotion ? 0 : 20 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: {
        duration: reducedMotion ? 0.1 : 0.6,
        ease: "easeOut",
      },
    },
  },

  // Scale animations
  scaleIn: {
    hidden: { opacity: 0, scale: reducedMotion ? 1 : 0.8 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: {
        duration: reducedMotion ? 0.1 : 0.5,
        ease: "easeOut",
      },
    },
  },

  // Stagger container
  staggerContainer: {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: reducedMotion ? 0 : 0.1,
        delayChildren: reducedMotion ? 0 : 0.1,
      },
    },
  },

  // Stagger items
  staggerItem: {
    hidden: { opacity: 0, y: reducedMotion ? 0 : 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: reducedMotion ? 0.1 : 0.5,
        ease: "easeOut",
      },
    },
  },

  // Slide animations
  slideInLeft: {
    hidden: { x: reducedMotion ? 0 : "-100%" },
    visible: { 
      x: 0,
      transition: {
        duration: reducedMotion ? 0.1 : 0.6,
        ease: "easeOut",
      },
    },
  },

  slideInRight: {
    hidden: { x: reducedMotion ? 0 : "100%" },
    visible: { 
      x: 0,
      transition: {
        duration: reducedMotion ? 0.1 : 0.6,
        ease: "easeOut",
      },
    },
  },

  // Hover animations
  hoverScale: {
    rest: { scale: 1 },
    hover: { 
      scale: reducedMotion ? 1 : 1.05,
      transition: {
        duration: reducedMotion ? 0 : 0.2,
        ease: "easeOut",
      },
    },
  },

  hoverLift: {
    rest: { y: 0 },
    hover: { 
      y: reducedMotion ? 0 : -4,
      transition: {
        duration: reducedMotion ? 0 : 0.2,
        ease: "easeOut",
      },
    },
  },
});

// Default variants (automatically detects reduced motion preference)
export const animationVariants = createAnimationVariants(prefersReducedMotion());

// Specific variants for different use cases
export const heroVariants = {
  container: {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: prefersReducedMotion() ? 0 : 0.2,
        delayChildren: prefersReducedMotion() ? 0 : 0.1,
      },
    },
  },
  item: {
    hidden: { opacity: 0, y: prefersReducedMotion() ? 0 : 30 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: prefersReducedMotion() ? 0.1 : 0.8,
        ease: "easeOut",
      },
    },
  },
};

export const cardVariants = {
  container: {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: prefersReducedMotion() ? 0 : 0.1,
      },
    },
  },
  item: {
    hidden: { opacity: 0, y: prefersReducedMotion() ? 0 : 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: prefersReducedMotion() ? 0.1 : 0.5,
        ease: "easeOut",
      },
    },
  },
};

// Utility function to get appropriate transition props
export const getTransitionProps = (reducedMotion?: boolean) => {
  const shouldReduce = reducedMotion ?? prefersReducedMotion();
  
  return {
    initial: "hidden",
    whileInView: "visible",
    viewport: { once: true, margin: "-50px" },
    transition: {
      duration: shouldReduce ? 0.1 : 0.6,
      ease: "easeOut",
    },
  };
};
