// Production-ready loading components
// Comprehensive loading states with accessibility and animations

'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Loader2, Wifi, WifiOff, AlertCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

// ============================================================================
// LOADING COMPONENT TYPES
// ============================================================================

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  'aria-label'?: string;
}

interface LoadingStateProps {
  isLoading: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  delay?: number;
  className?: string;
}

interface SkeletonProps {
  className?: string;
  variant?: 'text' | 'circular' | 'rectangular';
  width?: string | number;
  height?: string | number;
  lines?: number;
}

// ============================================================================
// LOADING SPINNER
// ============================================================================

export function LoadingSpinner({ 
  size = 'md', 
  className,
  'aria-label': ariaLabel = 'Loading...'
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
  };

  return (
    <Loader2 
      className={cn('animate-spin text-muted-foreground', sizeClasses[size], className)}
      aria-label={ariaLabel}
      role="status"
    />
  );
}

// ============================================================================
// LOADING STATE WRAPPER
// ============================================================================

export function LoadingState({ 
  isLoading, 
  children, 
  fallback, 
  delay = 200,
  className 
}: LoadingStateProps) {
  const [showLoading, setShowLoading] = React.useState(false);

  React.useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (isLoading) {
      timeoutId = setTimeout(() => setShowLoading(true), delay);
    } else {
      setShowLoading(false);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [isLoading, delay]);

  if (isLoading && showLoading) {
    return (
      <div className={cn('flex items-center justify-center', className)}>
        {fallback || <LoadingSpinner />}
      </div>
    );
  }

  return <>{children}</>;
}

// ============================================================================
// SKELETON COMPONENTS
// ============================================================================

export function Skeleton({ 
  className, 
  variant = 'rectangular',
  width,
  height,
  lines = 1,
  ...props 
}: SkeletonProps) {
  const baseClasses = 'animate-pulse bg-muted rounded';
  
  const variantClasses = {
    text: 'h-4',
    circular: 'rounded-full',
    rectangular: 'rounded',
  };

  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
  };

  if (variant === 'text' && lines > 1) {
    return (
      <div className="space-y-2">
        {Array.from({ length: lines }, (_, i) => (
          <div
            key={i}
            className={cn(
              baseClasses,
              variantClasses[variant],
              i === lines - 1 ? 'w-3/4' : 'w-full',
              className
            )}
            style={style}
            {...props}
          />
        ))}
      </div>
    );
  }

  return (
    <div
      className={cn(baseClasses, variantClasses[variant], className)}
      style={style}
      {...props}
    />
  );
}

// ============================================================================
// SPECIALIZED LOADING COMPONENTS
// ============================================================================

export function PageLoading({ message = 'Loading page...' }: { message?: string }) {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="text-center space-y-4">
        <LoadingSpinner size="xl" />
        <p className="text-muted-foreground" aria-live="polite">
          {message}
        </p>
      </div>
    </div>
  );
}

export function SectionLoading({ 
  message = 'Loading...', 
  className 
}: { 
  message?: string; 
  className?: string; 
}) {
  return (
    <div className={cn('flex items-center justify-center p-8', className)}>
      <div className="text-center space-y-3">
        <LoadingSpinner size="lg" />
        <p className="text-sm text-muted-foreground" aria-live="polite">
          {message}
        </p>
      </div>
    </div>
  );
}

export function InlineLoading({ 
  message = 'Loading...', 
  size = 'sm' 
}: { 
  message?: string; 
  size?: 'sm' | 'md'; 
}) {
  return (
    <div className="flex items-center gap-2">
      <LoadingSpinner size={size} />
      <span className="text-sm text-muted-foreground">{message}</span>
    </div>
  );
}

// ============================================================================
// BUTTON LOADING STATES
// ============================================================================

interface LoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean;
  loadingText?: string;
  children: React.ReactNode;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
}

export function LoadingButton({ 
  isLoading = false,
  loadingText,
  children,
  disabled,
  className,
  ...props 
}: LoadingButtonProps) {
  return (
    <Button
      disabled={disabled || isLoading}
      className={className}
      {...props}
    >
      <AnimatePresence mode="wait">
        {isLoading ? (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex items-center gap-2"
          >
            <LoadingSpinner size="sm" />
            {loadingText && <span>{loadingText}</span>}
          </motion.div>
        ) : (
          <motion.div
            key="content"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    </Button>
  );
}

// ============================================================================
// CONNECTION STATUS
// ============================================================================

interface ConnectionStatusProps {
  isConnected: boolean;
  isReconnecting?: boolean;
  onRetry?: () => void;
  className?: string;
}

export function ConnectionStatus({ 
  isConnected, 
  isReconnecting = false,
  onRetry,
  className 
}: ConnectionStatusProps) {
  if (isConnected && !isReconnecting) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={cn(
        'fixed top-4 left-1/2 transform -translate-x-1/2 z-50',
        className
      )}
    >
      <Card className="border-destructive/20 bg-destructive/5">
        <CardContent className="flex items-center gap-3 p-3">
          {isReconnecting ? (
            <>
              <LoadingSpinner size="sm" />
              <span className="text-sm font-medium">Reconnecting...</span>
            </>
          ) : (
            <>
              <WifiOff className="w-4 h-4 text-destructive" />
              <span className="text-sm font-medium text-destructive">
                Connection lost
              </span>
              {onRetry && (
                <Button size="sm" variant="outline" onClick={onRetry}>
                  Retry
                </Button>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}

// ============================================================================
// SKELETON LAYOUTS
// ============================================================================

export function MessageSkeleton() {
  return (
    <div className="flex gap-3 p-4">
      <Skeleton variant="circular" width={32} height={32} />
      <div className="flex-1 space-y-2">
        <Skeleton variant="text" width="25%" />
        <Skeleton variant="text" lines={2} />
      </div>
    </div>
  );
}

export function ParticipantSkeleton() {
  return (
    <div className="flex items-center gap-3 p-2">
      <Skeleton variant="circular" width={32} height={32} />
      <div className="flex-1">
        <Skeleton variant="text" width="60%" />
      </div>
    </div>
  );
}

export function RoomHeaderSkeleton() {
  return (
    <div className="flex items-center justify-between p-4 border-b">
      <div className="flex items-center gap-3">
        <Skeleton variant="text" width={120} />
        <Skeleton variant="rectangular" width={60} height={24} />
      </div>
      <div className="flex gap-2">
        <Skeleton variant="rectangular" width={80} height={32} />
        <Skeleton variant="rectangular" width={80} height={32} />
      </div>
    </div>
  );
}

// ============================================================================
// PROGRESSIVE LOADING
// ============================================================================

interface ProgressiveLoadingProps {
  stages: Array<{
    message: string;
    duration?: number;
  }>;
  onComplete?: () => void;
  className?: string;
}

export function ProgressiveLoading({ 
  stages, 
  onComplete,
  className 
}: ProgressiveLoadingProps) {
  const [currentStage, setCurrentStage] = React.useState(0);

  React.useEffect(() => {
    if (currentStage >= stages.length) {
      onComplete?.();
      return;
    }

    const stage = stages[currentStage];
    const duration = stage.duration || 1000;

    const timeoutId = setTimeout(() => {
      setCurrentStage(prev => prev + 1);
    }, duration);

    return () => clearTimeout(timeoutId);
  }, [currentStage, stages, onComplete]);

  if (currentStage >= stages.length) {
    return null;
  }

  const currentMessage = stages[currentStage]?.message || 'Loading...';

  return (
    <div className={cn('text-center space-y-4', className)}>
      <LoadingSpinner size="lg" />
      <motion.p
        key={currentStage}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        className="text-muted-foreground"
        aria-live="polite"
      >
        {currentMessage}
      </motion.p>
      <div className="flex justify-center gap-1">
        {stages.map((_, index) => (
          <div
            key={index}
            className={cn(
              'w-2 h-2 rounded-full transition-colors',
              index <= currentStage ? 'bg-primary' : 'bg-muted'
            )}
          />
        ))}
      </div>
    </div>
  );
}
