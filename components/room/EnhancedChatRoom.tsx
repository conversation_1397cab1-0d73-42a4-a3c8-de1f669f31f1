'use client';

import { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { ChatInterface } from './ChatInterface';
import { MessageInput } from './MessageInput';
import { MentionNotifications } from './MentionNotifications';
import { useEnhancedMessaging } from '@/hooks/useEnhancedMessaging';
import { useAnonymousIdentity } from '@/hooks/useAnonymousIdentity';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  MessageSquare, 
  Users, 
  Settings,
  AtSign,
  Reply
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface EnhancedChatRoomProps {
  roomId: Id<'rooms'>;
  className?: string;
}

export function EnhancedChatRoom({ roomId, className }: EnhancedChatRoomProps) {
  const { identity } = useAnonymousIdentity();
  const [showSidebar, setShowSidebar] = useState(false);

  // Queries
  const messages = useQuery(api.messages.subscribeToRoomMessages, { roomId });
  const participants = useQuery(api.rooms.getRoomParticipants, { roomId });
  const room = useQuery(api.rooms.getRoomById, { roomId });

  // Enhanced messaging hook
  const {
    toggleReaction,
    editMessage,
    deleteMessage,
    startReply,
    cancelReply,
    replyingTo,
    unreadMentionCount,
    markMentionsAsRead,
    isLoading,
    error
  } = useEnhancedMessaging(roomId);

  // Find current participant
  const currentParticipant = participants?.find(p => p.sessionId === identity?.sessionId);
  
  // Transform messages to include enhanced data
  const enhancedMessages = messages?.map(msg => ({
    ...msg,
    threadDepth: msg.threadDepth || 0,
    threadReplyCount: msg.threadReplyCount || 0,
    mentions: msg.mentions || [],
    hasMentions: msg.hasMentions || false,
    engagementMetrics: msg.engagementMetrics || {
      totalReactions: 0,
      uniqueReactors: 0,
      replyCount: 0,
      mentionCount: 0,
      viewCount: 0,
    },
  })) || [];

  const handleReaction = async (messageId: string, emoji: string) => {
    try {
      await toggleReaction(messageId, emoji);
    } catch (error) {
      console.error('Failed to toggle reaction:', error);
    }
  };

  const handleReply = (messageId: string, content: string, author: string) => {
    startReply(messageId, content, author);
  };

  const handleEdit = async (messageId: string, newContent: string) => {
    try {
      await editMessage(messageId, newContent);
    } catch (error) {
      console.error('Failed to edit message:', error);
    }
  };

  const handleDelete = async (messageId: string) => {
    try {
      await deleteMessage(messageId);
    } catch (error) {
      console.error('Failed to delete message:', error);
    }
  };

  const handleMentionClick = (participantSessionId: string) => {
    // Could scroll to participant or show profile
    console.log('Mention clicked:', participantSessionId);
  };

  const handleMessageSent = () => {
    // Mark mentions as read when user sends a message
    if (unreadMentionCount > 0) {
      markMentionsAsRead();
    }
  };

  if (!room || !identity) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center space-y-2">
          <MessageSquare className="h-8 w-8 mx-auto text-muted-foreground" />
          <div className="text-sm text-muted-foreground">Loading chat room...</div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full bg-background", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-border bg-background/95 backdrop-blur-sm">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5 text-primary" />
            <div>
              <div className="font-medium text-sm">
                {room.topic || 'Anonymous Chat'}
              </div>
              <div className="text-xs text-muted-foreground">
                Room: {room.roomCode}
              </div>
            </div>
          </div>
          
          {/* Reply indicator */}
          {replyingTo && (
            <Badge variant="secondary" className="text-xs">
              <Reply className="h-3 w-3 mr-1" />
              Replying
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* Mention notifications */}
          {identity && (
            <MentionNotifications
              roomId={roomId}
              participantSessionId={identity.sessionId}
            />
          )}

          {/* Participants count */}
          <Button
            variant="ghost"
            size="sm"
            className="h-8 px-2 text-xs"
            onClick={() => setShowSidebar(!showSidebar)}
          >
            <Users className="h-4 w-4 mr-1" />
            {participants?.length || 0}
          </Button>

          {/* Settings */}
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Main chat area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Messages */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Chat interface */}
          <div className="flex-1 relative overflow-hidden">
            <ChatInterface
              messages={enhancedMessages}
              currentParticipant={currentParticipant}
              participants={participants || []}
              onReaction={handleReaction}
              onReply={handleReply}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onMentionClick={handleMentionClick}
              replyingTo={replyingTo}
            />
          </div>

          {/* Message input */}
          <div className="border-t border-border bg-background/95 backdrop-blur-sm p-3">
            <MessageInput
              roomId={roomId}
              currentParticipant={currentParticipant}
              participants={participants?.filter(p => p.isActive) || []}
              disabled={!currentParticipant?.isActive}
              onMessageSent={handleMessageSent}
              replyingTo={replyingTo}
              onCancelReply={cancelReply}
            />
          </div>
        </div>

        {/* Sidebar */}
        {showSidebar && (
          <div className="w-64 border-l border-border bg-muted/30 p-3">
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-sm mb-2">Participants</h3>
                <div className="space-y-2">
                  {participants?.map((participant) => (
                    <div
                      key={participant.sessionId}
                      className="flex items-center gap-2 p-2 rounded-lg hover:bg-muted/50"
                    >
                      <div className={cn(
                        "w-2 h-2 rounded-full",
                        participant.isActive ? "bg-green-500" : "bg-gray-400"
                      )} />
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium truncate">
                          {participant.anonymousAlias}
                          {participant.sessionId === identity?.sessionId && (
                            <span className="text-xs text-muted-foreground ml-1">(you)</span>
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {participant.messageCount} messages
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Room stats */}
              <div>
                <h3 className="font-medium text-sm mb-2">Room Stats</h3>
                <div className="space-y-1 text-xs text-muted-foreground">
                  <div>Total messages: {room.totalMessages || 0}</div>
                  <div>Active participants: {participants?.filter(p => p.isActive).length || 0}</div>
                  <div>Max participants: {room.maxParticipants}</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Loading/Error states */}
      {isLoading && (
        <div className="absolute inset-0 bg-background/50 flex items-center justify-center">
          <div className="text-sm text-muted-foreground">Processing...</div>
        </div>
      )}

      {error && (
        <div className="absolute bottom-4 left-4 right-4 bg-destructive/10 border border-destructive/20 rounded-lg p-3">
          <div className="text-sm text-destructive">{error}</div>
        </div>
      )}
    </div>
  );
}
