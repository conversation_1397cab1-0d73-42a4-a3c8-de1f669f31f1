'use client';

import { motion } from 'motion/react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  ArrowLeft, 
  Users,
  MessageSquare,
  Briefcase,
  GraduationCap,
  Heart,
  Code,
  Sparkles
} from 'lucide-react';

const categories = [
  {
    icon: Briefcase,
    title: "Professional",
    description: "Workplace discussions, feedback, and industry insights",
    count: 12,
    color: "from-blue-500/20 to-blue-600/20"
  },
  {
    icon: GraduationCap,
    title: "Education",
    description: "Student discussions, academic topics, and learning",
    count: 8,
    color: "from-green-500/20 to-green-600/20"
  },
  {
    icon: Heart,
    title: "Support",
    description: "Mental health, recovery, and personal growth",
    count: 15,
    color: "from-pink-500/20 to-pink-600/20"
  },
  {
    icon: Code,
    title: "Technology",
    description: "Tech discussions, programming, and innovation",
    count: 6,
    color: "from-purple-500/20 to-purple-600/20"
  },
  {
    icon: Users,
    title: "Community",
    description: "General discussions and community building",
    count: 9,
    color: "from-orange-500/20 to-orange-600/20"
  },
  {
    icon: MessageSquare,
    title: "Open Forum",
    description: "Free-form discussions on any topic",
    count: 4,
    color: "from-red-500/20 to-red-600/20"
  }
];

export default function BrowsePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* VeilMeet Logo & Brand */}
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center">
              <Shield className="h-6 w-6 text-primary-foreground" />
            </div>
            <div className="flex flex-col items-start">
              <span className="text-lg font-semibold text-foreground">VeilMeet</span>
              <span className="text-xs text-muted-foreground">Anonymous Conferencing</span>
            </div>
          </div>

          <Badge variant="secondary" className="mb-4">
            <Sparkles className="w-4 h-4 mr-2" />
            Browse Categories
          </Badge>
          
          <h1 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-foreground to-primary bg-clip-text text-transparent">
            Explore Room Categories
          </h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Find the perfect anonymous discussion space for your interests and needs
          </p>
        </motion.div>

        {/* Categories Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12"
        >
          {categories.map((category, index) => (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="group hover:shadow-xl transition-all duration-300 hover:scale-105 border-border/50 bg-card/50 backdrop-blur-sm overflow-hidden">
                <CardContent className="p-6">
                  {/* Icon and Background */}
                  <div className="relative mb-6">
                    <div className={`absolute inset-0 bg-gradient-to-br ${category.color} rounded-lg blur-xl opacity-50`} />
                    <div className="relative w-16 h-16 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <category.icon className="w-8 h-8 text-primary" />
                    </div>
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-semibold text-foreground mb-3">
                    {category.title}
                  </h3>
                  <p className="text-muted-foreground mb-4 leading-relaxed">
                    {category.description}
                  </p>

                  {/* Stats */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Users className="w-4 h-4" />
                      <span>{category.count} active rooms</span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      Active
                    </Badge>
                  </div>

                  <Button 
                    variant="outline" 
                    className="w-full group-hover:bg-primary/10 transition-colors"
                    asChild
                  >
                    <Link href={`/category/${category.title.toLowerCase()}`}>
                      Browse Rooms
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="text-center space-y-4"
        >
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/create-room">
                Create New Room
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/join">
                Join with Code
              </Link>
            </Button>
          </div>
          
          <Button variant="ghost" asChild>
            <Link href="/">
              <ArrowLeft className="mr-2 w-4 h-4" />
              Back to Home
            </Link>
          </Button>
        </motion.div>
      </div>
    </div>
  );
}
