'use client';

import { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  MoreVertical,
  Reply,
  Co<PERSON>,
  Flag,
  Trash2,
  Edit,
  Bot,
  MessageSquare,
  Clock
} from 'lucide-react';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { generateAvatarUrl, formatRelativeTime } from '@/lib/identity';
import { cn } from '@/lib/utils';

interface MessageMention {
  participantSessionId: string;
  anonymousAlias: string;
  startIndex: number;
  endIndex: number;
  mentionText: string;
}

interface MessageReaction {
  emoji: string;
  count: number;
  participantSessionIds: string[];
  firstReactedAt: number;
  lastReactedAt: number;
  participantDetails: Array<{
    sessionId: string;
    anonymousAlias: string;
    reactedAt: number;
  }>;
}

interface Message {
  id: string;
  roomId: string;
  participantSessionId: string;
  anonymousAlias: string;
  content: string;
  messageType: 'text' | 'voice' | 'system' | 'ai_generated';
  timestamp: number;
  isEdited: boolean;
  editedAt?: number;
  reactions: MessageReaction[];
  replyToMessageId?: string;
  threadRootId?: string;
  threadDepth: number;
  threadReplyCount: number;
  mentions: MessageMention[];
  hasMentions: boolean;
  engagementMetrics: {
    totalReactions: number;
    uniqueReactors: number;
    replyCount: number;
    mentionCount: number;
    viewCount: number;
    lastEngagementAt?: number;
  };
}

interface Participant {
  sessionId: string;
  anonymousAlias: string;
  avatarSeed: string;
  joinedAt: number;
  messageCount: number;
  isActive: boolean;
  isMuted: boolean;
}

interface ChatInterfaceProps {
  messages: Message[];
  currentParticipant?: Participant;
  participants: Participant[];
  onReaction?: (messageId: string, emoji: string) => void;
  onReply?: (messageId: string, content: string, author: string) => void;
  onEdit?: (messageId: string, newContent: string) => void;
  onDelete?: (messageId: string) => void;
  onMentionClick?: (participantSessionId: string) => void;
  replyingTo?: {
    messageId: string;
    content: string;
    author: string;
  } | null;
}

export function ChatInterface({
  messages,
  currentParticipant,
  participants,
  onReaction,
  onReply,
  onEdit,
  onDelete,
  onMentionClick,
  replyingTo
}: ChatInterfaceProps) {
  const scrollViewportRef = useRef<HTMLDivElement>(null);
  const [autoScroll, setAutoScroll] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [isUserScrolling, setIsUserScrolling] = useState(false);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (autoScroll && !isUserScrolling && messages.length > 0) {
      const scrollToBottom = () => {
        const viewport = scrollViewportRef.current;

        if (viewport) {
          // Force scroll to bottom
          viewport.scrollTop = viewport.scrollHeight - viewport.clientHeight;
        }
      };

      // Multiple attempts to ensure scrolling works
      scrollToBottom();
      requestAnimationFrame(scrollToBottom);
      setTimeout(scrollToBottom, 10);
      setTimeout(scrollToBottom, 100);
    }
  }, [messages.length, autoScroll, isUserScrolling]);

  // Initial scroll to bottom on mount
  useEffect(() => {
    const viewport = scrollViewportRef.current;
    if (viewport && messages.length > 0) {
      setTimeout(() => {
        viewport.scrollTop = viewport.scrollHeight - viewport.clientHeight;
      }, 100);
    }
  }, []);

  // Enhanced scroll handling
  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const viewport = event.currentTarget;
    const { scrollTop, scrollHeight, clientHeight } = viewport;

    // Check if user is at the bottom (with tolerance)
    const isAtBottom = Math.abs(scrollHeight - scrollTop - clientHeight) < 5;

    // Update auto-scroll state
    setAutoScroll(isAtBottom);

    // Track user scrolling
    setIsUserScrolling(true);

    // Clear user scrolling flag after a delay
    setTimeout(() => setIsUserScrolling(false), 200);
  };

  const getParticipantAvatar = (sessionId: string) => {
    const participant = participants.find(p => p.sessionId === sessionId);
    return participant ? generateAvatarUrl(participant.avatarSeed) : '';
  };

  const isOwnMessage = (message: Message) => {
    return message.participantSessionId === currentParticipant?.sessionId;
  };

  const canEditMessage = (message: Message) => {
    return isOwnMessage(message) && 
           message.messageType === 'text' && 
           Date.now() - message.timestamp < 5 * 60 * 1000; // 5 minutes
  };

  const canDeleteMessage = (message: Message) => {
    return isOwnMessage(message) && message.messageType === 'text';
  };

  // Helper function to render message content with mentions highlighted
  const renderMessageContent = (content: string, mentions: MessageMention[]) => {
    if (!mentions || mentions.length === 0) {
      return content;
    }

    // Sort mentions by start index to process them in order
    const sortedMentions = [...mentions].sort((a, b) => a.startIndex - b.startIndex);

    let result = [];
    let lastIndex = 0;

    sortedMentions.forEach((mention, index) => {
      // Add text before mention
      if (mention.startIndex > lastIndex) {
        result.push(content.slice(lastIndex, mention.startIndex));
      }

      // Add highlighted mention
      result.push(
        <span
          key={`mention-${index}`}
          className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-1 rounded cursor-pointer hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors"
          onClick={() => onMentionClick?.(mention.participantSessionId)}
          title={`Mentioned ${mention.anonymousAlias}`}
        >
          {mention.mentionText}
        </span>
      );

      lastIndex = mention.endIndex;
    });

    // Add remaining text
    if (lastIndex < content.length) {
      result.push(content.slice(lastIndex));
    }

    return result;
  };

  // Helper function to find the message being replied to
  const findReplyToMessage = (replyToMessageId?: string) => {
    if (!replyToMessageId) return null;
    return messages.find(m => m.id === replyToMessageId);
  };

  // Helper function to render reply context
  const renderReplyContext = (replyToMessage: Message) => {
    const isReplyToOwn = replyToMessage.participantSessionId === currentParticipant?.sessionId;

    return (
      <div className="mb-2 pl-3 border-l-2 border-muted-foreground/30">
        <div className="text-xs text-muted-foreground mb-1">
          Replying to {isReplyToOwn ? 'yourself' : replyToMessage.anonymousAlias}
        </div>
        <div className="text-xs text-muted-foreground/80 line-clamp-2">
          {replyToMessage.content.length > 100
            ? replyToMessage.content.substring(0, 100) + '...'
            : replyToMessage.content
          }
        </div>
      </div>
    );
  };

  // Scroll to bottom function for manual use
  const scrollToBottom = () => {
    setAutoScroll(true);
    setIsUserScrolling(false);

    const viewport = scrollViewportRef.current;

    if (viewport) {
      viewport.scrollTo({
        top: viewport.scrollHeight - viewport.clientHeight,
        behavior: 'smooth'
      });
    }
  };

  if (messages.length === 0) {
    return (
      <div className="absolute inset-0 flex items-center justify-center p-8 overflow-hidden">
        <div className="text-center space-y-3 max-w-sm">
          <div className="w-16 h-16 mx-auto rounded-full bg-muted/50 flex items-center justify-center">
            <MessageSquare className="w-8 h-8 text-muted-foreground" />
          </div>
          <div className="space-y-1">
            <div className="font-medium text-muted-foreground">No messages yet</div>
            <div className="text-sm text-muted-foreground/70">
              Start the conversation by sending a message below
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="absolute inset-0 flex flex-col overflow-hidden">
      <div
        ref={scrollViewportRef}
        className="flex-1 overflow-y-auto overflow-x-hidden px-3 lg:px-4 scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent"
        onScroll={handleScroll}
      >
        <div className="py-3 lg:py-4 space-y-1">
        {messages.map((message, index) => {
          const isOwn = isOwnMessage(message);
          const isSystem = message.messageType === 'system';
          const isAI = message.messageType === 'ai_generated';
          const prevMessage = index > 0 ? messages[index - 1] : null;
          const nextMessage = index < messages.length - 1 ? messages[index + 1] : null;

          // Check if this message is grouped with previous message
          const isGrouped = prevMessage &&
                           prevMessage.participantSessionId === message.participantSessionId &&
                           message.timestamp - prevMessage.timestamp < 60000 && // 1 minute
                           !isSystem;

          // Check if this is the last message in a group (show avatar)
          const isLastInGroup = !nextMessage ||
                               nextMessage.participantSessionId !== message.participantSessionId ||
                               nextMessage.timestamp - message.timestamp >= 60000 ||
                               nextMessage.messageType === 'system';

          const showAvatar = !isOwn && !isSystem && isLastInGroup;

          // Get reply context if this is a reply
          const replyToMessage = findReplyToMessage(message.replyToMessageId);
          const isReply = !!replyToMessage;

          if (isSystem) {
            return (
              <div key={message.id} className="flex justify-center py-2">
                <div className="bg-muted/50 rounded-full px-3 py-1 text-xs text-muted-foreground border">
                  {message.content}
                </div>
              </div>
            );
          }

          return (
            <div
              key={message.id}
              className={cn(
                "flex",
                isOwn ? "justify-end" : "justify-start",
                isGrouped ? "mt-1" : "mt-3 lg:mt-4"
              )}
            >
              <div className={cn(
                "flex items-end space-x-1.5 lg:space-x-2 max-w-[75%] sm:max-w-xs lg:max-w-md",
                isOwn && "flex-row-reverse space-x-reverse"
              )}>
                {/* Avatar - only show for non-own messages and only on last message of group */}
                {!isOwn && (
                  <div className="flex-shrink-0">
                    {showAvatar ? (
                      <img
                        src={isAI ? '/ai-avatar.png' : getParticipantAvatar(message.participantSessionId)}
                        alt={message.anonymousAlias}
                        className="w-5 h-5 lg:w-6 lg:h-6 rounded-full mb-1"
                      />
                    ) : (
                      <div className="w-5 h-5 lg:w-6 lg:h-6" />
                    )}
                  </div>
                )}

                {/* Message Content */}
                <div className="relative group">
                  <div className={cn(
                    "px-3 py-1.5 lg:px-4 lg:py-2 rounded-lg cursor-pointer select-none relative",
                    isOwn
                      ? "bg-primary text-primary-foreground rounded-br-sm hover:bg-primary/90"
                      : isAI
                      ? "bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800/50 text-blue-900 dark:text-blue-100 rounded-bl-sm"
                      : "bg-muted rounded-bl-sm hover:bg-muted/80"
                  )}
                  onDoubleClick={() => onReaction?.(message.id, '👍')}
                  title="Double-click to react"
                  >
                  {/* Author name for non-own messages */}
                  {!isOwn && !isGrouped && (
                    <div className="text-xs font-medium mb-1 opacity-70">
                      {isAI ? (
                        <span className="flex items-center gap-1 text-blue-600 dark:text-blue-400">
                          <Bot className="h-3 w-3" />
                          AI Assistant
                        </span>
                      ) : (
                        message.anonymousAlias
                      )}
                    </div>
                  )}

                  {/* Reply context */}
                  {replyToMessage && renderReplyContext(replyToMessage)}

                  {/* Message text with mention highlighting */}
                  <div className="text-sm leading-relaxed break-words overflow-wrap-anywhere">
                    {renderMessageContent(message.content, message.mentions)}
                  </div>

                  {/* Thread indicator */}
                  {message.threadReplyCount > 0 && (
                    <div className="mt-2 text-xs text-muted-foreground flex items-center gap-1">
                      <MessageSquare className="h-3 w-3" />
                      {message.threadReplyCount} {message.threadReplyCount === 1 ? 'reply' : 'replies'}
                    </div>
                  )}

                  {/* Message status and time */}
                  <div className={cn(
                    "flex items-center justify-end gap-1 mt-1 text-xs",
                    isOwn ? "text-primary-foreground/70" : "text-muted-foreground/70"
                  )}>
                    {message.isEdited && (
                      <span className="opacity-70">edited</span>
                    )}
                    <span>{formatRelativeTime(message.timestamp)}</span>
                    {isOwn && (
                      <Clock className="w-3 h-3 opacity-60" />
                    )}
                  </div>

                  {/* Enhanced Reactions */}
                  {message.reactions.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {message.reactions.map((reaction) => {
                        const hasReacted = reaction.participantSessionIds.includes(currentParticipant?.sessionId || '');
                        const reactionTooltip = reaction.participantDetails
                          ?.slice(0, 5)
                          .map(p => p.anonymousAlias)
                          .join(', ') + (reaction.count > 5 ? ` and ${reaction.count - 5} others` : '');

                        return (
                          <button
                            key={reaction.emoji}
                            className={cn(
                              "inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs transition-colors touch-manipulation",
                              hasReacted
                                ? "bg-blue-100 dark:bg-blue-900/30 border border-blue-300 dark:border-blue-700 text-blue-700 dark:text-blue-300"
                                : "bg-muted/50 hover:bg-muted border border-border/50 text-muted-foreground"
                            )}
                            onClick={() => onReaction?.(message.id, reaction.emoji)}
                            title={reactionTooltip}
                          >
                            <span>{reaction.emoji}</span>
                            <span className={hasReacted ? "font-medium" : ""}>{reaction.count}</span>
                          </button>
                        );
                      })}
                    </div>
                  )}

                  </div>

                  {/* Message actions - positioned outside bubble but within container */}
                  <div className={cn(
                    "absolute top-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200",
                    "hidden sm:flex items-center gap-1", // Hide on mobile
                    isOwn ? "-left-16" : "-right-16"
                  )}>
                    {/* Quick reaction buttons */}
                    <div className="flex items-center gap-0.5 bg-background border border-border rounded-full px-1 py-0.5 shadow-md">
                      {['👍', '❤️', '😊', '😮'].map((emoji) => (
                        <button
                          key={emoji}
                          className="h-6 w-6 rounded-full hover:bg-muted transition-colors text-xs flex items-center justify-center"
                          onClick={() => onReaction?.(message.id, emoji)}
                          title={`React with ${emoji}`}
                        >
                          {emoji}
                        </button>
                      ))}
                    </div>

                    {/* More options */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 rounded-full bg-background border border-border hover:bg-muted"
                        >
                          <MoreVertical className="h-3 w-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align={isOwn ? "end" : "start"} className="w-40">

                        <DropdownMenuItem onClick={() => onReply?.(message.id, message.content, message.anonymousAlias)}>
                          <Reply className="mr-2 h-4 w-4" />
                          Reply
                        </DropdownMenuItem>

                        <DropdownMenuItem onClick={() => navigator.clipboard.writeText(message.content)}>
                          <Copy className="mr-2 h-4 w-4" />
                          Copy
                        </DropdownMenuItem>

                        {canEditMessage(message) && (
                          <DropdownMenuItem onClick={() => onEdit?.(message.id, message.content)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                        )}

                        {canDeleteMessage(message) && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => onDelete?.(message.id)}
                              className="text-destructive focus:text-destructive"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </>
                        )}

                        {!isOwn && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive focus:text-destructive">
                              <Flag className="mr-2 h-4 w-4" />
                              Report
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  {/* Mobile message actions - Long press or tap */}
                  <div className="sm:hidden absolute top-1 right-1">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <button
                          className="h-6 w-6 rounded-full bg-background/90 border border-border/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center"
                          onTouchStart={(e) => e.stopPropagation()}
                        >
                          <MoreVertical className="h-3 w-3" />
                        </button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align={isOwn ? "end" : "start"} className="w-44">
                        {/* Quick reactions */}
                        <div className="flex gap-1 p-2 justify-center border-b">
                          {['👍', '❤️', '😊', '😮', '😢', '😡'].map((emoji) => (
                            <button
                              key={emoji}
                              className="h-8 w-8 rounded-full hover:bg-muted transition-colors text-base touch-manipulation flex items-center justify-center"
                              onClick={() => onReaction?.(message.id, emoji)}
                            >
                              {emoji}
                            </button>
                          ))}
                        </div>
                        <DropdownMenuSeparator />

                        <DropdownMenuItem onClick={() => onReply?.(message.id, message.content, message.anonymousAlias)} className="py-3">
                          <Reply className="mr-3 h-5 w-5" />
                          Reply
                        </DropdownMenuItem>

                        <DropdownMenuItem className="py-3" onClick={() => navigator.clipboard.writeText(message.content)}>
                          <Copy className="mr-3 h-5 w-5" />
                          Copy
                        </DropdownMenuItem>

                        {canEditMessage(message) && (
                          <DropdownMenuItem onClick={() => onEdit?.(message.id, message.content)} className="py-3">
                            <Edit className="mr-3 h-5 w-5" />
                            Edit
                          </DropdownMenuItem>
                        )}

                        {canDeleteMessage(message) && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => onDelete?.(message.id)}
                              className="text-destructive focus:text-destructive py-3"
                            >
                              <Trash2 className="mr-3 h-5 w-5" />
                              Delete
                            </DropdownMenuItem>
                          </>
                        )}

                        {!isOwn && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive focus:text-destructive py-3">
                              <Flag className="mr-3 h-5 w-5" />
                              Report
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            </div>
          );
          })}
          {/* Invisible element to scroll to */}
          <div ref={messagesEndRef} className="h-1" />
        </div>
      </div>

      {/* Scroll to bottom indicator */}
      {!autoScroll && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10">
          <Button
            variant="outline"
            size="sm"
            onClick={scrollToBottom}
            className="shadow-lg border-border/50 bg-background/90 backdrop-blur-sm hover:bg-background rounded-full px-4 py-2"
          >
            <MessageSquare className="w-4 h-4 mr-2" />
            New messages
          </Button>
        </div>
      )}
    </div>
  );
}
