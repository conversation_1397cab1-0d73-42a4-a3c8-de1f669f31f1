/**
 * VeilMind Push-to-Talk Controls System
 * Flexible PTT with multiple input methods and safety features
 */

export type PTTMode = 'hold' | 'toggle' | 'voice_activation';
export type PTTInputType = 'keyboard' | 'mouse' | 'touch' | 'voice';

export interface PTTSettings {
  mode: PTTMode;
  keyboardKey: string; // Key code (e.g., 'Space', 'KeyT')
  mouseButton: number; // 0=left, 1=middle, 2=right
  enableKeyboard: boolean;
  enableMouse: boolean;
  enableTouch: boolean;
  enableVoiceActivation: boolean;
  audioFeedback: boolean; // Beep sounds
  visualFeedback: boolean; // Visual indicators
  safetyTimeout: number; // Auto-release timeout in ms
  requireModifier: boolean; // Require Ctrl/Alt/Shift
  modifierKeys: string[]; // ['ctrlKey', 'altKey', 'shiftKey']
}

export interface PTTState {
  isActive: boolean;
  isPressing: boolean;
  inputType: PTTInputType | null;
  lastActivation: number;
  safetyTimeoutActive: boolean;
}

export type PTTEventType = 'activated' | 'deactivated' | 'safety_timeout' | 'settings_changed';

export interface PTTEvent {
  type: PTTEventType;
  timestamp: number;
  inputType?: PTTInputType;
  data?: any;
}

export class PushToTalkControls {
  private settings: PTTSettings;
  private state: PTTState;
  private eventListeners: Map<PTTEventType, Set<(event: PTTEvent) => void>> = new Map();
  
  // Input tracking
  private pressedKeys = new Set<string>();
  private pressedMouseButtons = new Set<number>();
  private touchActive = false;
  private voiceActivationActive = false;
  
  // Safety timeout
  private safetyTimer: NodeJS.Timeout | null = null;
  
  // Audio feedback
  private audioContext: AudioContext | null = null;
  private activationSound: AudioBuffer | null = null;
  private deactivationSound: AudioBuffer | null = null;
  
  // Mobile touch handling
  private touchStartTime = 0;
  private touchElement: HTMLElement | null = null;
  
  constructor(settings: Partial<PTTSettings> = {}) {
    this.settings = {
      mode: 'hold',
      keyboardKey: 'Space',
      mouseButton: 2, // Right click
      enableKeyboard: true,
      enableMouse: false,
      enableTouch: true,
      enableVoiceActivation: false,
      audioFeedback: true,
      visualFeedback: true,
      safetyTimeout: 30000, // 30 seconds
      requireModifier: false,
      modifierKeys: [],
      ...settings
    };
    
    this.state = {
      isActive: false,
      isPressing: false,
      inputType: null,
      lastActivation: 0,
      safetyTimeoutActive: false
    };
    
    // Initialize event listener maps
    ['activated', 'deactivated', 'safety_timeout', 'settings_changed'].forEach(eventType => {
      this.eventListeners.set(eventType as PTTEventType, new Set());
    });
    
    this.initializeAudioFeedback();
    this.setupEventListeners();
  }
  
  private async initializeAudioFeedback(): Promise<void> {
    if (!this.settings.audioFeedback) return;
    
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      // Generate activation sound (short beep)
      this.activationSound = this.generateBeep(800, 0.1); // 800Hz, 100ms
      
      // Generate deactivation sound (lower beep)
      this.deactivationSound = this.generateBeep(400, 0.1); // 400Hz, 100ms
    } catch (error) {
      console.warn('Failed to initialize audio feedback:', error);
    }
  }
  
  private generateBeep(frequency: number, duration: number): AudioBuffer {
    if (!this.audioContext) throw new Error('Audio context not available');
    
    const sampleRate = this.audioContext.sampleRate;
    const frameCount = sampleRate * duration;
    const buffer = this.audioContext.createBuffer(1, frameCount, sampleRate);
    const data = buffer.getChannelData(0);
    
    for (let i = 0; i < frameCount; i++) {
      const t = i / sampleRate;
      const envelope = Math.sin(Math.PI * t / duration); // Smooth envelope
      data[i] = Math.sin(2 * Math.PI * frequency * t) * envelope * 0.1;
    }
    
    return buffer;
  }
  
  private setupEventListeners(): void {
    // Keyboard events
    if (this.settings.enableKeyboard) {
      document.addEventListener('keydown', this.handleKeyDown.bind(this));
      document.addEventListener('keyup', this.handleKeyUp.bind(this));
    }
    
    // Mouse events
    if (this.settings.enableMouse) {
      document.addEventListener('mousedown', this.handleMouseDown.bind(this));
      document.addEventListener('mouseup', this.handleMouseUp.bind(this));
      document.addEventListener('contextmenu', this.handleContextMenu.bind(this));
    }
    
    // Prevent page unload while PTT is active
    window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
  }
  
  private handleKeyDown(event: KeyboardEvent): void {
    if (this.isTargetKey(event) && !this.pressedKeys.has(event.code)) {
      this.pressedKeys.add(event.code);
      
      if (this.checkModifiers(event)) {
        this.activatePTT('keyboard');
        event.preventDefault();
      }
    }
  }
  
  private handleKeyUp(event: KeyboardEvent): void {
    if (this.isTargetKey(event) && this.pressedKeys.has(event.code)) {
      this.pressedKeys.delete(event.code);
      
      if (this.settings.mode === 'hold') {
        this.deactivatePTT('keyboard');
      }
      
      event.preventDefault();
    }
  }
  
  private handleMouseDown(event: MouseEvent): void {
    if (event.button === this.settings.mouseButton && !this.pressedMouseButtons.has(event.button)) {
      this.pressedMouseButtons.add(event.button);
      
      if (this.checkModifiers(event)) {
        this.activatePTT('mouse');
        event.preventDefault();
      }
    }
  }
  
  private handleMouseUp(event: MouseEvent): void {
    if (event.button === this.settings.mouseButton && this.pressedMouseButtons.has(event.button)) {
      this.pressedMouseButtons.delete(event.button);
      
      if (this.settings.mode === 'hold') {
        this.deactivatePTT('mouse');
      }
      
      event.preventDefault();
    }
  }
  
  private handleContextMenu(event: MouseEvent): void {
    // Prevent context menu when using right-click for PTT
    if (this.settings.mouseButton === 2 && this.settings.enableMouse) {
      event.preventDefault();
    }
  }
  
  private handleBeforeUnload(event: BeforeUnloadEvent): void {
    if (this.state.isActive) {
      event.preventDefault();
      event.returnValue = 'You are currently speaking. Are you sure you want to leave?';
    }
  }
  
  private isTargetKey(event: KeyboardEvent): boolean {
    return event.code === this.settings.keyboardKey;
  }
  
  private checkModifiers(event: KeyboardEvent | MouseEvent): boolean {
    if (!this.settings.requireModifier) return true;
    
    return this.settings.modifierKeys.some(modifier => {
      switch (modifier) {
        case 'ctrlKey': return event.ctrlKey;
        case 'altKey': return event.altKey;
        case 'shiftKey': return event.shiftKey;
        case 'metaKey': return event.metaKey;
        default: return false;
      }
    });
  }
  
  // Touch handling for mobile
  setupTouchControls(element: HTMLElement): void {
    if (!this.settings.enableTouch) return;
    
    this.touchElement = element;
    
    element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
    element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
    element.addEventListener('touchcancel', this.handleTouchEnd.bind(this), { passive: false });
  }
  
  private handleTouchStart(event: TouchEvent): void {
    if (!this.touchActive) {
      this.touchActive = true;
      this.touchStartTime = Date.now();
      this.activatePTT('touch');
      event.preventDefault();
    }
  }
  
  private handleTouchEnd(event: TouchEvent): void {
    if (this.touchActive) {
      this.touchActive = false;
      
      if (this.settings.mode === 'hold') {
        this.deactivatePTT('touch');
      }
      
      event.preventDefault();
    }
  }
  
  // Voice activation integration
  setVoiceActivation(active: boolean): void {
    if (this.voiceActivationActive === active) return;
    
    this.voiceActivationActive = active;
    
    if (active && this.settings.enableVoiceActivation) {
      this.activatePTT('voice');
    } else if (!active) {
      this.deactivatePTT('voice');
    }
  }
  
  // Core PTT methods
  private activatePTT(inputType: PTTInputType): void {
    if (this.settings.mode === 'toggle' && this.state.isActive) {
      this.deactivatePTT(inputType);
      return;
    }
    
    if (this.state.isActive) return;
    
    this.state.isActive = true;
    this.state.isPressing = true;
    this.state.inputType = inputType;
    this.state.lastActivation = Date.now();
    
    // Start safety timeout
    this.startSafetyTimeout();
    
    // Play activation sound
    this.playSound(this.activationSound);
    
    // Emit event
    this.emitEvent('activated', { inputType });
    
    console.log(`PTT activated via ${inputType}`);
  }
  
  private deactivatePTT(inputType: PTTInputType): void {
    if (!this.state.isActive) return;
    
    // Only deactivate if the input type matches (for multi-input scenarios)
    if (this.state.inputType !== inputType && this.state.inputType !== null) {
      return;
    }
    
    this.state.isActive = false;
    this.state.isPressing = false;
    this.state.inputType = null;
    
    // Clear safety timeout
    this.clearSafetyTimeout();
    
    // Play deactivation sound
    this.playSound(this.deactivationSound);
    
    // Emit event
    this.emitEvent('deactivated', { inputType });
    
    console.log(`PTT deactivated via ${inputType}`);
  }
  
  private startSafetyTimeout(): void {
    if (this.settings.safetyTimeout <= 0) return;
    
    this.clearSafetyTimeout();
    
    this.safetyTimer = setTimeout(() => {
      if (this.state.isActive) {
        this.state.safetyTimeoutActive = true;
        this.deactivatePTT(this.state.inputType || 'keyboard');
        this.emitEvent('safety_timeout', { 
          duration: this.settings.safetyTimeout,
          inputType: this.state.inputType 
        });
        console.warn('PTT safety timeout triggered');
      }
    }, this.settings.safetyTimeout);
  }
  
  private clearSafetyTimeout(): void {
    if (this.safetyTimer) {
      clearTimeout(this.safetyTimer);
      this.safetyTimer = null;
    }
    this.state.safetyTimeoutActive = false;
  }
  
  private playSound(buffer: AudioBuffer | null): void {
    if (!this.settings.audioFeedback || !this.audioContext || !buffer) return;
    
    try {
      const source = this.audioContext.createBufferSource();
      source.buffer = buffer;
      source.connect(this.audioContext.destination);
      source.start();
    } catch (error) {
      console.warn('Failed to play PTT sound:', error);
    }
  }
  
  // Public methods
  forceActivate(): void {
    this.activatePTT('keyboard');
  }
  
  forceDeactivate(): void {
    this.deactivatePTT(this.state.inputType || 'keyboard');
  }
  
  updateSettings(newSettings: Partial<PTTSettings>): void {
    const oldSettings = { ...this.settings };
    this.settings = { ...this.settings, ...newSettings };
    
    // Reinitialize audio if audio feedback setting changed
    if (oldSettings.audioFeedback !== this.settings.audioFeedback) {
      this.initializeAudioFeedback();
    }
    
    this.emitEvent('settings_changed', { 
      oldSettings, 
      newSettings: this.settings 
    });
  }
  
  getState(): PTTState {
    return { ...this.state };
  }
  
  getSettings(): PTTSettings {
    return { ...this.settings };
  }
  
  // Event handling
  addEventListener(eventType: PTTEventType, listener: (event: PTTEvent) => void): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.add(listener);
    }
  }
  
  removeEventListener(eventType: PTTEventType, listener: (event: PTTEvent) => void): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.delete(listener);
    }
  }
  
  private emitEvent(type: PTTEventType, data?: any): void {
    const event: PTTEvent = {
      type,
      timestamp: Date.now(),
      data
    };
    
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.forEach(listener => listener(event));
    }
  }
  
  // Cleanup
  dispose(): void {
    // Force deactivate if active
    if (this.state.isActive) {
      this.forceDeactivate();
    }

    // Clear timers
    this.clearSafetyTimeout();

    // Close audio context
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
      this.audioContext = null;
    }

    // Clear touch element
    if (this.touchElement) {
      this.touchElement = null;
    }

    // Clear state
    this.state.isActive = false;
    this.state.isPressing = false;
    this.eventListeners.clear();

    console.log('Push-to-Talk Controls disposed');
  }
}
