"use client";

import React from "react";
import { Header } from "./_components/header";
import { HeroSection } from "./_components/hero-section";
import { RoomDiscoverySection } from "./_components/room-discovery-section";
import { FeaturesSection } from "./_components/features-section";
import { HowItWorksSection } from "./_components/how-it-works-section";
import { UseCasesSection } from "./_components/use-cases-section";
import { AICapabilitiesSection } from "./_components/ai-capabilities-section";
import { PrivacySecuritySection } from "./_components/privacy-security-section";
import { TestimonialsSection } from "./_components/testimonials-section";
import { FinalCTASection } from "./_components/final-cta-section";
import { Footer } from "./_components/footer";

const navItems = [
  { name: "Features", link: "#features" },
  { name: "How It Works", link: "#how-it-works" },
  { name: "Use Cases", link: "#use-cases" },
  { name: "AI Technology", link: "#ai-capabilities" },
  { name: "Security", link: "#privacy-security" },
  { name: "Testimonials", link: "#testimonials" }
];

export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      <Header navItems={navItems} />

      <main className="pt-16">
        <HeroSection />
        <RoomDiscoverySection />
        <FeaturesSection />
        <HowItWorksSection />
        <UseCasesSection />
        <AICapabilitiesSection />
        <PrivacySecuritySection />
        <TestimonialsSection />
        <FinalCTASection />
      </main>

      <Footer />
    </div>
  );
}
