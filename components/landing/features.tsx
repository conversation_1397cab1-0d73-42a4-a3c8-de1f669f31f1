"use client";

import { motion } from "motion/react";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Shield, 
  Sparkles, 
  Zap, 
  Eye, 
  MessageSquare, 
  Brain,
  Lock,
  Users,
  Mic
} from "lucide-react";

const features = [
  {
    icon: Shield,
    title: "Complete Anonymity",
    description: "Your identity remains completely hidden. No registration, no tracking, no digital footprints.",
    color: "green-500",
    badge: "Privacy First"
  },
  {
    icon: Sparkles,
    title: "AI-Powered Moderation",
    description: "Real-time AI ensures conversations stay productive, respectful, and on-topic.",
    color: "primary",
    badge: "AI Enhanced"
  },
  {
    icon: Zap,
    title: "Instant Room Creation",
    description: "Create anonymous discussion rooms in seconds. No setup, no complexity, just talk.",
    color: "cyan-500",
    badge: "One-Click"
  },
  {
    icon: Eye,
    title: "Voice Modulation",
    description: "Advanced voice changing technology ensures even your voice remains anonymous.",
    color: "blue-500",
    badge: "Voice Privacy"
  },
  {
    icon: MessageSquare,
    title: "Smart Facilitation",
    description: "AI suggests conversation starters, manages turn-taking, and keeps discussions flowing.",
    color: "orange-500",
    badge: "AI Facilitated"
  },
  {
    icon: Brain,
    title: "Intelligent Insights",
    description: "Get anonymous sentiment analysis and conversation summaries without compromising privacy.",
    color: "red-500",
    badge: "Smart Analytics"
  }
];

export function Features() {
  return (
    <section className="py-16 sm:py-20 lg:py-24 px-4 sm:px-6 lg:px-8 relative">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          className="text-center mb-12 sm:mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Badge variant="outline" className="mb-4 border-primary/30 text-primary text-xs sm:text-sm">
            <Sparkles className="w-3 h-3 mr-1" />
            Core Features
          </Badge>
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6 px-2">
            <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">AI Meets Anonymity</span>
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed px-2">
            Experience the perfect fusion of cutting-edge AI technology and complete privacy protection.
            Every feature designed to empower honest, meaningful conversations.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
              className="group"
            >
              <Card className="h-full bg-card/50 backdrop-blur-sm border-border/50 hover:border-primary/30 transition-all duration-300 group-hover:shadow-lg">
                <CardContent className="p-4 sm:p-6 lg:p-8">
                  <div className="flex items-start gap-3 sm:gap-4 mb-4">
                    <div className={`p-2 sm:p-3 rounded-xl bg-${feature.color}/10 group-hover:bg-${feature.color}/20 transition-colors flex-shrink-0`}>
                      <feature.icon className={`w-5 h-5 sm:w-6 sm:h-6 text-${feature.color}`} />
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {feature.badge}
                    </Badge>
                  </div>

                  <h3 className="text-lg sm:text-xl font-semibold mb-3 group-hover:text-primary transition-colors leading-tight">
                    {feature.title}
                  </h3>

                  <p className="text-sm sm:text-base text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-12 sm:mt-16 px-4"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center gap-2 text-muted-foreground text-sm sm:text-base">
            <Lock className="w-4 h-4 flex-shrink-0" />
            <span className="text-center">All features work without compromising your anonymity</span>
          </div>
        </motion.div>
      </div>

      {/* Background decoration */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-1/4 left-0 w-72 h-72 bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-0 w-96 h-96 bg-accent/5 rounded-full blur-3xl" />
      </div>
    </section>
  );
}
