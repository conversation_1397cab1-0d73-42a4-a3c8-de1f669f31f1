'use client';

import { useState, useCallback } from 'react';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { useAnonymousIdentity } from './useAnonymousIdentity';
import { toast } from 'sonner';

export function useMessaging(roomId?: Id<'rooms'>) {
  const { identity } = useAnonymousIdentity();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const sendMessageMutation = useMutation(api.messages.sendMessage);
  const addReactionMutation = useMutation(api.messages.addReaction);
  const editMessageMutation = useMutation(api.messages.editMessage);
  const deleteMessageMutation = useMutation(api.messages.deleteMessage);

  // Send a text message
  const sendMessage = useCallback(async (content: string) => {
    if (!roomId || !identity || !content.trim()) {
      throw new Error('Missing required parameters');
    }

    try {
      setIsLoading(true);
      setError(null);

      const messageId = await sendMessageMutation({
        roomId,
        participantSessionId: identity.sessionId,
        content: content.trim(),
        messageType: 'text',
      });

      return messageId;
    } catch (err) {
      console.error('Failed to send message:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [roomId, identity, sendMessageMutation]);

  // Add or remove a reaction to a message
  const toggleReaction = useCallback(async (messageId: Id<'messages'>, emoji: string) => {
    if (!identity) {
      throw new Error('No identity available');
    }

    try {
      setError(null);

      const updatedReactions = await addReactionMutation({
        messageId,
        participantSessionId: identity.sessionId,
        emoji,
      });

      return updatedReactions;
    } catch (err) {
      console.error('Failed to toggle reaction:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to add reaction';
      setError(errorMessage);
      throw err;
    }
  }, [identity, addReactionMutation]);

  // Edit a message
  const editMessage = useCallback(async (messageId: Id<'messages'>, newContent: string) => {
    if (!identity || !newContent.trim()) {
      throw new Error('Missing required parameters');
    }

    try {
      setIsLoading(true);
      setError(null);

      await editMessageMutation({
        messageId,
        participantSessionId: identity.sessionId,
        newContent: newContent.trim(),
      });

      toast.success('Message edited successfully');
    } catch (err) {
      console.error('Failed to edit message:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to edit message';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [identity, editMessageMutation]);

  // Delete a message
  const deleteMessage = useCallback(async (messageId: Id<'messages'>) => {
    if (!identity) {
      throw new Error('No identity available');
    }

    try {
      setIsLoading(true);
      setError(null);

      await deleteMessageMutation({
        messageId,
        participantSessionId: identity.sessionId,
      });

      toast.success('Message deleted successfully');
    } catch (err) {
      console.error('Failed to delete message:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete message';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [identity, deleteMessageMutation]);

  // Send a quick reaction (common emojis)
  const sendQuickReaction = useCallback(async (messageId: Id<'messages'>, emoji: '👍' | '❤️' | '😊' | '😮' | '😢' | '😡') => {
    return toggleReaction(messageId, emoji);
  }, [toggleReaction]);

  // Validate message content
  const validateMessage = useCallback((content: string): { isValid: boolean; error?: string } => {
    if (!content.trim()) {
      return { isValid: false, error: 'Message cannot be empty' };
    }

    if (content.length > 1000) {
      return { isValid: false, error: 'Message is too long (max 1000 characters)' };
    }

    // Basic content validation (can be extended)
    const hasOnlyWhitespace = /^\s*$/.test(content);
    if (hasOnlyWhitespace) {
      return { isValid: false, error: 'Message cannot contain only whitespace' };
    }

    return { isValid: true };
  }, []);

  // Format message for display
  const formatMessage = useCallback((content: string): string => {
    // Basic formatting (can be extended with markdown, mentions, etc.)
    return content.trim();
  }, []);

  return {
    sendMessage,
    toggleReaction,
    editMessage,
    deleteMessage,
    sendQuickReaction,
    validateMessage,
    formatMessage,
    isLoading,
    error,
  };
}

// Hook for message reactions
export function useMessageReactions() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const addReactionMutation = useMutation(api.messages.addReaction);

  const addReaction = useCallback(async (
    messageId: Id<'messages'>, 
    emoji: string, 
    participantSessionId: string
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      const updatedReactions = await addReactionMutation({
        messageId,
        participantSessionId,
        emoji,
      });

      return updatedReactions;
    } catch (err) {
      console.error('Failed to add reaction:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to add reaction';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [addReactionMutation]);

  const getReactionSummary = useCallback((reactions: Array<{
    emoji: string;
    count: number;
    participantSessionIds: string[];
  }>, currentSessionId?: string) => {
    return reactions.map(reaction => ({
      ...reaction,
      hasUserReacted: currentSessionId ? reaction.participantSessionIds.includes(currentSessionId) : false,
    }));
  }, []);

  return {
    addReaction,
    getReactionSummary,
    isLoading,
    error,
  };
}

// Hook for message threading (replies)
export function useMessageThreading() {
  const [replyingTo, setReplyingTo] = useState<{
    messageId: string;
    content: string;
    author: string;
  } | null>(null);

  const startReply = useCallback((messageId: string, content: string, author: string) => {
    setReplyingTo({ messageId, content, author });
  }, []);

  const cancelReply = useCallback(() => {
    setReplyingTo(null);
  }, []);

  const formatReplyPreview = useCallback((content: string, maxLength: number = 50) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  }, []);

  return {
    replyingTo,
    startReply,
    cancelReply,
    formatReplyPreview,
  };
}
