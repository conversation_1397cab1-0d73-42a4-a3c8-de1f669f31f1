/**
 * VeilMind Voice Modulation AudioWorklet Processor
 * Real-time voice transformation for complete anonymity
 * 
 * Features:
 * - Pitch shifting (-12 to +12 semitones)
 * - Formant shifting for gender/age anonymity
 * - Tone modification (warmth, breathiness, resonance)
 * - Real-time processing with <50ms latency
 */

class VoiceModulatorProcessor extends AudioWorkletProcessor {
  constructor() {
    super();
    
    // Initialize parameters
    this.pitchShift = 0; // semitones
    this.formantShift = 1.0; // multiplier
    this.warmth = 0; // -1 to 1
    this.breathiness = 0; // 0 to 1
    this.resonance = 0; // -1 to 1
    this.roboticFilter = 0; // 0 to 1
    
    // Processing buffers
    this.bufferSize = 1024;
    this.inputBuffer = new Float32Array(this.bufferSize);
    this.outputBuffer = new Float32Array(this.bufferSize);
    this.bufferIndex = 0;
    
    // Pitch shifting variables
    this.pitchShiftBuffer = new Float32Array(this.bufferSize * 2);
    this.pitchShiftIndex = 0;
    this.pitchRatio = 1.0;
    
    // Formant shifting variables
    this.formantBuffer = new Float32Array(this.bufferSize);
    this.formantDelay = new Float32Array(64);
    this.formantIndex = 0;
    
    // Tone modification filters
    this.lowPassFilter = { x1: 0, x2: 0, y1: 0, y2: 0 };
    this.highPassFilter = { x1: 0, x2: 0, y1: 0, y2: 0 };
    this.bandPassFilter = { x1: 0, x2: 0, y1: 0, y2: 0 };
    
    // Noise gate
    this.noiseGate = -40; // dB
    this.gateThreshold = Math.pow(10, this.noiseGate / 20);
    this.gateSmoothing = 0.01;
    this.gateLevel = 0;
    
    // Listen for parameter changes
    this.port.onmessage = (event) => {
      const { type, data } = event.data;
      if (type === 'updateParameters') {
        this.updateParameters(data);
      }
    };
  }
  
  updateParameters(params) {
    if (params.pitchShift !== undefined) {
      this.pitchShift = Math.max(-12, Math.min(12, params.pitchShift));
      this.pitchRatio = Math.pow(2, this.pitchShift / 12);
    }
    if (params.formantShift !== undefined) {
      this.formantShift = Math.max(0.5, Math.min(2.0, params.formantShift));
    }
    if (params.warmth !== undefined) {
      this.warmth = Math.max(-1, Math.min(1, params.warmth));
    }
    if (params.breathiness !== undefined) {
      this.breathiness = Math.max(0, Math.min(1, params.breathiness));
    }
    if (params.resonance !== undefined) {
      this.resonance = Math.max(-1, Math.min(1, params.resonance));
    }
    if (params.roboticFilter !== undefined) {
      this.roboticFilter = Math.max(0, Math.min(1, params.roboticFilter));
    }
    if (params.noiseGate !== undefined) {
      this.noiseGate = Math.max(-60, Math.min(0, params.noiseGate));
      this.gateThreshold = Math.pow(10, this.noiseGate / 20);
    }
  }
  
  process(inputs, outputs, parameters) {
    const input = inputs[0];
    const output = outputs[0];
    
    if (!input || !input[0] || !output || !output[0]) {
      return true;
    }
    
    const inputChannel = input[0];
    const outputChannel = output[0];
    const frameCount = inputChannel.length;
    
    for (let i = 0; i < frameCount; i++) {
      let sample = inputChannel[i];
      
      // Apply noise gate
      sample = this.applyNoiseGate(sample);
      
      // Apply pitch shifting
      sample = this.applyPitchShift(sample);
      
      // Apply formant shifting
      sample = this.applyFormantShift(sample);
      
      // Apply tone modifications
      sample = this.applyToneModification(sample);
      
      // Apply robotic filter if enabled
      if (this.roboticFilter > 0) {
        sample = this.applyRoboticFilter(sample);
      }
      
      // Prevent clipping
      sample = Math.max(-1, Math.min(1, sample));
      
      outputChannel[i] = sample;
    }
    
    return true;
  }
  
  applyNoiseGate(sample) {
    const level = Math.abs(sample);
    
    if (level > this.gateThreshold) {
      this.gateLevel += (1 - this.gateLevel) * this.gateSmoothing;
    } else {
      this.gateLevel += (0 - this.gateLevel) * this.gateSmoothing;
    }
    
    return sample * this.gateLevel;
  }
  
  applyPitchShift(sample) {
    if (Math.abs(this.pitchShift) < 0.01) {
      return sample;
    }
    
    // Simple pitch shifting using time-domain approach
    this.pitchShiftBuffer[this.pitchShiftIndex] = sample;
    
    const readIndex = (this.pitchShiftIndex - (this.bufferSize / this.pitchRatio)) % this.pitchShiftBuffer.length;
    const readIndexInt = Math.floor(readIndex);
    const readIndexFrac = readIndex - readIndexInt;
    
    const sample1 = this.pitchShiftBuffer[readIndexInt] || 0;
    const sample2 = this.pitchShiftBuffer[(readIndexInt + 1) % this.pitchShiftBuffer.length] || 0;
    
    const interpolatedSample = sample1 + (sample2 - sample1) * readIndexFrac;
    
    this.pitchShiftIndex = (this.pitchShiftIndex + 1) % this.pitchShiftBuffer.length;
    
    return interpolatedSample;
  }
  
  applyFormantShift(sample) {
    if (Math.abs(this.formantShift - 1.0) < 0.01) {
      return sample;
    }
    
    // Simple formant shifting using delay line
    this.formantBuffer[this.formantIndex] = sample;
    
    const delayAmount = Math.floor(32 / this.formantShift);
    const delayedIndex = (this.formantIndex - delayAmount + this.formantBuffer.length) % this.formantBuffer.length;
    const delayedSample = this.formantBuffer[delayedIndex];
    
    this.formantIndex = (this.formantIndex + 1) % this.formantBuffer.length;
    
    return sample * 0.7 + delayedSample * 0.3;
  }
  
  applyToneModification(sample) {
    let output = sample;
    
    // Apply warmth (low-pass/high-pass filtering)
    if (this.warmth !== 0) {
      const cutoff = this.warmth > 0 ? 0.3 : 0.7;
      const alpha = Math.exp(-2 * Math.PI * cutoff);
      
      if (this.warmth > 0) {
        // Low-pass filter for warmth
        this.lowPassFilter.y1 = alpha * this.lowPassFilter.y1 + (1 - alpha) * output;
        output = this.lowPassFilter.y1;
      } else {
        // High-pass filter for brightness
        this.highPassFilter.y1 = alpha * this.highPassFilter.y1 + alpha * (output - this.highPassFilter.x1);
        this.highPassFilter.x1 = output;
        output = this.highPassFilter.y1;
      }
    }
    
    // Apply breathiness (add subtle noise)
    if (this.breathiness > 0) {
      const noise = (Math.random() - 0.5) * 0.1 * this.breathiness;
      output = output * (1 - this.breathiness * 0.3) + noise;
    }
    
    // Apply resonance (band-pass filtering)
    if (this.resonance !== 0) {
      const frequency = this.resonance > 0 ? 800 : 200; // Hz
      const q = 2.0;
      const omega = 2 * Math.PI * frequency / 48000; // Assuming 48kHz sample rate
      const sin = Math.sin(omega);
      const cos = Math.cos(omega);
      const alpha = sin / (2 * q);
      
      const b0 = alpha;
      const b1 = 0;
      const b2 = -alpha;
      const a0 = 1 + alpha;
      const a1 = -2 * cos;
      const a2 = 1 - alpha;
      
      const filteredSample = (b0 * output + b1 * this.bandPassFilter.x1 + b2 * this.bandPassFilter.x2 
                             - a1 * this.bandPassFilter.y1 - a2 * this.bandPassFilter.y2) / a0;
      
      this.bandPassFilter.x2 = this.bandPassFilter.x1;
      this.bandPassFilter.x1 = output;
      this.bandPassFilter.y2 = this.bandPassFilter.y1;
      this.bandPassFilter.y1 = filteredSample;
      
      output = output * (1 - Math.abs(this.resonance) * 0.5) + filteredSample * Math.abs(this.resonance) * 0.5;
    }
    
    return output;
  }
  
  applyRoboticFilter(sample) {
    // Simple ring modulation for robotic effect
    const modulationFreq = 30; // Hz
    const time = currentFrame / sampleRate;
    const modulation = Math.sin(2 * Math.PI * modulationFreq * time);
    
    return sample * (0.5 + 0.5 * modulation * this.roboticFilter);
  }
}

registerProcessor('voice-modulator', VoiceModulatorProcessor);
