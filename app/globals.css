@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --animate-gradient: gradient 8s linear infinite;
  @keyframes gradient {
  to {
    background-position: var(--bg-size, 300%) 0;
    }
  }
  --animate-marquee: marquee var(--duration) infinite linear;
  --animate-marquee-vertical: marquee-vertical var(--duration) linear infinite
;
  @keyframes marquee {
  from {
    transform: translateX(0);}
  to {
    transform: translateX(calc(-100% - var(--gap)));}}
  @keyframes marquee-vertical {
  from {
    transform: translateY(0);}
  to {
    transform: translateY(calc(-100% - var(--gap)));}}}
:root {
  /* Core Design Tokens */
  --radius: 0.65rem;
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* VeilMeet Brand Colors */
  --veil-blue: oklch(0.488 0.243 264.376);
  --veil-blue-light: oklch(0.588 0.193 264.376);
  --veil-blue-dark: oklch(0.388 0.293 264.376);
  --veil-cyan: oklch(0.696 0.17 162.48);
  --veil-purple: oklch(0.627 0.265 303.9);
  --veil-green: oklch(0.769 0.188 70.08);

  /* Base Colors */
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);

  /* Semantic Colors */
  --primary: var(--veil-blue);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.456 0 0); /* Improved contrast */
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: var(--veil-blue);

  /* Chart Colors */
  --chart-1: var(--veil-blue);
  --chart-2: var(--veil-cyan);
  --chart-3: var(--veil-green);
  --chart-4: var(--veil-purple);
  --chart-5: oklch(0.645 0.246 16.439);

  /* Sidebar Colors */
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: var(--veil-blue);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: var(--veil-blue);

  /* Spacing Scale */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-2xl: 3rem;     /* 48px */
  --spacing-3xl: 4rem;     /* 64px */

  /* Typography Scale */
  --text-xs: 0.75rem;      /* 12px */
  --text-sm: 0.875rem;     /* 14px */
  --text-base: 1rem;       /* 16px */
  --text-lg: 1.125rem;     /* 18px */
  --text-xl: 1.25rem;      /* 20px */
  --text-2xl: 1.5rem;      /* 24px */
  --text-3xl: 1.875rem;    /* 30px */
  --text-4xl: 2.25rem;     /* 36px */
  --text-5xl: 3rem;        /* 48px */
  --text-6xl: 3.75rem;     /* 60px */
}

.dark {
  /* VeilMeet Brand Colors - Dark Mode */
  --veil-blue: oklch(0.588 0.193 264.376);
  --veil-blue-light: oklch(0.688 0.143 264.376);
  --veil-blue-dark: oklch(0.488 0.243 264.376);
  --veil-cyan: oklch(0.796 0.12 162.48);
  --veil-purple: oklch(0.727 0.215 303.9);
  --veil-green: oklch(0.869 0.138 70.08);

  /* Base Colors - Dark Mode */
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);

  /* Semantic Colors - Dark Mode */
  --primary: var(--veil-blue);
  --primary-foreground: oklch(0.145 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.608 0 0); /* Improved contrast for dark mode */
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: var(--veil-blue);

  /* Chart Colors - Dark Mode */
  --chart-1: var(--veil-blue);
  --chart-2: var(--veil-cyan);
  --chart-3: var(--veil-green);
  --chart-4: var(--veil-purple);
  --chart-5: oklch(0.745 0.196 16.439);

  /* Sidebar Colors - Dark Mode */
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: var(--veil-blue);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: var(--veil-blue);
}


@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Prevent horizontal overflow */
  html, body {
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Ensure all containers respect viewport width */
  .container {
    max-width: 100vw;
    overflow-x: hidden;
  }

  /* Mobile-specific improvements */
  @media (max-width: 768px) {
    /* Improve touch targets */
    button, [role="button"] {
      min-height: 44px;
      min-width: 44px;
    }

    /* Better scrolling on mobile */
    .scrollbar-thin {
      scrollbar-width: thin;
      scrollbar-color: hsl(var(--muted)) transparent;
    }

    .scrollbar-thin::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }

    .scrollbar-thin::-webkit-scrollbar-track {
      background: transparent;
    }

    .scrollbar-thin::-webkit-scrollbar-thumb {
      background-color: hsl(var(--muted));
      border-radius: 2px;
    }

    .scrollbar-thin::-webkit-scrollbar-thumb:hover {
      background-color: hsl(var(--muted-foreground));
    }
  }

  /* Safe area support for mobile devices */
  .safe-area-pt {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-pb {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-pl {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-pr {
    padding-right: env(safe-area-inset-right);
  }

  /* Smooth animations */
  * {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  /* Custom scrollbar for desktop */
  @media (min-width: 769px) {
    .scrollbar-thin {
      scrollbar-width: thin;
      scrollbar-color: hsl(var(--muted)) transparent;
    }

    .scrollbar-thin::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    .scrollbar-thin::-webkit-scrollbar-track {
      background: transparent;
    }

    .scrollbar-thin::-webkit-scrollbar-thumb {
      background-color: hsl(var(--muted));
      border-radius: 3px;
    }

    .scrollbar-thin::-webkit-scrollbar-thumb:hover {
      background-color: hsl(var(--muted-foreground));
    }
  }

  /* VeilMeet Utility Classes */
  .veil-gradient-primary {
    background: linear-gradient(135deg, var(--veil-blue), var(--veil-cyan));
  }

  .veil-gradient-secondary {
    background: linear-gradient(135deg, var(--veil-purple), var(--veil-green));
  }

  .veil-gradient-text {
    background: linear-gradient(135deg, var(--veil-blue), var(--veil-cyan));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .veil-glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .dark .veil-glass {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Animation utilities with reduced motion support */
  @media (prefers-reduced-motion: no-preference) {
    .animate-float {
      animation: float 6s ease-in-out infinite;
    }

    .animate-pulse-slow {
      animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    .animate-fade-in {
      animation: fadeIn 0.5s ease-out;
    }

    .animate-slide-up {
      animation: slideUp 0.5s ease-out;
    }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Focus styles for better accessibility */
  .focus-ring {
    @apply outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }

  /* Consistent section spacing */
  .section-padding {
    @apply py-16 md:py-24;
  }

  .container-padding {
    @apply px-4 md:px-6 lg:px-8;
  }
}
