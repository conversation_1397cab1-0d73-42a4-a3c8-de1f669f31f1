'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  Clock, 
  Shield, 
  Mic, 
  Video, 
  ArrowRight, 
  AlertCircle,
  Loader2,
  UserPlus,
  Home,
  RefreshCw
} from 'lucide-react';
import { useRoom } from '@/hooks/useRoom';
import { useAnonymousIdentity, useRoomParticipation } from '@/hooks/useAnonymousIdentity';
import { formatRelativeTime, isValidRoomCode, generateAvatarUrl } from '@/lib/identity';
import { toast } from 'sonner';

export default function JoinRoomPage() {
  const params = useParams();
  const router = useRouter();
  const roomCode = params.roomCode as string;
  
  const { identity, isLoading: identityLoading } = useAnonymousIdentity();
  const { room, participants, isRoomValid, isRoomFull, isLoading: roomLoading } = useRoom(roomCode);
  const { joinRoom, isJoining, error: joinError } = useRoomParticipation();
  
  const [isValidCode, setIsValidCode] = useState(true);

  // Validate room code format
  useEffect(() => {
    if (roomCode && !isValidRoomCode(roomCode)) {
      setIsValidCode(false);
    }
  }, [roomCode]);

  const handleJoinRoom = async () => {
    if (!room || !identity) return;

    try {
      await joinRoom(roomCode);
      router.push(`/room/${roomCode}`);
      toast.success('Joined room successfully!');
    } catch (err) {
      toast.error('Failed to join room');
    }
  };

  const handleCreateNewRoom = () => {
    router.push('/create-room');
  };

  const handleGoHome = () => {
    router.push('/');
  };

  // Loading state
  if (identityLoading || roomLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-veil-purple/5 flex items-center justify-center">
        <div className="flex items-center gap-2 text-muted-foreground">
          <Loader2 className="h-5 w-5 animate-spin" />
          <span>Loading room information...</span>
        </div>
      </div>
    );
  }

  // Invalid room code format
  if (!isValidCode) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-veil-purple/5 flex items-center justify-center">
        <div className="container mx-auto px-4 max-w-md">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Card className="border-destructive/50 bg-card/50 backdrop-blur-sm">
              <CardHeader className="text-center">
                <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
                <CardTitle className="text-destructive">Invalid Room Code</CardTitle>
                <CardDescription>
                  The room code format is invalid. Room codes should be in the format: word-word-1234
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button onClick={handleCreateNewRoom} className="w-full">
                  Create New Room
                </Button>
                <Button onClick={handleGoHome} variant="outline" className="w-full">
                  Go Home
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    );
  }

  // Room not found
  if (!room) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-veil-purple/5 flex items-center justify-center">
        <div className="container mx-auto px-4 max-w-md">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Card className="border-destructive/50 bg-card/50 backdrop-blur-sm">
              <CardHeader className="text-center">
                <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
                <CardTitle className="text-destructive">Room Not Found</CardTitle>
                <CardDescription>
                  The room "{roomCode}" doesn't exist or has expired.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button onClick={handleCreateNewRoom} className="w-full">
                  <UserPlus className="mr-2 h-4 w-4" />
                  Create Similar Room
                </Button>
                <Button onClick={handleGoHome} variant="outline" className="w-full">
                  <Home className="mr-2 h-4 w-4" />
                  Go Home
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    );
  }

  // Room is full
  if (isRoomFull) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-veil-purple/5 flex items-center justify-center">
        <div className="container mx-auto px-4 max-w-md">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Card className="border-orange-500/50 bg-card/50 backdrop-blur-sm">
              <CardHeader className="text-center">
                <Users className="h-12 w-12 text-orange-500 mx-auto mb-4" />
                <CardTitle className="text-orange-500">Room is Full</CardTitle>
                <CardDescription>
                  This room has reached its maximum capacity of {room.maxParticipants} participants.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button onClick={() => window.location.reload()} variant="outline" className="w-full">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Check Again
                </Button>
                <Button onClick={handleCreateNewRoom} className="w-full">
                  <UserPlus className="mr-2 h-4 w-4" />
                  Create New Room
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    );
  }

  // Room is not active
  if (!isRoomValid) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-veil-purple/5 flex items-center justify-center">
        <div className="container mx-auto px-4 max-w-md">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Card className="border-destructive/50 bg-card/50 backdrop-blur-sm">
              <CardHeader className="text-center">
                <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
                <CardTitle className="text-destructive">Room Ended</CardTitle>
                <CardDescription>
                  This room has ended or expired.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button onClick={handleCreateNewRoom} className="w-full">
                  <UserPlus className="mr-2 h-4 w-4" />
                  Create Similar Room
                </Button>
                <Button onClick={handleGoHome} variant="outline" className="w-full">
                  <Home className="mr-2 h-4 w-4" />
                  Go Home
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    );
  }

  // Show room preview and join option
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* App Branding & Page Header */}
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* VeilMeet Logo & Brand */}
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
              <Shield className="h-6 w-6 text-white" />
            </div>
            <div className="flex flex-col items-start">
              <span className="text-lg font-semibold text-foreground">VeilMeet</span>
              <span className="text-sm text-muted-foreground">Anonymous Discussions</span>
            </div>
          </div>

          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent mb-4">
            Join Anonymous Room
          </h1>
          <p className="text-muted-foreground text-lg">
            You've been invited to join an anonymous discussion
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{room.topic || 'Anonymous Discussion'}</span>
                <Badge variant={room.status === 'active' ? 'default' : 'secondary'}>
                  {room.status}
                </Badge>
              </CardTitle>
              <CardDescription>
                Room Code: <span className="font-mono">{roomCode}</span>
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Room Info */}
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2 text-sm">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span>{room.currentParticipants}/{room.maxParticipants} participants</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>Created {formatRelativeTime(room.createdAt)}</span>
                </div>
              </div>

              {/* Features */}
              <div className="flex flex-wrap gap-2">
                {room.allowVoice && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Mic className="h-3 w-3" />
                    Voice Chat
                  </Badge>
                )}
                {room.allowVideo && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Video className="h-3 w-3" />
                    Video Chat
                  </Badge>
                )}
                <Badge variant="outline" className="flex items-center gap-1">
                  <Shield className="h-3 w-3" />
                  AI Moderated
                </Badge>
              </div>

              {/* Current Participants Preview */}
              {participants.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-muted-foreground">Current Participants</h4>
                  <div className="flex flex-wrap gap-2">
                    {participants.slice(0, 6).map((participant) => (
                      <div
                        key={participant.sessionId}
                        className="flex items-center gap-2 bg-background/50 rounded-full px-3 py-1 text-sm"
                      >
                        <img
                          src={generateAvatarUrl(participant.avatarSeed)}
                          alt={participant.anonymousAlias}
                          className="w-5 h-5 rounded-full"
                        />
                        <span>{participant.anonymousAlias}</span>
                      </div>
                    ))}
                    {participants.length > 6 && (
                      <div className="flex items-center justify-center bg-background/50 rounded-full px-3 py-1 text-sm text-muted-foreground">
                        +{participants.length - 6} more
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Your Identity Preview */}
              {identity && (
                <div className="bg-background/50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-muted-foreground mb-2">You'll join as:</h4>
                  <div className="flex items-center gap-3">
                    <img
                      src={generateAvatarUrl(identity.avatarSeed)}
                      alt={identity.anonymousAlias}
                      className="w-8 h-8 rounded-full"
                    />
                    <div>
                      <div className="font-medium">{identity.anonymousAlias}</div>
                      <div className="text-sm text-muted-foreground">Anonymous participant</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Join Button */}
              <Button
                onClick={handleJoinRoom}
                disabled={isJoining}
                size="lg"
                className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-primary-foreground py-6 text-lg font-semibold shadow-lg"
              >
                {isJoining ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Joining Room...
                  </>
                ) : (
                  <>
                    <UserPlus className="mr-2 h-5 w-5" />
                    Join Anonymous Discussion
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </>
                )}
              </Button>

              {joinError && (
                <div className="text-destructive text-sm bg-destructive/10 p-3 rounded-md">
                  {joinError}
                </div>
              )}

              {/* Alternative Actions */}
              <div className="flex gap-2">
                <Button onClick={handleCreateNewRoom} variant="outline" className="flex-1">
                  Create New Room
                </Button>
                <Button onClick={handleGoHome} variant="ghost" className="flex-1">
                  Go Home
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
