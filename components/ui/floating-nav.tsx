"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "motion/react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  <PERSON>, 
  ArrowUp, 
  Menu, 
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Lock
} from "lucide-react";

const navItems = [
  { name: "Features", href: "#features" },
  { name: "How It Works", href: "#how-it-works" },
  { name: "Use Cases", href: "#use-cases" },
  { name: "AI Technology", href: "#ai-capabilities" },
  { name: "Security", href: "#privacy-security" },
  { name: "Testimonials", href: "#testimonials" }
];

export function FloatingNav() {
  const [isVisible, setIsVisible] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener("scroll", toggleVisibility);
    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <>
      {/* Main Floating Navigation */}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 100 }}
            transition={{ duration: 0.3 }}
            className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50"
          >
            <div className="bg-card/80 backdrop-blur-lg border border-border/50 rounded-xl sm:rounded-2xl px-3 sm:px-4 lg:px-6 py-2 sm:py-3 shadow-2xl max-w-[95vw]">
              <div className="flex items-center gap-2 sm:gap-3 lg:gap-4">
                {/* Logo */}
                <div className="flex items-center gap-1 sm:gap-2">
                  <div className="w-5 h-5 sm:w-6 sm:h-6 bg-gradient-to-br from-purple-500 to-blue-600 rounded-md flex items-center justify-center">
                    <Shield className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
                  </div>
                  <span className="font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent text-xs sm:text-sm">VeilMeet</span>
                </div>

                {/* Desktop Navigation */}
                <div className="hidden lg:flex items-center gap-1">
                  {navItems.map((item) => (
                    <a
                      key={item.name}
                      href={item.href}
                      className="px-2 xl:px-3 py-1.5 text-xs xl:text-sm text-muted-foreground hover:text-primary transition-colors rounded-lg hover:bg-primary/10 whitespace-nowrap"
                    >
                      {item.name}
                    </a>
                  ))}
                </div>

                {/* Mobile Menu Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  className="lg:hidden p-1 sm:p-2"
                  onClick={() => setIsMenuOpen(!isMenuOpen)}
                >
                  {isMenuOpen ? <X className="w-3 h-3 sm:w-4 sm:h-4" /> : <Menu className="w-3 h-3 sm:w-4 sm:h-4" />}
                </Button>

                {/* CTA Button */}
                <Button
                  size="sm"
                  className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-primary-foreground text-xs px-2 sm:px-3 lg:px-4 py-1 sm:py-2 min-h-[32px] sm:min-h-[36px]"
                >
                  <Sparkles className="w-2 h-2 sm:w-3 sm:h-3 mr-1" />
                  <span className="hidden sm:inline">Create Room</span>
                  <span className="sm:hidden">Create</span>
                </Button>

                {/* Scroll to Top */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={scrollToTop}
                  className="hover:bg-primary/10 p-1 sm:p-2"
                >
                  <ArrowUp className="w-3 h-3 sm:w-4 sm:h-4" />
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMenuOpen && isVisible && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="fixed bottom-20 sm:bottom-24 left-1/2 transform -translate-x-1/2 z-50 lg:hidden"
          >
            <div className="bg-card/90 backdrop-blur-lg border border-border/50 rounded-xl sm:rounded-2xl p-3 sm:p-4 shadow-2xl min-w-[180px] sm:min-w-[200px] max-w-[90vw]">
              <div className="space-y-1 sm:space-y-2">
                {navItems.map((item) => (
                  <a
                    key={item.name}
                    href={item.href}
                    onClick={() => setIsMenuOpen(false)}
                    className="block px-2 sm:px-3 py-2 text-xs sm:text-sm text-muted-foreground hover:text-primary transition-colors rounded-lg hover:bg-primary/10"
                  >
                    {item.name}
                  </a>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Trust Indicators - Fixed Position */}
      <motion.div
        initial={{ opacity: 0, x: -100 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.8, delay: 2 }}
        className="fixed left-2 lg:left-4 xl:left-6 top-1/2 transform -translate-y-1/2 z-40 hidden xl:block max-w-[200px]"
        style={{ maxWidth: 'calc(100vw - 1rem)' }}
      >
        <div className="bg-card/80 backdrop-blur-lg border border-border/50 rounded-xl lg:rounded-2xl p-2 lg:p-3 xl:p-4 space-y-2 lg:space-y-3 shadow-lg">
          <div className="flex items-center gap-1 lg:gap-2 text-xs">
            <div className="w-1.5 h-1.5 lg:w-2 lg:h-2 bg-green-500 rounded-full animate-pulse flex-shrink-0" />
            <span className="text-muted-foreground text-xs lg:text-xs xl:text-sm whitespace-nowrap">10,000+ Sessions</span>
          </div>
          <div className="flex items-center gap-1 lg:gap-2 text-xs">
            <Lock className="w-2 h-2 lg:w-3 lg:h-3 text-blue-500 flex-shrink-0" />
            <span className="text-muted-foreground text-xs lg:text-xs xl:text-sm whitespace-nowrap">100% Anonymous</span>
          </div>
          <div className="flex items-center gap-1 lg:gap-2 text-xs">
            <Users className="w-2 h-2 lg:w-3 lg:h-3 text-primary flex-shrink-0" />
            <span className="text-muted-foreground text-xs lg:text-xs xl:text-sm whitespace-nowrap">AI-Enhanced</span>
          </div>
        </div>
      </motion.div>

      {/* Quick CTA - Fixed Position */}
      <motion.div
        initial={{ opacity: 0, x: 100 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.8, delay: 2.5 }}
        className="fixed right-2 lg:right-4 xl:right-6 top-1/2 transform -translate-y-1/2 z-40 hidden xl:block max-w-[150px]"
        style={{ maxWidth: 'calc(100vw - 1rem)' }}
      >
        <div className="bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl lg:rounded-2xl p-2 lg:p-3 xl:p-4 border border-primary/30 shadow-lg">
          <div className="text-center">
            <Badge variant="secondary" className="mb-1 lg:mb-2 text-xs whitespace-nowrap">
              <Sparkles className="w-2 h-2 mr-1 flex-shrink-0" />
              Try Now
            </Badge>
            <Button
              size="sm"
              className="bg-primary text-primary-foreground hover:bg-primary/90 text-xs px-2 lg:px-3 py-1 lg:py-2 w-full min-h-[28px] lg:min-h-[32px] whitespace-nowrap"
            >
              Create Room
            </Button>
          </div>
        </div>
      </motion.div>
    </>
  );
}
