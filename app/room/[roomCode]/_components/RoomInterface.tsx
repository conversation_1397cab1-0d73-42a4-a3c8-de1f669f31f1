'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from 'next-themes';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users, Reply, Volume2, Share2, Shield, Hash, Wifi, Headphones, Sun, Moon, LogOut } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useViewport } from '@/hooks/useViewport';
import { ChatInterface } from '@/components/room/ChatInterface';
import { MessageInput } from '@/components/room/MessageInput';
import { ParticipantsList } from '@/components/room/ParticipantsList';
import { TypingIndicator } from '@/components/room/TypingIndicator';
import { ModernVoiceControls } from './ModernVoiceControls';
import { MobileRoomLayout } from './MobileRoomLayout';
// VoiceRoomControls removed - functionality integrated into ModernVoiceControls
import { useVoiceRoom } from '@/hooks/useVoiceRoom';
import type { Room, ParticipantSummary, Message, TypingParticipant } from '@/lib/types';
import type { Id } from '@/convex/_generated/dataModel';

interface RoomInterfaceProps {
  room: Room;
  participants: ParticipantSummary[];
  messages: Message[];
  typingIndicators: TypingParticipant[];
  currentParticipant?: ParticipantSummary;
  isCreator?: boolean;

  onReaction: (messageId: string, emoji: string) => Promise<void>;
  onReply: (messageId: string, content: string, author: string) => void;
  onEdit: (messageId: string, newContent: string) => Promise<void>;
  onDelete: (messageId: string) => Promise<void>;
  onMentionClick: (participantSessionId: string) => void;
  onLeave: () => void;
  onShare: () => void;
  onEndRoom?: () => void;
  replyingTo?: {
    messageId: Id<'messages'>;
    content: string;
    author: string;
  } | null;
  onCancelReply?: () => void;
  voiceRoom?: any; // Voice functionality removed - keeping interface for future implementation
}

// Removed MediaControlsState - now using voice controls directly

export function RoomInterface({
  room,
  participants,
  messages,
  typingIndicators,
  currentParticipant,
  isCreator = false,
  onReaction,
  onReply,
  onEdit,
  onDelete,
  onMentionClick,
  onLeave,
  onShare,
  onEndRoom,
  replyingTo,
  onCancelReply,
  voiceRoom
}: RoomInterfaceProps) {
  const { isMobile, isTablet } = useViewport();
  const isCompact = isMobile || isTablet;
  const { theme, setTheme } = useTheme();

  // Voice room functionality
  const { participants: voiceParticipants } = useVoiceRoom(room._id);

  const [showParticipants, setShowParticipants] = useState(!isCompact);


  // Voice functionality removed - keeping placeholder for future implementation
  const handleVoiceSettingsChange = useCallback((settings: any) => {
    // Placeholder for future voice implementation
  }, []);

  const handleVoicePresetChange = useCallback((presetName: string) => {
    // Placeholder for future voice implementation
  }, []);



  const handleReaction = useCallback(async (messageId: string, emoji: string) => {
    try {
      await onReaction(messageId, emoji);
    } catch (error) {
      // Handle error silently or show user-friendly message
    }
  }, [onReaction]);

  // Computed values
  const activeParticipants = useMemo(() =>
    participants.filter(p => p.isActive),
    [participants]
  );

  const typingParticipants = useMemo(() =>
    typingIndicators.map(t => ({
      sessionId: t.sessionId,
      anonymousAlias: participants.find(p => p.sessionId === t.sessionId)?.anonymousAlias || 'Anonymous'
    })),
    [typingIndicators, participants]
  );

  // Transform messages for ChatInterface compatibility
  const transformedMessages = useMemo(() =>
    messages.map(msg => ({
      id: msg._id,
      roomId: msg.roomId,
      participantSessionId: msg.participantSessionId,
      anonymousAlias: msg.anonymousAlias,
      content: msg.content,
      messageType: msg.messageType,
      timestamp: msg.timestamp,
      isEdited: msg.isEdited,
      editedAt: msg.editedAt,
      reactions: msg.reactions,
      replyToMessageId: msg.replyToMessageId,
      threadRootId: msg.threadRootId,
      threadDepth: msg.threadDepth,
      threadReplyCount: msg.threadReplyCount,
      mentions: msg.mentions,
      hasMentions: msg.hasMentions,
      engagementMetrics: msg.engagementMetrics
    })),
    [messages]
  );

  // Transform participants for ChatInterface compatibility
  const transformedParticipants = useMemo(() =>
    participants.map(p => ({
      sessionId: p.sessionId,
      anonymousAlias: p.anonymousAlias,
      avatarSeed: p.avatarSeed,
      joinedAt: p.joinedAt,
      messageCount: p.messageCount,
      isActive: p.isActive,
      isMuted: p.isMuted
    })),
    [participants]
  );

  const transformedCurrentParticipant = useMemo(() =>
    currentParticipant ? {
      sessionId: currentParticipant.sessionId,
      anonymousAlias: currentParticipant.anonymousAlias,
      avatarSeed: currentParticipant.avatarSeed,
      joinedAt: currentParticipant.joinedAt,
      messageCount: currentParticipant.messageCount,
      isActive: currentParticipant.isActive,
      isMuted: currentParticipant.isMuted
    } : undefined,
    [currentParticipant]
  );

  // Use mobile layout for compact screens
  if (isCompact) {
    return (
      <MobileRoomLayout
        room={room}
        participants={participants}
        messages={messages}
        typingIndicators={typingIndicators}
        currentParticipant={currentParticipant}
        isCreator={isCreator}

        onReaction={onReaction}
        onReply={onReply}
        onEdit={onEdit}
        onDelete={onDelete}
        onMentionClick={onMentionClick}
        onLeave={onLeave}
        onShare={onShare}
        onEndRoom={onEndRoom}
        replyingTo={replyingTo}
        onCancelReply={onCancelReply}

      />
    );
  }

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Room Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm sticky top-0 z-40">
        <div className="flex items-center justify-between px-3 sm:px-4 py-2 sm:py-2.5">
          {/* Room Info */}
          <div className="flex items-center gap-2 sm:gap-2.5 min-w-0 flex-1">
            <div className="flex items-center gap-2 sm:gap-2.5">
              {/* Icon + Name Group */}
              <div className="flex items-center gap-2 sm:gap-2.5">
                <div className="bg-gradient-to-br from-purple-500 via-purple-600 to-blue-600 rounded-lg sm:rounded-xl p-1.5 sm:p-2 shadow-lg">
                  <Shield className="h-4 w-4 sm:h-5 sm:w-5 text-white drop-shadow-sm" />
                </div>
                <span className="text-base sm:text-lg font-bold text-foreground bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                  VeilMeet
                </span>
              </div>

              {/* Vertical Line */}
              <div className="w-px h-8 sm:h-10 bg-border" />

              {/* Group Details */}
              <div className="min-w-0">
                <h1 className="font-semibold text-foreground truncate text-xs sm:text-sm mb-0.5">
                  {room.topic || 'Anonymous Discussion'}
                </h1>
                <div className="flex items-center text-xs text-muted-foreground">
                  <Hash className="h-3 w-3" />
                  <span className="font-mono">{room.roomCode}</span>
                  <Users className="h-3 w-3 ml-2 sm:ml-3" />
                  <span>{participants.length}/{room.maxParticipants}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Header Actions */}
          <div className="flex items-center gap-1 sm:gap-1.5">
            {/* Participants Toggle for Desktop */}
            {!isCompact && (
              <Button
                variant="outline"
                size="icon"
                onClick={() => setShowParticipants(!showParticipants)}
                className={cn(
                  "transition-all duration-200",
                  showParticipants && "bg-accent"
                )}
              >
                <Users className="h-4 w-4" />
              </Button>
            )}

            {/* Voice Available Indicator */}
            {room.allowVoice && (
              <Badge variant="outline" className="hidden sm:flex items-center gap-1 text-xs px-2 py-0.5 border-green-200 text-green-700 dark:border-green-800 dark:text-green-300">
                <Volume2 className="h-3 w-3" />
                Voice
              </Badge>
            )}

            {/* Theme Toggle */}
            <Button
              variant="outline"
              size="icon"
              onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
              className="h-8 w-8 sm:h-9 sm:w-9"
            >
              {theme === 'dark' ? (
                <Sun className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
              ) : (
                <Moon className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
              )}
            </Button>

            <Button
              variant="outline"
              size="icon"
              onClick={onShare}
              className="h-8 w-8 sm:h-9 sm:w-9"
            >
              <Share2 className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
            </Button>

            <Button
              variant="outline"
              size="icon"
              onClick={onLeave}
              className="h-8 w-8 sm:h-9 sm:w-9"
            >
              <LogOut className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
            </Button>
          </div>
        </div>

        {/* Reply indicator */}
        {replyingTo && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="px-3 sm:px-4 py-2 bg-muted/50 border-t border-border overflow-hidden"
          >
            <div className="flex items-center justify-between gap-2">
              <div className="flex items-center gap-2 text-sm min-w-0 flex-1">
                <Reply className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                <span className="text-muted-foreground">Replying to</span>
                <span className="font-medium truncate">{replyingTo.author}</span>
                <span className="text-muted-foreground truncate max-w-[120px] sm:max-w-xs">
                  {replyingTo.content}
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onCancelReply}
                className="h-6 w-6 p-0 flex-shrink-0"
              >
                ×
              </Button>
            </div>
          </motion.div>
        )}
      </header>

      {/* Main Content */}
      <div className="flex-1 flex min-h-0">
        {/* Left Sidebar - AI Assistant */}
        <aside className="hidden xl:flex flex-col w-80 border-r border-border bg-card/20 backdrop-blur-sm">
          <div className="flex-1 flex flex-col px-3 py-3 min-h-0">
            {/* AI Assistant Section */}
            <div className="flex-1 flex flex-col min-h-0 bg-card/50 rounded-lg border border-border">
              {/* AI Header */}
              <div className="flex-shrink-0 px-3 py-3 border-b border-border">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                  <h3 className="font-semibold text-sm">AI Assistant</h3>
                  {room.allowVoice && (
                    <Badge variant="outline" className="text-xs ml-auto">
                      <Volume2 className="h-3 w-3 mr-1" />
                      Voice Ready
                    </Badge>
                  )}
                </div>
              </div>

              {/* AI Content */}
              <div className="flex-1 flex flex-col min-h-0 px-3 py-3">
                <div className="flex-1 overflow-y-auto space-y-3 scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent">
                  {/* Voice Status Info */}
                  {room.allowVoice && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="px-3 py-3 rounded-lg bg-green-50 dark:bg-green-950/30 border border-border"
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <Headphones className="h-4 w-4 text-green-600 dark:text-green-400" />
                        <span className="text-sm font-medium text-green-900 dark:text-green-100">
                          Voice Features Available
                        </span>
                      </div>
                      <p className="text-xs text-green-700 dark:text-green-300">
                        Use the media controls below to join voice chat and access voice modulation features.
                      </p>
                    </motion.div>
                  )}

                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="px-3 py-3 rounded-lg bg-blue-50 dark:bg-blue-950/30 border border-border"
                  >
                    <p className="text-sm text-blue-900 dark:text-blue-100">
                      AI-powered insights and moderation coming soon...
                    </p>
                  </motion.div>

                  {/* AI Features Preview */}
                  <div className="space-y-3">
                    <div className="text-xs font-medium text-muted-foreground">Upcoming Features</div>
                    <div className="space-y-2">
                      {[
                        'Real-time sentiment analysis',
                        'Smart conversation summaries',
                        'Automated moderation',
                        'Topic suggestions',
                        'Language translation'
                      ].map((feature, i) => (
                        <motion.div
                          key={feature}
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: i * 0.1 + 0.2 }}
                          className="px-2 py-2 rounded bg-muted/50 text-xs text-muted-foreground border border-border hover:bg-muted/70 transition-colors duration-200"
                        >
                          {feature}
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </aside>

        {/* Chat Area */}
        <main className="flex-1 flex flex-col min-h-0 bg-background/50">
          {/* Modern Voice Controls */}
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="px-2 sm:px-3 py-2 sm:py-3 border-b border-border bg-card/50 backdrop-blur-sm"
          >
            <ModernVoiceControls
              roomId={room._id}
              participantCount={activeParticipants.length}
              disabled={false}
              enableKeyboardShortcuts={true}
              className="transition-all duration-200"
            />
          </motion.div>

          {/* Messages */}
          <div className="flex-1 min-h-0 relative">
            <div className="absolute inset-0">
              <ChatInterface
                messages={transformedMessages}
                currentParticipant={transformedCurrentParticipant}
                participants={transformedParticipants}
                onReaction={handleReaction}
                onReply={onReply}
                onEdit={onEdit}
                onDelete={onDelete}
                onMentionClick={onMentionClick}
                replyingTo={replyingTo}
              />
            </div>
          </div>

          {/* Typing Indicator */}
          <AnimatePresence>
            {typingParticipants.length > 0 && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="overflow-hidden"
              >
                <TypingIndicator
                  typingParticipants={typingParticipants}
                  currentSessionId={currentParticipant?.sessionId}
                  className="border-t border-border bg-card/30 backdrop-blur-sm"
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Message Input */}
          <div className="border-t border-border bg-card/50 backdrop-blur-sm">
            <div className="px-2 sm:px-0">
              <MessageInput
                roomId={room._id}
                currentParticipant={currentParticipant}
                participants={participants}
                onMessageSent={() => { }}
                replyingTo={replyingTo}
                onCancelReply={onCancelReply}
              />
            </div>
          </div>
        </main>

        {/* Participants Sidebar */}
        <AnimatePresence mode="wait">
          {showParticipants && (
            <motion.aside
              initial={{ width: 0, opacity: 0, x: 20 }}
              animate={{
                width: isCompact ? '100%' : 320,
                opacity: 1,
                x: 0
              }}
              exit={{
                width: 0,
                opacity: 0,
                x: 20,
                transition: { duration: 0.2 }
              }}
              transition={{
                type: "spring",
                stiffness: 300,
                damping: 30,
                duration: 0.3
              }}
              className={cn(
                "border-l border-border bg-card/40 backdrop-blur-md overflow-hidden shadow-lg",
                isCompact && "absolute inset-0 z-50 bg-background/95 backdrop-blur-lg"
              )}
            >
              <div className="h-full flex flex-col">
                {/* Participants Header */}
                <motion.div
                  initial={{ y: -10, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.1 }}
                  className="px-3 py-3 border-b border-border bg-card/50"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold text-sm">
                        Participants
                      </h3>
                      <Badge variant="secondary" className="text-xs">
                        {participants.length}
                      </Badge>
                    </div>
                    {isCompact && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowParticipants(false)}
                        className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive transition-colors duration-200"
                      >
                        ×
                      </Button>
                    )}
                  </div>

                  {/* Active participants indicator */}
                  <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                    <span>{activeParticipants.length} active</span>
                    {room.allowVoice && voiceParticipants.length > 0 && (
                      <>
                        <div className="w-px h-3 bg-border mx-1" />
                        <Volume2 className="h-3 w-3" />
                        <span>{voiceParticipants.length} in voice</span>
                      </>
                    )}
                  </div>
                </motion.div>

                {/* Participants List */}
                <motion.div
                  initial={{ y: 10, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.2 }}
                  className="flex-1 overflow-hidden p-2.5"
                >
                  <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent">
                    <ParticipantsList
                      participants={participants}
                      voiceParticipants={voiceParticipants}
                      currentSessionId={currentParticipant?.sessionId}
                      showDetails={!isCompact}
                    />
                  </div>
                </motion.div>
              </div>
            </motion.aside>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
