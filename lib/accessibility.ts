/**
 * Accessibility utilities for VeilMeet
 * Helps ensure WCAG 2.1 AA compliance
 */

// Generate unique IDs for ARIA relationships
export const generateId = (prefix: string = 'veil'): string => {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
};

// Check if user prefers reduced motion
export const prefersReducedMotion = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

// Check if user prefers high contrast
export const prefersHighContrast = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-contrast: high)').matches;
};

// Keyboard navigation helpers
export const KEYBOARD_KEYS = {
  ENTER: 'Enter',
  SPACE: ' ',
  ESCAPE: 'Escape',
  ARROW_UP: 'ArrowUp',
  ARROW_DOWN: 'ArrowDown',
  ARROW_LEFT: 'ArrowLeft',
  ARROW_RIGHT: 'ArrowRight',
  TAB: 'Tab',
  HOME: 'Home',
  END: 'End',
} as const;

export type KeyboardKey = typeof KEYBOARD_KEYS[keyof typeof KEYBOARD_KEYS];

// Handle keyboard activation (Enter or Space)
export const handleKeyboardActivation = (
  event: React.KeyboardEvent,
  callback: () => void
): void => {
  if (event.key === KEYBOARD_KEYS.ENTER || event.key === KEYBOARD_KEYS.SPACE) {
    event.preventDefault();
    callback();
  }
};

// Focus management utilities
export const focusElement = (selector: string): void => {
  const element = document.querySelector(selector) as HTMLElement;
  if (element) {
    element.focus();
  }
};

export const trapFocus = (containerElement: HTMLElement): void => {
  const focusableElements = containerElement.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );
  
  const firstElement = focusableElements[0] as HTMLElement;
  const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

  const handleTabKey = (e: KeyboardEvent) => {
    if (e.key === KEYBOARD_KEYS.TAB) {
      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement.focus();
          e.preventDefault();
        }
      }
    }
  };

  containerElement.addEventListener('keydown', handleTabKey);
  
  // Return cleanup function
  return () => {
    containerElement.removeEventListener('keydown', handleTabKey);
  };
};

// Screen reader utilities
export const announceToScreenReader = (message: string): void => {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', 'polite');
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;
  
  document.body.appendChild(announcement);
  
  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
};

// Color contrast utilities
export const getContrastRatio = (color1: string, color2: string): number => {
  // This is a simplified version - in production, you'd want a more robust implementation
  // For now, return a placeholder that assumes good contrast
  return 4.5; // WCAG AA minimum
};

// ARIA attributes helpers
export const getAriaProps = (options: {
  label?: string;
  labelledBy?: string;
  describedBy?: string;
  expanded?: boolean;
  selected?: boolean;
  disabled?: boolean;
  required?: boolean;
  invalid?: boolean;
  live?: 'off' | 'polite' | 'assertive';
  atomic?: boolean;
  hidden?: boolean;
}) => {
  const ariaProps: Record<string, any> = {};
  
  if (options.label) ariaProps['aria-label'] = options.label;
  if (options.labelledBy) ariaProps['aria-labelledby'] = options.labelledBy;
  if (options.describedBy) ariaProps['aria-describedby'] = options.describedBy;
  if (options.expanded !== undefined) ariaProps['aria-expanded'] = options.expanded;
  if (options.selected !== undefined) ariaProps['aria-selected'] = options.selected;
  if (options.disabled !== undefined) ariaProps['aria-disabled'] = options.disabled;
  if (options.required !== undefined) ariaProps['aria-required'] = options.required;
  if (options.invalid !== undefined) ariaProps['aria-invalid'] = options.invalid;
  if (options.live) ariaProps['aria-live'] = options.live;
  if (options.atomic !== undefined) ariaProps['aria-atomic'] = options.atomic;
  if (options.hidden !== undefined) ariaProps['aria-hidden'] = options.hidden;
  
  return ariaProps;
};

// Skip link component props
export const getSkipLinkProps = (targetId: string, label: string = 'Skip to main content') => ({
  href: `#${targetId}`,
  className: 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary focus:text-primary-foreground focus:rounded-md focus:shadow-lg',
  children: label,
});

// Landmark roles
export const LANDMARK_ROLES = {
  BANNER: 'banner',
  NAVIGATION: 'navigation', 
  MAIN: 'main',
  COMPLEMENTARY: 'complementary',
  CONTENTINFO: 'contentinfo',
  SEARCH: 'search',
  FORM: 'form',
  REGION: 'region',
} as const;

// Common ARIA patterns
export const ARIA_PATTERNS = {
  BUTTON: {
    role: 'button',
    tabIndex: 0,
  },
  LINK: {
    role: 'link',
    tabIndex: 0,
  },
  MENU: {
    role: 'menu',
  },
  MENUITEM: {
    role: 'menuitem',
    tabIndex: -1,
  },
  TAB: {
    role: 'tab',
    tabIndex: -1,
  },
  TABPANEL: {
    role: 'tabpanel',
    tabIndex: 0,
  },
  DIALOG: {
    role: 'dialog',
    'aria-modal': true,
  },
} as const;
