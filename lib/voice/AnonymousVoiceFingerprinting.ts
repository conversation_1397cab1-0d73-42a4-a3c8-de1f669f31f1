/**
 * VeilMind Anonymous Voice Fingerprinting System
 * Generates unique voice signatures per session while ensuring anonymity
 */

import { VoiceModulationSettings } from './VoiceModulationEngine';

export interface VoiceFingerprint {
  sessionId: string;
  roomId: string;
  voiceId: string; // Unique identifier for this voice in this session
  basePitch: number; // Base pitch shift for this session
  toneSignature: {
    warmth: number;
    breathiness: number;
    resonance: number;
    formantShift: number;
  };
  createdAt: number;
  expiresAt: number;
}

export interface VoiceCollisionMap {
  [roomId: string]: Set<string>; // Track used voice IDs per room
}

export class AnonymousVoiceFingerprinting {
  private static instance: AnonymousVoiceFingerprinting;
  private voiceFingerprints: Map<string, VoiceFingerprint> = new Map();
  private collisionMap: VoiceCollisionMap = {};
  private readonly FINGERPRINT_DURATION = 4 * 60 * 60 * 1000; // 4 hours
  private readonly CLEANUP_INTERVAL = 30 * 60 * 1000; // 30 minutes
  
  private constructor() {
    // Start cleanup timer
    setInterval(() => this.cleanupExpiredFingerprints(), this.CLEANUP_INTERVAL);
  }
  
  static getInstance(): AnonymousVoiceFingerprinting {
    if (!AnonymousVoiceFingerprinting.instance) {
      AnonymousVoiceFingerprinting.instance = new AnonymousVoiceFingerprinting();
    }
    return AnonymousVoiceFingerprinting.instance;
  }
  
  /**
   * Generate a unique voice fingerprint for a session
   */
  generateVoiceFingerprint(sessionId: string, roomId: string): VoiceFingerprint {
    // Check if we already have a fingerprint for this session
    const existingKey = `${sessionId}-${roomId}`;
    const existing = this.voiceFingerprints.get(existingKey);
    
    if (existing && existing.expiresAt > Date.now()) {
      return existing;
    }
    
    // Generate new voice fingerprint
    const voiceId = this.generateUniqueVoiceId(roomId);
    const now = Date.now();
    
    const fingerprint: VoiceFingerprint = {
      sessionId,
      roomId,
      voiceId,
      basePitch: this.generateBasePitch(),
      toneSignature: this.generateToneSignature(),
      createdAt: now,
      expiresAt: now + this.FINGERPRINT_DURATION
    };
    
    // Store fingerprint
    this.voiceFingerprints.set(existingKey, fingerprint);
    
    // Track voice ID for collision avoidance
    if (!this.collisionMap[roomId]) {
      this.collisionMap[roomId] = new Set();
    }
    this.collisionMap[roomId].add(voiceId);
    
    console.log(`Generated voice fingerprint for session ${sessionId} in room ${roomId}`);
    return fingerprint;
  }
  
  /**
   * Get voice modulation settings from fingerprint
   */
  getVoiceSettings(fingerprint: VoiceFingerprint): VoiceModulationSettings {
    return {
      pitchShift: fingerprint.basePitch,
      formantShift: fingerprint.toneSignature.formantShift,
      warmth: fingerprint.toneSignature.warmth,
      breathiness: fingerprint.toneSignature.breathiness,
      resonance: fingerprint.toneSignature.resonance,
      roboticFilter: 0, // Keep robotic filter as user choice
      noiseGate: -40 // Standard noise gate
    };
  }
  
  /**
   * Generate unique voice ID that doesn't collide with others in the room
   */
  private generateUniqueVoiceId(roomId: string): string {
    const usedIds = this.collisionMap[roomId] || new Set();
    let voiceId: string;
    let attempts = 0;
    const maxAttempts = 100;
    
    do {
      voiceId = this.generateVoiceId();
      attempts++;
      
      if (attempts > maxAttempts) {
        // Fallback: use timestamp to ensure uniqueness
        voiceId = `voice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        break;
      }
    } while (usedIds.has(voiceId));
    
    return voiceId;
  }
  
  /**
   * Generate a voice ID using phonetic-like patterns
   */
  private generateVoiceId(): string {
    const consonants = ['b', 'c', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p', 'q', 'r', 's', 't', 'v', 'w', 'x', 'z'];
    const vowels = ['a', 'e', 'i', 'o', 'u'];
    
    let voiceId = '';
    const length = 6 + Math.floor(Math.random() * 3); // 6-8 characters
    
    for (let i = 0; i < length; i++) {
      if (i % 2 === 0) {
        voiceId += consonants[Math.floor(Math.random() * consonants.length)];
      } else {
        voiceId += vowels[Math.floor(Math.random() * vowels.length)];
      }
    }
    
    return voiceId;
  }
  
  /**
   * Generate base pitch shift within natural range
   */
  private generateBasePitch(): number {
    // Generate pitch shift between -8 and +8 semitones
    // Weighted towards smaller changes for more natural sound
    const weights = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1];
    const weightedIndex = this.weightedRandom(weights);
    return (weightedIndex - 9) * 0.9; // Scale to -8 to +8 range
  }
  
  /**
   * Generate tone signature for consistent voice character
   */
  private generateToneSignature() {
    return {
      warmth: (Math.random() - 0.5) * 0.8, // -0.4 to +0.4
      breathiness: Math.random() * 0.3, // 0 to 0.3
      resonance: (Math.random() - 0.5) * 0.6, // -0.3 to +0.3
      formantShift: 0.8 + Math.random() * 0.4 // 0.8 to 1.2
    };
  }
  
  /**
   * Weighted random selection
   */
  private weightedRandom(weights: number[]): number {
    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
    let random = Math.random() * totalWeight;
    
    for (let i = 0; i < weights.length; i++) {
      random -= weights[i];
      if (random <= 0) {
        return i;
      }
    }
    
    return weights.length - 1;
  }
  
  /**
   * Get existing fingerprint for session
   */
  getVoiceFingerprint(sessionId: string, roomId: string): VoiceFingerprint | null {
    const key = `${sessionId}-${roomId}`;
    const fingerprint = this.voiceFingerprints.get(key);
    
    if (fingerprint && fingerprint.expiresAt > Date.now()) {
      return fingerprint;
    }
    
    return null;
  }
  
  /**
   * Rotate voice fingerprint (generate new one)
   */
  rotateVoiceFingerprint(sessionId: string, roomId: string): VoiceFingerprint {
    const key = `${sessionId}-${roomId}`;
    
    // Remove old fingerprint
    const oldFingerprint = this.voiceFingerprints.get(key);
    if (oldFingerprint && this.collisionMap[roomId]) {
      this.collisionMap[roomId].delete(oldFingerprint.voiceId);
    }
    
    this.voiceFingerprints.delete(key);
    
    // Generate new fingerprint
    return this.generateVoiceFingerprint(sessionId, roomId);
  }
  
  /**
   * Clean up expired fingerprints
   */
  private cleanupExpiredFingerprints(): void {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [key, fingerprint] of this.voiceFingerprints.entries()) {
      if (fingerprint.expiresAt <= now) {
        // Remove from collision map
        if (this.collisionMap[fingerprint.roomId]) {
          this.collisionMap[fingerprint.roomId].delete(fingerprint.voiceId);
          
          // Clean up empty room collision maps
          if (this.collisionMap[fingerprint.roomId].size === 0) {
            delete this.collisionMap[fingerprint.roomId];
          }
        }
        
        // Remove fingerprint
        this.voiceFingerprints.delete(key);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`Cleaned up ${cleanedCount} expired voice fingerprints`);
    }
  }
  
  /**
   * Get statistics for monitoring
   */
  getStatistics() {
    const now = Date.now();
    const activeFingerprints = Array.from(this.voiceFingerprints.values())
      .filter(fp => fp.expiresAt > now);
    
    const roomCounts = Object.keys(this.collisionMap).reduce((acc, roomId) => {
      acc[roomId] = this.collisionMap[roomId].size;
      return acc;
    }, {} as Record<string, number>);
    
    return {
      totalFingerprints: this.voiceFingerprints.size,
      activeFingerprints: activeFingerprints.length,
      roomCounts,
      oldestFingerprint: activeFingerprints.length > 0 
        ? Math.min(...activeFingerprints.map(fp => fp.createdAt))
        : null
    };
  }
  
  /**
   * Clear all fingerprints (for testing/cleanup)
   */
  clearAll(): void {
    this.voiceFingerprints.clear();
    this.collisionMap = {};
    console.log('Cleared all voice fingerprints');
  }
}
